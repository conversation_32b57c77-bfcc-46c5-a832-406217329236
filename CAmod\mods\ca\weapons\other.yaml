^FireWeapon:
	ValidTargets: Ground, Water, Trees
	Range: 5c0
	Warhead@1Dam: SpreadDamage
		Spread: 213
		Damage: 30000
		ValidTargets: Ground, Water, Trees
		AffectsParent: false
		Versus:
			None: 90
			Wood: 50
			Tree: 50
			Light: 60
			Heavy: 25
			Concrete: 25
			Brick: 5
		DamageTypes: Prone50Percent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>h, Incendiary
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Structure, Wall
	Warhead@3Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: firetrt1.aud
		ImpactActors: false
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx xxx xxx

FireballLauncher:
	Inherits: ^FireWeapon
	Report: firebl3.aud
	Range: 6c0
	ReloadDelay: 62
	Projectile: BulletCA
		Speed: 250
		TrailImage: fb2
		Image: FB1
		TrailSequences: idle, idle2
		TrailInterval: 1
		TrailDelay: 0
		TrailSpacing: 256
	Warhead@1Dam: SpreadDamage
		Versus:
			Light: 0
			Heavy: 10
			Concrete: 15
			Wood: 24
	Warhead@2Dam: SpreadDamage
		Spread: 512
		Range: 0, 0c512, 2c512
		Falloff: 100, 10, 10
		Damage: 30000
		ValidTargets: Ground, Water, Trees
		Versus:
			Light: 50
			None: 0
			Wood: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary

ChemballLauncher:
	Inherits: FireballLauncher
	ReloadDelay: 70
	Projectile: BulletCA
		TrailImage: cb2
		TrailPalette: temptd
		-TrailSequences:
		Image: cb1
		Palette: temptd
	Warhead@1Dam: SpreadDamage
		Spread: 288
		Versus:
			Heavy: 15
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
	Warhead@2Dam: SpreadDamage
		Versus:
			Light: 70
	Warhead@3Eff: CreateEffect
		Explosions: med_chem
		ExplosionPalette: temptd
		AirThreshold: 256
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
	-Warhead@Flames:

FireballGun:
	Inherits: FireballLauncher
	Range: 4c275
	ReloadDelay: 40
	Projectile: BulletCA
		Speed: 140
		-TrailSpacing:
	Warhead@1Dam: SpreadDamage
		Damage: 10000
		Spread: 288
		Falloff: 100, 50, 25, 12, 6, 3, 0
		Versus:
			None: 100
			Concrete: 10
			Light: 25
			Heavy: 10
			Wood: 50
	Warhead@3Eff: CreateEffect
		Explosions: small_napalm
	-Warhead@2Dam:
	-Warhead@Flames:

Napalmtd:
	ValidTargets: Ground, Water, Trees
	ReloadDelay: 50
	Report: chute1.aud
	Burst: 7
	BurstDelays: 6
	Range: 2c0
	TargetActorCenter: true
	Projectile: GravityBomb
		Image: BOMBLET
		Velocity: 25, 0, -74
		Acceleration: 0, 0, 0
		Shadow: true
	Warhead@1Dam: SpreadDamage
		Spread: 512
		Damage: 1500
		Falloff: 1000, 500, 135, 50, 18, 7, 0
		ValidTargets: Ground, Water
		Versus:
			None: 100
			Wood: 0
			Light: 100
			Heavy: 55
			Concrete: 55
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, FlakVestMitigatedMinor, AirToGround
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
	Warhead@3Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: flamer2.aud
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx xxx xxx

NapalmtdBuilding:
	Inherits: Napalmtd
	-Report:
	-Projectile:
	Projectile: InstantHit
	TargetActorCenter: false
	Warhead@1Dam: SpreadDamage
		Delay: 35
		Versus:
			None: 0
			Wood: 36
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
		DebugOverlayColor: ffaa00
	-Warhead@2Smu:
	-Warhead@3Eff:
	-Warhead@Flames:

NapalmtdBomber:
	Inherits: Napalmtd
	Warhead@1Dam: SpreadDamage
		Versus:
			Wood: 60

Chemspray:
	ReloadDelay: 50
	Range: 3c769
	StartBurstReport: flamer2.aud
	Burst: 5
	BurstDelays: 5
	Projectile: Bullet
		Speed: 90
		Inaccuracy: 484
		Image: chemall
		Palette: ra2effect-ignore-lighting-alpha50
		LaunchAngle: 5
	Warhead@1Dam: SpreadDamage
		Spread: 288
		Falloff: 100, 50, 25, 12, 6, 3, 0
		Damage: 1450
		Versus:
			None: 300
			Wood: 135
			Tree: 120
			Brick: 5
			Concrete: 45
			Light: 85
			Heavy: 50
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Building, Wall

FlamerTD:
	ReloadDelay: 65
	Range: 2c512
	MinRange: 0c512
	Report: flamer2.aud
	TargetActorCenter: true
	Projectile: LinearPulse
		Speed: 256
		MinimumImpactDistance: 0c768
		MaximumImpactDistance: 1c768
		ForceGround: true
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Falloff: 100, 35, 10, 0
		Damage: 1850
		Versus:
			None: 300
			Wood: 100
			Tree: 100
			Light: 70
			Heavy: 30
			Concrete: 30
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		ValidRelationships: Enemy, Neutral
		Delay: 5
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Building, Wall

FlamerTDFF:
	Inherits: FlamerTD
	Projectile: LinearPulse
		MinimumImpactDistance: 1c768
	Warhead@1Dam: SpreadDamage
		Falloff: 100, 50, 0
		ValidRelationships: Ally
		DebugOverlayColor: ffaa00
	-Warhead@2Smu:

BigFlamerTD:
	Inherits: FlamerTD
	Burst: 2
	BurstDelays: 5
	Projectile: LinearPulse
		Speed: 288
		VisualSpeed: 96
		Image: flameallfast
		Inaccuracy: 484
		Palette: tseffect-ignore-lighting-alpha90
		MinimumImpactDistance: 288
		MaximumImpactDistance: 2304
	Warhead@1Dam: SpreadDamage
		Damage: 925
		Versus:
			Light: 100
			Concrete: 50

BigFlamerTD.Decoy:
	Inherits: BigFlamerTD
	Warhead@1Dam: SpreadDamage
		Damage: 0
	-Warhead@2Smu:

FlamerTDBATF:
	Inherits: BigFlamerTD
	Burst: 1

BigFlamerTDFF:
	Inherits: BigFlamerTD
	Projectile: LinearPulse
		-Image:
		-Palette:
		MinimumImpactDistance: 1440
	Warhead@1Dam: SpreadDamage
		Falloff: 100, 50, 0
		ValidRelationships: Ally
		DebugOverlayColor: ffaa00
	-Warhead@2Smu:

ChemsprayTD:
	Inherits: FlamerTD
	Warhead@1Dam: SpreadDamage
		Falloff: 100, 45, 20, 0
		Damage: 2000
		Versus:
			Concrete: 40
			Heavy: 40
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame

ChemsprayTDFF:
	Inherits: FlamerTDFF
	Warhead@1Dam: SpreadDamage
		Damage: 2000
		Versus:
			Concrete: 40
			Heavy: 40
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
		InvalidTargets: ChemWarrior

ChemsprayTDBATF:
	Inherits: BigFlamerTD
	Burst: 1
	Projectile: LinearPulse
		Image: chemallfast
		Palette: ra2effect-ignore-lighting-alpha90
	Warhead@1Dam: SpreadDamage
		Damage: 1100
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame

HeavyFlameTankFlamer:
	Inherits: BigFlamerTD
	-Report:
	ReloadDelay: 80
	Burst: 25
	BurstDelays: 2
	Warhead@1Dam: SpreadDamage
		Falloff: 100, 45, 20, 0
		Damage: 175
		Versus:
			None: 230
	Warhead@2Smu: LeaveSmudge
		AirThreshold: 256
		Chance: 10

HeavyFlameTankFlamer.Decoy:
	Inherits: HeavyFlameTankFlamer
	Warhead@1Dam: SpreadDamage
		Damage: 0
	-Warhead@2Smu:

HeavyFlameTankFlamer.UPG:
	Inherits: HeavyFlameTankFlamer
	ReloadDelay: 95
	Projectile: LinearPulse
		Image: flameallblack
		Palette: scrin-ignore-lighting-alpha85
	Warhead@1Dam: SpreadDamage
		Damage: 180
		Versus:
			Heavy: 70
			Concrete: 60
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-Black

HeavyFlameTankFlamer.UPG.Decoy:
	Inherits: HeavyFlameTankFlamer.UPG
	Warhead@1Dam: SpreadDamage
		Damage: 0
	-Warhead@2Smu:

HeavyFlameTankFlamerFF:
	Inherits: HeavyFlameTankFlamer
	Projectile: LinearPulse
		-Image:
		-Palette:
		MinimumImpactDistance: 1440
	Warhead@1Dam: SpreadDamage
		Falloff: 100, 50, 0
		ValidRelationships: Ally
		DebugOverlayColor: ffaa00
	-Warhead@2Smu:

HeavyFlameTankFlamerFF.UPG:
	Inherits: HeavyFlameTankFlamerFF
	ReloadDelay: 95

AvatarFlamer:
	Inherits: HeavyFlameTankFlamer
	Burst: 13
	BurstDelays: 4
	Warhead@1Dam: SpreadDamage
		Damage: 260

AvatarFlamer.UPG:
	Inherits: HeavyFlameTankFlamer.UPG
	Burst: 13
	BurstDelays: 4
	Warhead@1Dam: SpreadDamage
		Damage: 270

AvatarFlamerFF:
	Inherits: HeavyFlameTankFlamerFF
	Burst: 13
	BurstDelays: 4

AvatarFlamerFF.UPG:
	ReloadDelay: 95

BlackHandFlamer:
	Inherits: HeavyFlameTankFlamer
	ReloadDelay: 110
	BurstDelays: 2
	Burst: 28
	Range: 3c512
	Projectile: LinearPulse
		Speed: 440
		VisualSpeed: 220
		Inaccuracy: 160
		Image: thinblueflame
		Palette: effect-ignore-lighting-alpha85
		MinimumImpactDistance: 440
		MaximumImpactDistance: 3c512
		RepeatAnimation: false
	Warhead@1Dam: SpreadDamage
		Range: 0, 256, 512
		Falloff: 100, 100, 0
		Damage: 260
		Versus:
			None: 55
			Wood: 15
			Light: 70
			Heavy: 100
			Concrete: 35
			Brick: 5
		-Delay:
	Warhead@2Smu: LeaveSmudge
		Chance: 5

Napalm:
	Inherits: ^FireWeapon
	ReloadDelay: 20
	Range: 4c512
	Projectile: Bullet
		Image: BOMBLET
		Speed: 85
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 170
		Damage: 10000
		Versus:
			Wood: 100
			Concrete: 50

^TeslaWeapon:
	ReloadDelay: 3
	Range: 7c0
	Report: tesla1.aud
	Projectile: TeslaZapCA
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 10000
		Versus:
			None: 1000
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch

TeslaZap:
	Inherits: ^TeslaWeapon
	Warhead@1Dam: SpreadDamage
		Versus:
			Wood: 60
			Concrete: 80

TeslaZapBoost1:
	Inherits: TeslaZap
	Range: 7c512
	Warhead@1Dam: SpreadDamage
		Damage: 11000

TeslaZapBoost2:
	Inherits: TeslaZap
	Range: 8c0
	Warhead@1Dam: SpreadDamage
		Damage: 12000

TeslaZapBoost3:
	Inherits: TeslaZap
	Range: 8c512
	Warhead@1Dam: SpreadDamage
		Damage: 13000

PortaTesla:
	Inherits: ^TeslaWeapon
	ReloadDelay: 100
	Range: 6c0
	Report: shktrop1.aud
	Warhead@1Dam: SpreadDamage
		Damage: 5300
		Versus:
			None: 240
			Wood: 60
			Heavy: 75
			Concrete: 70
			Light: 110

PortaTesla.UPG:
	Inherits: PortaTesla
	Report: itesatta.aud
	ReloadDelay: 70
	Warhead@1Dam: SpreadDamage
		Versus:
			Heavy: 75
			Light: 125

PortaTeslaCharge:
	Inherits: ^TeslaWeapon
	ValidTargets: TeslaBoost
	ReloadDelay: 70
	Range: 1c768
	-Report:
	-Warhead@1Dam:
	-Warhead@2Smu:
	Warhead@charge: GrantExternalConditionCA
		Range: 42
		Duration: 70
		Condition: charged
		ValidRelationships: Ally
		ValidTargets: TeslaBoost
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: ffffff
		ValidTargets: Ground, Vehicle

TTrackZap:
	Inherits: ^TeslaWeapon
	ReloadDelay: 125
	Range: 7c0
	Warhead@1Dam: SpreadDamage
		Damage: 18500
		Versus:
			Wood: 50
			Concrete: 50
			Light: 75

TTrackZap.UPG:
	Inherits: TTrackZap
	Warhead@Arc: FireShrapnel
		Weapon: TTrackArc1
		Amount: 1
		AimChance: 100
		ThrowWithoutTarget: false
		AimTargetStances: Enemy, Neutral
		ValidTargets: Vehicle, Ship, Structure

TTrackArc1:
	Inherits: TTrackZap.UPG
	-Report:
	Range: 2c512
	ValidTargets: Vehicle, Ship
	Warhead@1Dam: SpreadDamage
		ValidTargets: Vehicle, Ship
		Versus:
			Light: 15
			Heavy: 15
	Warhead@Arc: FireShrapnel
		Weapon: TTrackArc2
		AimChance: 100

TTrackArc2:
	Inherits: TTrackArc1
	Warhead@1Dam: SpreadDamage
	-Warhead@Arc:

TTankZap:
	Inherits: ^TeslaWeapon
	Report: vtesatta.aud, vtesattb.aud
	ReloadDelay: 50
	Burst: 2
	BurstDelays: 3
	Range: 5c512
	Warhead@1Dam: SpreadDamage
		Damage: 3500
		Versus:
			None: 380
			Wood: 55
			Concrete: 55
			Heavy: 70

TTankZap.UPG:
	Inherits: TTankZap
	Warhead@Arc: FireShrapnel
		Weapon: TTankArc
		Amount: 1
		AimChance: 100
		ThrowWithoutTarget: false
		AimTargetStances: Enemy, Neutral

TTankArc:
	Inherits: TTankZap
	-Report:
	Range: 2c512
	ValidTargets: Infantry, Vehicle, Defense
	Warhead@1Dam: SpreadDamage
		ValidTargets: Infantry, Vehicle, Defense
		Damage: 1000
		Versus:
			None: 315

DogJaw:
	ValidTargets: Infantry
	ReloadDelay: 10
	Range: 2c0
	Report: dogg5p.aud
	TargetActorCenter: true
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		Damage: 100000
		ValidTargets: Infantry
		InvalidTargets: Ant, DogImmune
		DamageTypes: DefaultDeath

TerrorDogJaw:
	Inherits: DogJaw
	ValidTargets: Infantry, Vehicle, Structure

CyberdogJaw:
	Inherits: DogJaw
	ReloadDelay: 10
	Range: 4c512
	Report: cdog-attack1.aud
	Warhead@1Dam: TargetDamage
		-InvalidTargets:

DogExplode:
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 448
		Damage: 35000
		Versus:
			Wood: 75
			Concrete: 75
			Heavy: 70
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster
		InvalidTargets: TerrorDog
	Warhead@FF: HealthPercentageDamage
		Spread: 2c0
		Damage: 25
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		ValidTargets: TerrorDog
	Warhead@3Eff: CreateEffect
		Explosions: artillery_explosion
		ImpactSounds: xplosml2.aud

BruteAttack:
	ReloadDelay: 95
	Range: 1c512
	Report: brutehit.aud
	Projectile: InstantHit
	InvalidTargets: Structure
	TargetActorCenter: true
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 10000
		DamageTypes: DefaultDeath
		InvalidTargets: Structure
		Versus:
			None: 40
			Light: 75
			Heavy: 100
			Concrete: 50
			Wood: 35

BruteBuildingAttack:
	Inherits: BruteAttack
	Range: 1c768
	ValidTargets: Structure
	-InvalidTargets:
	Warhead@1Dam: SpreadDamage
		-InvalidTargets:
		ValidTargets: Structure

Commandeer:
	ReloadDelay: 5
	Range: 1c0
	Report: ggarenta.aud, ggarentb.aud, ggarentc.aud
	ValidTargets: NoCrew
	Projectile: InstantHit
	Warhead@1Change: ChangeOwner
		Range: 0c511
		ValidRelationships: Neutral
		ValidTargets: NoCrew

Heal:
	ReloadDelay: 80
	Range: 4c0
	ValidTargets: Heal
	Projectile: InstantHit
		Blockable: true
	TargetActorCenter: true
	Warhead@1Dam: SpreadDamage
		Spread: 213
		Damage: -3500
		Falloff: 100, 52, 20, 7, 0
		ValidRelationships: Ally
		ValidTargets: Heal
		DebugOverlayColor: 00FF00
		DamageTypes: DirectHeal
	Warhead@2Dam: TargetDamage
		Damage: -500
		ValidRelationships: Ally
		ValidTargets: Heal
		DamageTypes: DirectHeal
		Delay: 20
	Warhead@3Dam: TargetDamage
		Damage: -500
		ValidRelationships: Ally
		ValidTargets: Heal
		DamageTypes: DirectHeal
		Delay: 40
	Warhead@4Dam: TargetDamage
		Damage: -500
		ValidRelationships: Ally
		ValidTargets: Heal
		DamageTypes: DirectHeal
		Delay: 60

Repair:
	Inherits: Heal
	Range: 2c512
	ValidTargets: Repair
	Warhead@1Dam: SpreadDamage
		Damage: -3000
		ValidTargets: Repair
		DamageTypes: DirectRepair
	-Warhead@2Dam:
	-Warhead@3Dam:
	-Warhead@4Dam:
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: ffffff
		ValidTargets: Repair

CyborgRepair:
	Inherits: Repair
	ReloadDelay: 75

RepairIFV:
	Inherits: Repair
	ReloadDelay: 30
	Warhead@3Eff: CreateEffect
		Image: sparks_overlay
		Explosions: idle
		ExplosionPalette: tseffect-ignore-lighting-alpha75

RepairFlash:
	Inherits: Heal
	Range: 1c0
	Report: fixit1.aud
	ValidTargets: Repair
	Warhead@1Dam: SpreadDamage
		Damage: -1
		ValidTargets: Repair
		-DamageTypes:
	-Warhead@2Dam:
	-Warhead@3Dam:
	-Warhead@4Dam:
	Warhead@Flash: FlashTarget
		Spread: 1c0
		Color: ffffff
		ValidTargets: Repair

ShieldFlash:
	Inherits: RepairFlash
	Warhead@Flash: FlashTarget
		ValidTargets: Structure

Demolish:
	Warhead@1Dam: SpreadDamage
		DamageTypes: DefaultDeath
		Spread: 1c0
		Damage: 50000
	Warhead@2Eff: CreateEffect
		Explosions: building, building2
		ImpactSounds: kaboom22.aud
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 5
		Dimensions: 3,3
		Footprint: xxx xxx xxx

PlaceC4:
	ReloadDelay: 25
	Range: 1c849
	Projectile: InstantHit
	Report: icolseta.aud
	ValidTargets: C4Plantable
	InvalidTargets: C4Attached, TNTAttached, C4Immune
	Warhead@AttachDelayedWeapon: AttachDelayedWeapon
		Weapon: C4
		Type: c4
		TriggerTime: 60
		DeathTypes: ExplosionDeath
		Range: 1
	Warhead@TargetValidator: SpreadDamage
	Warhead@Flash: FlashTarget
		Spread: 0c256
		Color: ffffff
		ValidTargets: Ground, Structure, Vehicle

PlaceC4Seal:
	Inherits: PlaceC4
	InvalidTargets: Vehicle, C4Attached, TNTAttached, C4Immune
	Warhead@AttachDelayedWeapon: AttachDelayedWeapon
		TriggerTime: 20
		Type: c4seal
		ScaleTriggerTimeWithValue: true
	Warhead@Flash: FlashTarget
		ValidTargets: Ground, Structure

PlaceC4Assassin:
	Inherits: PlaceC4Seal
	Warhead@AttachDelayedWeapon: AttachDelayedWeapon
		Weapon: AssassinC4

PrepareC4Seal:
	Inherits: PlaceC4Seal
	-Report:
	-Warhead@AttachDelayedWeapon:
	Warhead@Flash: FlashTarget
		Color: ff0000
	Warhead@ProgressBar: GrantExternalConditionCA
		Range: 0c256
		Condition: c4seal-preparing
		Duration: 26
		ValidTargets: Structure

C4:
	Projectile: InstantHit
	Warhead@1Dam: HealthPercentageDamage
		ValidTargets: Air, Ground, Water
		Spread: 64
		Damage: 200
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		Versus:
			Light: 150
			Heavy: 150
	Warhead@2Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: icolexpa.aud
		ValidTargets: Ground, Water, Air
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Structure, Wall, Trees
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx xxx xxx

AssassinC4:
	Inherits: C4

Ivanbomb:
	Inherits: ^Artillery
	ValidTargets: Ground, TNTPlantable
	InvalidTargets: C4Attached, C4Immune
	ReloadDelay: 75
	Range: 3c725
	Report: grenade1.aud
	Projectile: Bullet
		LaunchAngle: 80
		Speed: 166
		Inaccuracy: 0
		Image: TNT
		BounceCount: 2
		BounceRangeModifier: 60
		BounceSound: dud2.aud
		ValidBounceBlockerRelationships: Ally, Neutral, Enemy
		ContrailLength: 10
		ContrailStartColor: FF9900
		ContrailStartColorAlpha: 192
		ContrailEndColor: FF0000
		ContrailStartWidth: 0c40
	Warhead@AttachDelayedWeapon: AttachDelayedWeapon
		Weapon: TNT
		Type: tnt
		TriggerTime: 140
		DeathTypes: ExplosionDeath
		Range: 1
		AttachSounds: icraatta.aud
		MissWeapon: DelayedTNT
	Warhead@TargetValidator: SpreadDamage
	Warhead@Flash: FlashTarget
		Spread: 0c256
		Color: ffffff
		ValidTargets: Ground, Structure, Vehicle
	-Warhead@1Dam:
	-Warhead@2Smu:
	Warhead@3Eff: CreateEffect
		Explosions: piff
		ImpactSounds: dud1.aud
	-Warhead@4EffWater:

TNT:
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 2800
		Falloff: 1000, 448, 192, 50, 18, 7, 0
		Versus:
			Light: 70
			Heavy: 20
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@AirDam: SpreadDamage
		Spread: 128
		Damage: 28000
		ValidTargets: Air, AirSmall
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: building, building2
		ImpactSounds: expnew06.aud
		ValidTargets: Ground, Water, Air
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Structure, Wall, Trees
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx xxx xxx

DelayedTNT:
	Inherits: TNT
	Warhead@1Dam: SpreadDamage
		Delay: 50
		ValidTargets: Ground, Water
	Warhead@2Eff: CreateEffect
		Delay: 50
	Warhead@Smu: LeaveSmudge
		Delay: 50
	Warhead@Flames: FireCluster
		Delay: 50
	Warhead@tnt: CreateEffect
		Image: groundtnt
		Explosions: idle1, idle2, idle3, idle4, idle5, idle6, idle7, idle8
		ValidTargets: Ground, Water

DefuseKit:
	ReloadDelay: 50
	Range: 2c511
	Report: gdefuse.aud
	ValidTargets: C4Attached, TNTAttached
	Projectile: LaserZapCA
		Width: 48
		Duration: 4
		Color: FFFFFF80
	Warhead@Flash: FlashTarget
		Spread: 128
		Color: ffffff
		ValidTargets: C4Attached, TNTAttached
	Warhead@DetachDelayedWeapon: DetachDelayedWeapon
		Types: c4, tnt, c4seal
		Range: 128
		ValidRelationships: Ally
		ValidTargets: C4Attached, TNTAttached
	Warhead@TargetValidator: SpreadDamage
		ValidTargets: C4Attached

IFVDefuseKit:
	Inherits: DefuseKit
	Range: 4c511

DemoTruckTargeting:
	Range: 1c849
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage

IvanIFVTargeting:
	Inherits: DemoTruckTargeting

Hack:
	Range: 11c0
	ReloadDelay: 50
	ValidTargets: Hackable
	TargetActorCenter: true
	Report: hacker-pulse.aud
	Projectile: ArcLaserZap
		Color: 1ce31270
		Angle: 60
		Width: 86
		Duration: 20
		HitAnim: empty
	Warhead@1Dam: TargetDamage
		ValidRelationships: Enemy, Neutral
	Warhead@CAMERA: SpawnActor
		Actors: camera.hacker
		Range: 1
		ImpactActors: false
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: 00ff00
		ValidTargets: Structure, Vehicle
		ValidRelationships: Neutral, Enemy
	Warhead@BeingCaptured: GrantExternalConditionCA
		Range: 0c511
		Duration: 50
		Condition: being-captured
		ValidTargets: Structure
		ValidRelationships: Neutral, Enemy

PrepareHack:
	Inherits: Hack
	-Report:
	-Projectile:
	Projectile: InstantHit
	-Warhead@Flash:

MADTankDetonate:
	InvalidTargets: Infantry
	Warhead@1Dam: HealthPercentageDamage
		Spread: 7c0
		Damage: 19
		Versus:
			Heavy: 85
			Light: 75
		InvalidTargets: MADTank, Infantry
	Warhead@2Dam: HealthPercentageDamage
		Spread: 5c0
		AffectsParent: true
		Damage: 10
		ValidTargets: MADTank
		Delay: 5
	Warhead@2Smu1: LeaveSmudge
		SmudgeType: Crater
		Size: 4
		Chance: 10
	Warhead@2Smu2: LeaveSmudge
		SmudgeType: Crater
		Size: 6,5
		Chance: 20
	Warhead@2Smu3: LeaveSmudge
		SmudgeType: Crater
		Size: 7,6
	Warhead@3Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: madexplo.aud
		ImpactActors: false
	Warhead@4Dam: SpreadDamage
		Spread: 1c0
		Damage: 100000
		Falloff: 100, 50, 33, 20, 10, 5, 0
		ValidTargets: Ground, Water
		Versus:
			None: 0
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 100
			Brick: 100
	Warhead@Concussion: GrantExternalConditionCA
		Range: 7c0
		Duration: 150
		Condition: concussion
		ValidTargets: Ground, Infantry, Vehicle, Ship

Laser:
	ReloadDelay: 95
	ValidTargets: Ground, Water
	Range: 8c512
	Report: oblfire.aud
	Projectile: LaserZapCA
		Width: 56
		HitAnim: laserfire
		Color: FF000080
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 90
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: FF000040
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 37500
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 250
			Light: 101
			Wood: 60
			Heavy: 101
			Concrete: 90
			Brick: 50
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep

Laser.Adv:
	Inherits: Laser
	Report: obelmod2.aud
	Range: 9c0
	-Projectile:
	Projectile: PlasmaBeam
		Duration: 21
		Colors: FF00BB80, FF0000A0, FF002270
		InnerLightness: 200
		OuterLightness: 115
		Radius: 2
		Distortion: 0
		DistortionAnimation: 96
		StartOffset: 0,480,0
		FollowingOffset: 0,-48,0
		RecalculateColors: true
		TrackTarget: true
		ImpactTicks: 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 4000
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion, small_explosion_alt1, small_explosion_alt2, small_explosion_alt3

LaserTur:
	Inherits: Laser
	ReloadDelay: 34
	Range: 6c0
	Report: lastur1.aud
	Projectile: LaserZapCA
		Width: 45
		Duration: 5
		SecondaryBeamWidth: 65
		SecondaryBeamColor: FF000030
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Damage: 5500
		Versus:
			None: 320
			Wood: 70
			Light: 85
			Heavy: 25
			Concrete: 44

LaserTur.Adv:
	Inherits: LaserTur
	Range: 7c0
	ReloadDelay: 30
	Projectile: LaserZapCA
		Duration: 10
		SecondaryBeamWidth: 75
		SecondaryBeamColor: FF00FF60
	Warhead@1Dam: SpreadDamage
		Spread: 128

LightTankLaser:
	Range: 4c768
	Inherits: LaserTur
	ReloadDelay: 50
	Report: tnklaser.aud
	Warhead@1Dam: SpreadDamage
		Damage: 5800
		Versus:
			None: 40
			Wood: 40
			Light: 100
			Heavy: 25
			Concrete: 25
			Brick: 20
		DamageTypes: Prone50Percent, FireDeath
	Warhead@PercDam: HealthPercentageDamage
		Spread: 42
		Damage: 50
		Versus:
			Light: 1
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		ValidTargets: Infantry

BattleTankLaser:
	Inherits: LightTankLaser
	ReloadDelay: 40

StealthAPCLaser:
	Inherits: LaserTur
	ReloadDelay: 30
	Report: sapc-fire1.aud, sapc-fire2.aud
	Warhead@1Dam: SpreadDamage
		Damage: 3400
		Versus:
			None: 100
			Wood: 20
			Light: 45
			Heavy: 20
			Concrete: 20

XOLaser:
	Inherits: Laser
	Report: xo-laser.aud
	ReloadDelay: 110
	Range: 7c0
	Projectile: LaserZapCA
		Blockable: true
		Width: 56
		HitAnim: laserfire
		Color: 9eff1480
		SecondaryBeam: true
		SecondaryBeamWidth: 90
		SecondaryBeamColor: 79cd0040
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 11000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 65
			Wood: 40
			Concrete: 75
			Light: 80
			Heavy: 100

XOCoilGun:
	Range: 7c0
	ReloadDelay: 70
	Report: xo-coil1.aud
	Burst: 2
	BurstDelays: 6
	Projectile: MissileCA
		Image: flakball
		Speed: 682
		TrailImage: coiltrail
		TrailSequences: idle1, idle2
		TrailInterval: 1
		ContrailStartWidth: 32
		ContrailStartColor: 809acf
		ContrailStartColorAlpha: 96
		ContrailLength: 6
		TrailSpacing: 682
		AllowSnapping: true
	Warhead@1Dam: SpreadDamage
		Spread: 0c128
		Damage: 2500
		Versus:
			None: 100
			Wood: 10
			Light: 70
			Heavy: 35
			Concrete: 10
			Brick: 10
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@3Eff: CreateEffect
		Explosions: piff
		ValidTargets: Ground, Water, Trees
		Inaccuracy: 64

PortaLaser:
	Inherits: Laser
	ReloadDelay: 42
	Burst: 2
	BurstDelays: 3
	Range: 6c0
	-Report:
	StartBurstReport: lasgun.aud, lasgun2.aud
	Projectile: LaserZapCA
		Width: 30
		Duration: 3
		SecondaryBeamWidth: 50
		SecondaryBeamColor: FF000030
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Damage: 1900
		Versus:
			None: 155
			Wood: 55
			Heavy: 40
			Concrete: 40

PortaLaser.Templar:
	Inherits: PortaLaser
	Warhead@1Dam: SpreadDamage
		Versus:
			None: 175
			Heavy: 45

PortaLaser.Templar.UPG:
	Inherits: PortaLaser.Templar
	Range: 6c512
	StartBurstReport: lasgunupg.aud, lasgunupg2.aud
	Projectile: LaserZapCA
		SecondaryBeamWidth: 75
		SecondaryBeamColor: FF006660
	Warhead@1Dam: SpreadDamage
		Damage: 2000

IFVLaser:
	Inherits: PortaLaser
	Range: 7c0
	-Burst:
	-StartBurstReport:
	Report: venmfireupg1.aud, venmfireupg1.aud
	Projectile: LaserZapCA
		SecondaryBeamWidth: 75
	Warhead@1Dam: SpreadDamage
		Damage: 4250

EnlightenedBeam:
	ReloadDelay: 65
	ValidTargets: Ground, Water
	Range: 6c512
	Report: enli-fire1.aud, enli-fire2.aud, enli-fire3.aud
	Projectile: PlasmaBeam
		Duration: 10
		CenterBeam: true
		CenterBeamColor: FFFFFFAA
		CenterBeamWidth: 48
		Colors: 0EF3C101, 0E9FF301, 0EEBF301
		InnerLightness: 200
		OuterLightness: 120
		Radius: 2
		SegmentLength: 120
		Distortion: 10
		DistortionAnimation: 20
		Blockable: true
		RecalculateColors: true
		TrackTarget: false
		MaxFacingDeviation: 64
	Warhead@1Dam: HealthPercentageSpreadDamage
		Spread: 341
		Falloff: 100, 45, 25, 15, 0
		Damage: 100
		Versus:
			Wood: 5
			None: 10
			Concrete: 10
			Brick: 10
			Light: 17
			Heavy: 17
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		ValidRelationships: Enemy, Neutral
	Warhead@friendlyFire: SpreadDamage
		Spread: 256
		Damage: 2500
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		Versus:
			None: 5
			Wood: 70
			Concrete: 45
			Light: 70
		ValidRelationships: Ally
	Warhead@2Eff: CreateEffect
		Explosions: enliexplode1, enliexplode2
		ImpactSounds: expnew17.aud, expnew16.aud
		ValidTargets: Ground, Air
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep
	Warhead@Flash: FlashTarget
		Spread: 1c0
		Color: 00ffff
		ValidTargets: Vehicle, Ship

EnlightenedEmp:
	ReloadDelay: 500
	Range: 6c512
	Report: enli-empfire.aud
	Projectile: Missile
		Blockable: false
		HorizontalRateOfTurn: 8
		Shadow: true
		Image: enliempproj
		Palette: effect
		MaximumLaunchSpeed: 190
		MinimumLaunchSpeed: 190
		Speed: 190
		RangeLimit: 6c512
		Jammable: false
		TrailImage: smokey
		TrailPalette: scrinplasma
	Warhead@1Emp: GrantExternalConditionCA
		Range: 0c896
		Duration: 150
		Condition: empdisable
		ValidTargets: Vehicle, Ship
		InvalidTargets: Cyborg, EmpImmune
	Warhead@2Emp: GrantExternalConditionCA
		Range: 0c896
		Duration: 100
		Condition: empdisable
		ValidTargets: Cyborg, Defense
		InvalidTargets: EmpImmune
	Warhead@3Eff_impact: CreateEffect
		Explosions: enliemphit1, enliemphit2
		ImpactSounds: enli-emphit.aud
		Inaccuracy: 341

VenomLaser:
	ReloadDelay: 30
	ValidTargets: Ground, Water
	Range: 5c512
	Report: venmfire1.aud, venmfire2.aud
	Projectile: LaserZapCA
		Width: 35
		HitAnim: laserfire
		Color: FF000080
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 65
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: FF000040
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 5500
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, AirToGround
		Versus:
			None: 250
			Wood: 50
			Concrete: 40
			Light: 95
			Heavy: 55
			Brick: 50
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep

VenomLaser.UPG:
	Inherits: VenomLaser
	Report: venmfireupg1.aud, venmfireupg1.aud
	Range: 6c512
	Projectile: LaserZapCA
		Duration: 10
		SecondaryBeamWidth: 75
		SecondaryBeamColor: FF00FF60
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 6325
		Versus:
			Heavy: 60

VenomLaserAA:
	Inherits: VenomLaser
	ValidTargets: Air, AirSmall
	Projectile: LaserZapCA
		-HitAnim:
	Warhead@1Dam: SpreadDamage
		ValidTargets: Air, AirSmall
		Damage: 4000
		Versus:
			Wood: 100
			Concrete: 100
			Light: 100
			Heavy: 100
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	-Warhead@2Smu:
	Warhead@3Eff: CreateEffect
		ValidTargets: Air, AirSmall
		Explosions: med_explosion

VenomLaserAA.UPG:
	Inherits: VenomLaserAA
	Report: venmfireupg1.aud, venmfireupg1.aud
	Range: 6c512
	Projectile: LaserZapCA
		Duration: 10
		SecondaryBeamWidth: 75
		SecondaryBeamColor: FF00FF60
	Warhead@1Dam: SpreadDamage
		Damage: 4600

PointLaser:
	Inherits: Laser
	ReloadDelay: 125
	ValidTargets: Ground, Water, Missile
	InvalidTargets: Infantry, Vehicle, Structure, Wall, Ship, Submarine
	Range: 1c768
	Report: vpalwe2d.aud
	AirThreshold: 4c0
	Projectile: LaserZapCA
		Width: 30
		Duration: 3
		-HitAnim:
		Color: 03f0fc
		SecondaryBeamWidth: 50
		SecondaryBeamColor: 03fcfc30
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Damage: 1

PrisLaser:
	ValidTargets: Ground, Water
	ReloadDelay: 3
	Range: 7c512
	Report: prisfire.aud
	Projectile: LaserZapCA
		Width: 35
		HitAnim: plaserfire
		Color: 00FFFFC8
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 65
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: 75D1FF
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 13000
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		Versus:
			None: 40
			Wood: 40
			Concrete: 90
			Light: 80
			Heavy: 80
			Brick: 50
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep
	Warhead@3Eff: CreateEffect
		ImpactSounds: xplos.aud
		ValidTargets: Ground, Water, Ship, Trees
	Warhead@Burst: FireCluster
		Weapon: PrisBurst
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx x_x xxx

PrisLaserSupport:
	ValidTargets: Ground, Water
	ReloadDelay: 3
	Range: 5c0
	Report: bpriat1a.aud
	Projectile: LaserZapCA
		Width: 35
		Color: 00FFFFC8
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 65
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: 75D1FF
	AirThreshold: 1000

PrisBurst:
	Inherits: PrisLaser
	-Report:
	Range: 3c0
	Projectile: LaserZapCA
		-HitAnim:
	Warhead@1Dam: SpreadDamage
		Spread: 256
		Damage: 6500
		Versus:
			None: 50
			Wood: 40
			Concrete: 25
			Light: 100
			Heavy: 55
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, FlakVestMitigated
	-Warhead@Burst:
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion
		-ImpactSounds:

PrisTLaser:
	Inherits: PrisLaser
	Report: ptnkfire.aud
	ReloadDelay: 50
	Range: 8c0
	Projectile: LaserZapCA
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Damage: 4400
		Spread: 341
		Versus:
			None: 150
			Wood: 100
			Concrete: 100
			Light: 100
			Heavy: 45
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, FlakVestMitigatedMinor
	Warhead@Burst: FireCluster
		Weapon: PrisTBurst

PrisTBurst:
	Inherits: PrisTLaser
	-Report:
	Range: 3c0
	Projectile: LaserZapCA
		-HitAnim:
	Warhead@1Dam: SpreadDamage
		Damage: 2100
		Spread: 160
		Versus:
			Heavy: 20
			Wood: 35
			Concrete: 100
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, FlakVestMitigated
	-Warhead@Burst:
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion
		-ImpactSounds:

PrisCLaser:
	Inherits: PrisTLaser
	Report: pcanfire.aud
	ReloadDelay: 125
	Range: 11c0
	Projectile: LaserZapCA
		Width: 70
		Duration: 15
		Color: 00FFDDDD
		SecondaryBeamWidth: 140
		SecondaryBeamColor: 4be8d499
	Warhead@1Dam: SpreadDamage
		Damage: 18000
		Spread: 512
		Versus:
			None: 60
			Wood: 100
			Concrete: 75
			Light: 85
			Heavy: 30
	-Warhead@Burst:

HopliteGun:
	ReloadDelay: 75
	Range: 7c0
	Report: hopl-fire1.aud
	Projectile: LaserZapCA
		Width: 40
		HitAnim: laserfire
		Color: 00FFFFC8
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 75
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: 75D1FF60
	Warhead@1Dam: SpreadDamage
		Spread: 180
		Damage: 6000
		Versus:
			None: 150
			Wood: 100
			Light: 100
			Heavy: 40
			Concrete: 50
			Brick: 30
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep

HopliteGunCharged:
	Inherits: HopliteGun
	Report: hopl-cfire1.aud
	Projectile: LaserZapCA
		Color: fffdcdC8
		SecondaryBeamColor: fff30060
	Warhead@Blind: GrantExternalConditionCA
		Range: 0c896
		Duration: 75
		Condition: blinded
		ValidTargets: Infantry
	Warhead@BlindVehicle: GrantExternalConditionCA
		Range: 0c512
		Duration: 75
		Condition: blinded
		ValidTargets: Vehicle, Ship
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: caneon
		Image: nullhit

HopliteTargeter:
	Inherits: HopliteGun
	-Report:
	-Projectile:
	Projectile: InstantHit
	-Warhead@1Dam:
	-Warhead@2Smu:

HopliteIFVGun:
	Inherits: HopliteGun
	ReloadDelay: 60
	Report: ptnkfire.aud
	Projectile: LaserZapCA
		HitAnim: plaserfire
	Warhead@1Dam: SpreadDamage
		Damage: 7000
		Spread: 288
		Versus:
			Wood: 80
			Heavy: 30

HopliteGunBATF:
	Inherits: HopliteGunCharged
	Range: 6c0
	Warhead@1Dam: SpreadDamage
		Spread: 288

KirovBomb:
	ReloadDelay: 25
	ValidTargets: Ground, Water
	Report: bwhis.aud
	Range: 0c480
	Projectile: GravityBomb
		Image: TBOMB
		OpenSequence: open
		Velocity: 5, 0, -66
		Acceleration: 0, 0, 0
		Shadow: True
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Falloff: 100, 40, 20, 5
		Damage: 13000
		Versus:
			None: 40
			Wood: 100
			Light: 85
			Heavy: 75
			Concrete: 60
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, AirToGround
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
	Warhead@3Eff: CreateEffect
		Explosions: artillery_explosion
		ImpactSounds: kaboom15.aud
		ValidTargets: Ground, Ship, Trees
	Warhead@4EffWater: CreateEffect
		Explosions: large_splash
		ImpactSounds: splashl1.aud, splashl2.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

KirovTeslaBomb:
	Inherits: KirovBomb
	Warhead@1Dam: SpreadDamage
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath, AirToGround
	Warhead@3Eff: CreateEffect
		Explosions: tsla_bomb
		ExplosionPalette: ra2effect
		ImpactSounds: kirbo1.aud, kirbo2.aud
	Warhead@5Tesla: FireShrapnel
		Weapon: KirovTeslaArc
		Amount: 3
		AimChance: 100
		AllowDirectHit: true
		ThrowWithoutTarget: false
		AimTargetStances: Enemy, Neutral
		ImpactActors: false

KirovClusterBomb:
	Inherits: KirovBomb
	Warhead@1Dam: SpreadDamage
		Spread: 1c512
		Versus:
			Concrete: 65
	Warhead@3Eff: CreateEffect
		Explosions: large_artillery_explosion
		ImpactSounds: artyhit.aud, artyhit2.aud, artyhit3.aud
	Warhead@Concussion1: GrantExternalConditionCA
		Range: 0c768
		Duration: 125
		Condition: concussion
		ValidTargets: Ground, Infantry, Vehicle, Ship
	Warhead@Concussion2: GrantExternalConditionCA
		Range: 1c768
		Duration: 35
		Condition: concussion
		ValidTargets: Ground, Infantry, Vehicle, Ship
	Warhead@5Shake: ShakeScreen
		Duration: 5
		Intensity: 1
		Multiplier: 0.5,0.5
	Warhead@6Cluster: FireCluster
		Weapon: KirovCluster
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx x_x xxx

KirovNukeBomb:
	Inherits: KirovBomb
	Warhead@1Dam: SpreadDamage
		Versus:
			Light: 100
			Heavy: 90
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath, AirToGround
	Warhead@3Eff: CreateEffect
		Explosions: nuke3
		ImpactSounds: kaboom1.aud
	Warhead@7Radio: CreateTintedCells
		Spread: 1c0
		Level: 300
		Falloff: 100, 55, 32, 5
		MaxLevel: 750
		LayerName: radioactivity.medium

KirovChaosBomb:
	Inherits: KirovBomb
	Warhead@Cloud1: SpawnActor
		Actors: chaoscloud, chaoscloud2
		Range: 5
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@3Eff: CreateEffect
		Explosions: chaosexplosion
		ExplosionPalette: caneon
		ImpactSounds: firebl3.aud
		ValidTargets: Ground, Trees
		ImpactActors: false

KirovTeslaArc:
	Inherits: TTankZap
	Range: 4c0
	Report: shktrop1.aud
	ValidTargets: Infantry, Vehicle, Ship
	Warhead@1Dam: SpreadDamage
		Damage: 3000
		ValidTargets: Infantry, Vehicle, Ship
		ValidRelationships: Enemy, Neutral

KirovCluster:
	ReloadDelay: 35
	Range: 3c0
	Projectile: Bullet
		Image: BOMB
		Speed: 96
		LaunchAngle: 0, 32
		Inaccuracy: 1c0
		BounceCount: 0
	Warhead@1Dam: SpreadDamage
		Spread: 256
		Damage: 1100
		Versus:
			None: 40
			Wood: 100
			Light: 65
			Heavy: 40
			Concrete: 60
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Structure, Wall, Trees
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: kaboom15.aud
		ValidTargets: Ground, Air, Ship, Trees
	Warhead@4EffWater: CreateEffect
		Explosions: med_splash
		ImpactSounds: splashl1.aud, splashl2.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

SonicZap:
	ReloadDelay: 120
	Range: 5c0
	TargetActorCenter: false
	Report: sonic4.aud
	Projectile: AreaBeamCA
		Speed: 0c428
		Duration: 65
		DamageInterval: 3
		Width: 420
		Shape: Flat
		ZOffset: 2048
		BeyondTargetRange: 0c341
		Blockable: true
		TrackTarget: true
		Color: 00c3ff25
	Warhead@1Dam: SpreadDamage
		Range: 0, 32
		Falloff: 100, 100
		Damage: 720
		ValidRelationships: Neutral, Enemy
		ValidTargets: Ground, Water
		Versus:
			Heavy: 60
			Concrete: 130
			Wood: 180
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath
	Warhead@2Dam: SpreadDamage
		Range: 0, 32
		Falloff: 100, 100
		Damage: 180
		InvalidTargets: Disruptor
		ValidRelationships: Ally
		Versus:
			Heavy: 60
			Brick: 400
			Wood: 180
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath

SonicZap.UPG:
	Inherits: SonicZap
	Range: 5c512
	Projectile: AreaBeamCA
		Color: 00ffcc25
	Warhead@1Dam: SpreadDamage
		Damage: 820
	Warhead@4Conc: GrantExternalConditionCA
		Range: 0c512
		Duration: 75
		Condition: concussion
		ValidTargets: Infantry, Vehicle, Ship
		ValidRelationships: Enemy, Neutral

SonicZapVisual:
	ReloadDelay: 120
	Range: 5c0
	TargetActorCenter: false
	Projectile: AreaBeamCA
		Speed: 0c428
		Duration: 65
		DamageInterval: 3
		Width: 180
		Shape: Flat
		ZOffset: 2048
		BeyondTargetRange: 0c341
		Blockable: true
		TrackTarget: true
		Color: 00fffb35

SonicZapVisual.UPG:
	Inherits: SonicZapVisual
	Range: 5c512
	Projectile: AreaBeamCA
		Color: 00ffa845

GapBeam:
	ReloadDelay: 15
	Range: 10c0
	Report: gaploop1.aud, gaploop2.aud, gaploop3.aud
	ValidTargets: Ground, Water, Air, AirSmall
	Projectile: ElectricBolt
		Colors: 111111CC,223333CC,11222299,222233CC,44444466,436a6666
		Duration: 16
		Angle: 45
		ZOffset: 512
		Width: 42
		Distortion: 32
		DistortionAnimation: 64
		TrackTarget: true
	Warhead@Debuff: GrantExternalConditionCA
		Range: 1c256
		Duration: 15
		Condition: gapveiled
		ValidRelationships: Enemy, Neutral
		ValidTargets: Ground, Water, Air, AirSmall
	Warhead@2Eff: CreateEffect
		Image: gap
		Explosions: muzzle
		ValidTargets: Ground, Water, Trees, Air, AirSmall
		Inaccuracy: 128
	Warhead@3Eff: CreateEffect
		Explosions: veilcloudsm, veilcloudsmf
		ExplosionPalette: effect-ignore-lighting-alpha40
		Inaccuracy: 1c256
		ValidTargets: Ground, Water, Trees, Air, AirSmall

MobileGapBeam:
	Inherits: GapBeam
	-ValidTargets:
	Range: 10c0
	Warhead@Debuff: GrantExternalConditionCA
		Range: 0c768
	Warhead@3Eff: CreateEffect
		Inaccuracy: 1c0

RadarJammer:
	ReloadDelay: 150
	Report: jammed.aud
	Range: 10c0
	Projectile: InstantHit
	Warhead@2Spawn: SpawnActor
		Actors: jamming.field
		Range: 1
		ForceGround: false
		ValidTargets: Ground, Water
		ImpactActors: false

VeilSmall:
	ReloadDelay: 10
	Range: 1c0
	Projectile: InstantHit
	Warhead@Cloud: CreateEffect
		Explosions: veilcloud, veilcloudf
		ExplosionPalette: effect-ignore-lighting-alpha50
		Inaccuracy: 2c0
		ValidTargets: Ground, Water
		ImpactActors: false

VeilMedium:
	Inherits: VeilSmall
	Warhead@Cloud: CreateEffect
		Inaccuracy: 3c768

VeilLarge:
	Inherits: VeilSmall
	Warhead@Cloud: CreateEffect
		Inaccuracy: 6c0

Railgun:
	ReloadDelay: 70
	Range: 8c0
	Report: railuse5.aud
	InvalidTargets: Submarine
	Projectile: RailgunCA
		Duration: 10
		Blockable: true
		DamageActorsInLine: false
		BeamColor: 00FFFFC8
		BeamWidth: 40
		HelixThickness: 16
		HelixRadius: 32
		HitAnim: explosion
		HitAnimSequence: small_explosion
		ZOffset: 2046
	Warhead@1Dam: SpreadDamage
		Spread: 0c64
		Falloff: 100, 37, 14, 5, 0
		Damage: 9500
		ValidRelationships: Ally, Neutral, Enemy
		InvalidTargets: Submarine
		Versus:
			None: 100
			Wood: 65
			Light: 100
			Heavy: 85
			Concrete: 15
			Brick: 75
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath

TitanRailgun:
	Inherits: Railgun
	Report: titanrailgun1.aud, titanrailgun2.aud
	ReloadDelay: 80
	Range: 5c768
	Projectile: RailgunCA
		DamageActorsInLine: true
		BeamColor: EEFFFFBB
		BeamWidth: 48
		HelixThickness: 20
		HelixRadius: 42
		HelixColor: 66FFEEBB
		Duration: 22
		HelixAlphaDeltaPerTick: -8
		BeamAlphaDeltaPerTick: -8
		HelixPitch: 682
		ZOffset: 4096
		PassthroughToMaxRange: true
		PassthroughMinDistance: 2c0
	Warhead@1Dam: SpreadDamage
		Damage: 5000
		Versus:
			None: 30
			Wood: 70
			Light: 70
			Heavy: 100
			Concrete: 60
		ValidRelationships: Enemy, Neutral
	Warhead@2Dam: SpreadDamage
		Damage: 1000
		Versus:
			None: 30
			Wood: 70
			Light: 70
			Heavy: 100
			Concrete: 60
			Brick: 75
		ValidRelationships: Ally
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath

TitanRailgunImpact:
	Inherits: TitanRailgun
	-Report:
	-Projectile:
	-Warhead@2Dam:
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Damage: 11000
		ValidRelationships: Enemy, Neutral, Ally

IonZap:
	ReloadDelay: 85
	Range: 4c768
	Report: ion1.aud
	Projectile: Railgun
		Duration: 7
		Blockable: true
		DamageActorsInLine: false
		BeamColor: ffffff
		BeamWidth: 40
		BeamAlphaDeltaPerTick: -16
		HelixThickness: 32
		HelixRadius: 10
		HelixPitch: 30
		HelixRadiusDeltaPerTick: 16
		HelixAlphaDeltaPerTick: -16
		HelixAngleDeltaPerTick: 16
		QuantizationCount: 4
		HelixColor: D8FCfC
		HitAnim: explosion
		HitAnimSequence: ionexp
		HitAnimPalette: temptd
		Inaccuracy: 64
		ZOffset: 2045
	Warhead@1Dam: SpreadDamage
		Damage: 12000
		Spread: 448
		AffectsParent: false
		Versus:
			None: 30
			Wood: 75
			Light: 85
			Heavy: 115
			Concrete: 60
			Brick: 75
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@3Eff: CreateEffect
		Explosions: shock_wave
		ExplosionPalette: tseffect
	Warhead@4Smu_impact: LeaveSmudge
		SmudgeType: Scorch

BurnFX:
	ValidTargets: Ground, Water, Trees
	ReloadDelay: 20
	Range: 0c128
	Projectile: Bullet
		Speed: 85
		Blockable: false
	Warhead@3Eff: CreateEffect
		Image: fire
		Inaccuracy: 171
		Explosions: 3, 6, 7, 8

BurnFXBlack:
	Inherits: BurnFX
	Warhead@3Eff: CreateEffect
		Image: fireblack
		Explosions: 1, 2, 3, 4
		ExplosionPalette: scrineffect

LaserFence:
	TargetActorCenter: true
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		DebugOverlayColor: FF0000
		Damage: 10000000
		DamageTypes: FireDeath

OrcaBomb:
	ValidTargets: Ground, Water
	ReloadDelay: 50
	Report: chute1.aud
	Burst: 3
	BurstDelays: 7
	Range: 2c0
	TargetActorCenter: true
	Projectile: GravityBomb
		Image: EMPBOMBLET
		Velocity: 25, 0, -74
		Acceleration: 0, 0, 0
		Shadow: true
	Warhead@1Dam: SpreadDamage
		Spread: 512
		Damage: 1600
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: Ground, Water
		Versus:
			None: 25
			Wood: 0
			Light: 75
			Heavy: 100
			Concrete: 35
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath, AirToGround
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
	Warhead@3Eff: CreateEffect
		Explosions: pulse_explosion_small
		ExplosionPalette: tseffect-ignore-lighting-alpha75
		ImpactSounds: orcabomb1.aud, orcabomb2.aud
	Warhead@4Eff: CreateEffect
		Explosions: artillery_explosion
		ValidTargets: Ground, Ship, Trees
	Warhead@emp: GrantExternalConditionCA
		Range: 1c512
		Duration: 375
		Condition: empdisable
		ValidTargets: Ground, Structure, Vehicle
		InvalidTargets: Defense, EmpImmune
	Warhead@empdef: GrantExternalConditionCA
		Range: 1c512
		Duration: 625
		Condition: empdisable
		ValidTargets: Defense
		InvalidTargets: EmpImmune

OrcaBombBuilding:
	Inherits: OrcaBomb
	-Report:
	-Projectile:
	Projectile: InstantHit
	TargetActorCenter: false
	Warhead@1Dam: SpreadDamage
		Delay: 35
		Versus:
			None: 0
			Wood: 40
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
		DebugOverlayColor: ffaa00
	-Warhead@2Smu:
	-Warhead@3Eff:
	-Warhead@4Eff:
	-Warhead@emp:
	-Warhead@empdef:

MicrowaveZap:
	ValidTargets: Ground, Water
	TargetActorCenter: true
	ReloadDelay: 170
	Range: 7c0
	Report: corefir1.aud
	Projectile: Railgun
		Duration: 5
		Blockable: true
		DamageActorsInLine: false
		BeamColor: FFFFFFFF
		BeamWidth: 0c0
		HelixThickness: 0c32
		HelixRadius: 0c16
		HelixAngleDeltaPerTick: 64
		QuantizationCount: 64
		HitAnim: sparks_overlay
		HitAnimPalette: tseffect-ignore-lighting-alpha75
		ZOffset: 6144
	Warhead@1Dam: SpreadDamage
		InvalidTargets: Infantry
		Falloff: 100, 37, 14, 0
		Spread: 448
		Damage: 30000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 20
			Wood: 5
			Concrete: 40
			Light: 100
			Heavy: 85
			Brick: 5
	Warhead@2Dam: SpreadDamage
		ValidTargets: Infantry
		Spread: 42
		Damage: 30000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@emp: GrantExternalConditionCA
		Range: 0c511
		Duration: 100
		Condition: empdisable
		ValidTargets: Vehicle, Ship, Defense
		InvalidTargets: EmpImmune
	Warhead@3Eff: CreateEffect
		Image: microwavehit
		Explosions: idle
		ValidTargets: Ground, Trees
		ExplosionPalette: scrineffect

MicrowaveZap.UPG:
	Inherits: MicrowaveZap
	Warhead@DriverKill: ChangeOwnerToNeutral
		ValidTargets: DriverKillLow
		InvalidTargets: DriverKillImmune
		ValidRelationships: Enemy, Ally
		CargoEffect: Kill
		Range: 0c511
	Warhead@emp: GrantExternalConditionCA
		Range: 1c0
	Warhead@emp2: GrantExternalConditionCA
		Range: 0c511
		Duration: 125
		Condition: empdisable
		ValidTargets: Vehicle, Ship, Defense
		InvalidTargets: EmpImmune
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: ffffff
		ValidTargets: DriverKillLow
		InvalidTargets: DriverKillImmune
		ValidRelationships: Enemy, Ally

HornetLauncher:
	ReloadDelay: 15
	Range: 20c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		ValidTargets: Ground, Water
		Damage: 50000
		Versus:
			None: 0
			Wood: 0
			Concrete: 0
			Light: 0
			Heavy: 0
			Brick: 0

V3Launcher:
	ReloadDelay: 250
	Range: 21c511
	MinRange: 6c0
	Report: vv3latta.aud, vv3lattb.aud
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		ValidTargets: Ground, Water
		Damage: 50000
		Versus:
			None: 0
			Wood: 0
			Concrete: 0
			Light: 0
			Heavy: 0
			Brick: 0

THTargetter:
	ReloadDelay: 250
	Range: 21c511
	MinRange: 6c0
	Projectile: InstantHit

THLauncher:
	Inherits: THTargetter
	Report: tomahawk.aud
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		Damage: 50000
		Versus:
			None: 0
			Wood: 0
			Concrete: 0
			Light: 0
			Heavy: 0
			Brick: 0

ICBMLauncher:
	ReloadDelay: 275
	Range: 25c0
	MinRange: 10c0
	Report: nukemisl.aud
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		ValidTargets: Ground, Water
		Damage: 50000
		Versus:
			None: 0
			Wood: 0
			Concrete: 0
			Light: 0
			Heavy: 0
			Brick: 0

Flare:
	ReloadDelay: 1
	Range: 10c0
	MinRange: 3c0
	ValidTargets: Structure
	TargetActorCenter: true
	Projectile: LaserZapCA
		Width: 45
		Duration: 8
		SecondaryBeamWidth: 65
		SecondaryBeamColor: FF000030
		ZOffset: 4096
	Warhead@1Dam: TargetDamage
		ValidTargets: Structure
	Warhead@2Con: GrantExternalConditionCA
		ValidTargets: Structure
		Range: 0c32
		Duration: 8
		Condition: flare

Cloud:
	ReloadDelay: 20
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 50
		Falloff: 100, 75, 52, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Damage: 350
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: Ground, Trees
		InvalidTargets: Creep
		Versus:
			None: 120
			Wood: 0
			Light: 50
			Heavy: 25
			Concrete: 100
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath

ChaosCloud:
	ReloadDelay: 25
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Cond: GrantExternalConditionCA
		ValidTargets: Ground, Vehicle
		InvalidTargets: ChaosImmune
		Range: 1c512
		Duration: 50
		Condition: berserk

VirusCloud:
	Inherits: Cloud
	-Warhead@18Radio:
	Warhead@1Dam: SpreadDamage
		Damage: 1000
		Spread: 256
		Falloff: 100, 37, 14, 5, 0
		DamageTypes: PoisonDeath
		Versus:
			Light: 35
			Wood: 10
			Concrete: 10
			Brick: 0

IrradiatedUnit:
	Inherits: Cloud
	Warhead@18Radio: CreateTintedCells
		Level: 50
		Falloff: 100, 40, 10
		LayerName: radioactivity.weak
		MaxLevel: 500
	Warhead@1Dam: SpreadDamage
		Damage: 2750
		Spread: 341
		Falloff: 100, 37, 14, 5, 0
		InvalidTargets: Desolator
		Versus:
			Light: 80
			Wood: 10
			Concrete: 10
			Brick: 0

Lasher:
	ReloadDelay: 5
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Range: 0, 341, 682
		Falloff: 100, 100, 0
		Damage: 4000
		ValidTargets: Infantry
		DamageTypes: BulletDeath, FlakVestMitigatedMinor
		ValidRelationships: Enemy, Neutral

ChaosDroneTargeting:
	Range: 2c0
	Projectile: InstantHit
	ValidTargets: Infantry, Vehicle, Ship
	InvalidTargets: ChaosImmune

MempTargeting:
	Range: 2c0
	Projectile: InstantHit
	ValidTargets: Structure, Vehicle, Ship

RadTrooperBeam:
	Range: 6c0
	ReloadDelay: 110
	Report: radbeam1.aud
	Projectile: RadBeam
		Amplitude: 176
		WaveLength: 384
		Thickness: 32
		ZOffset: 512
	Warhead@1Dam: SpreadDamage
		Spread: 136
		Damage: 7600
		Versus:
			None: 240
			Light: 100
			Heavy: 40
			Wood: 60
			Concrete: 70
			Brick: 12
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
	Warhead@3Eff: CreateEffect
		Explosions: radhitsm
		ExplosionPalette: caneon

DesolatorBeam:
	Inherits: RadTrooperBeam
	Report: idesat1a.aud
	Projectile: RadBeam
		Thickness: 48
	Warhead@1Dam: SpreadDamage
		Spread: 180
		Versus:
			Light: 120
	Warhead@2Dam: HealthPercentageDamage
		Spread: 42
		Damage: 300
		Versus:
			Light: 10
		ValidTargets: Infantry
		DamageTypes: RadiationDeath
	Warhead@2irrad: GrantExternalConditionCA
		Range: 0c256
		Duration: 250
		Condition: irradiated
		ValidTargets: Vehicle, Ship

RadBeamWeaponHeavy:
	Range: 5c768
	ReloadDelay: 80
	Report: eradcan1.aud
	Projectile: RadBeam
		Amplitude: 176
		WaveLength: 384
		Thickness: 64
		ZOffset: 512
	Warhead@1Dam: HealthPercentageDamage
		Spread: 256
		Damage: 300
		Versus:
			Light: 10
		ValidTargets: Infantry
		DamageTypes: RadiationDeath
	Warhead@2Dam: SpreadDamage
		Spread: 256
		Damage: 16000
		Versus:
			None: 175
			Wood: 45
			Concrete: 45
			Light: 75
			Heavy: 60
			Brick: 12
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
		InvalidTargets: Infantry
	Warhead@3Dam: SpreadDamage
		Spread: 256
		Damage: 3750
		Falloff: 100, 45, 25, 15, 0
		ValidTargets: Infantry
		DamageTypes: RadiationDeath
	Warhead@1Radiation: CreateTintedCells
		Spread: 1c0
		Falloff: 65, 30, 5, 0
		Level: 250
		MaxLevel: 750
		LayerName: radioactivity.medium
	Warhead@3Eff: CreateEffect
		Explosions: radhit
		ExplosionPalette: caneon
	Warhead@4Eff: CreateEffect
		Explosions: radburst

OverlordRadBeamWeapon:
	Inherits: RadBeamWeaponHeavy
	Burst: 2
	BurstDelays: 20
	Warhead@2Dam: SpreadDamage
		Spread: 256
		Damage: 13500

ApocRadBeamWeapon:
	Inherits: RadBeamWeaponHeavy
	Burst: 2
	BurstDelays: 10
	ReloadDelay: 135
	Warhead@2Dam: SpreadDamage
		Spread: 256
		Damage: 23500
	Warhead@3Dam: SpreadDamage
		Damage: 5475

RadEruptionWeapon:
	ReloadDelay: 35
	Report: idesat2a.aud
	Warhead@1Radiation: CreateTintedCells
		Spread: 1c0
		Falloff: 100, 75, 55, 30, 0
		Level: 250
		MaxLevel: 750
		LayerName: radioactivity.strong
	Warhead@2irrad: GrantExternalConditionCA
		Range: 2c0
		Duration: 250
		Condition: irradiated
		ValidTargets: Vehicle, Ship
	Warhead@3Eff: CreateEffect
		Explosions: radspike, radspike2
		ExplosionPalette: tdeffect

RadEruptionWeaponStage2:
	Inherits: RadEruptionWeapon
	Warhead@1Radiation: CreateTintedCells
		Falloff: 100, 80, 60, 40, 20, 0

PsychicBeamBATF:
	Inherits: DesolatorBeam
	ReloadDelay: 75
	Report: mastermind-fire.aud
	-Projectile:
	Projectile: PlasmaBeam
		Duration: 7
		Colors: ff00ff08
		InnerLightness: 200
		OuterLightness: 100
		Radius: 2
		Distortion: 150
		DistortionAnimation: 150
		SegmentLength: 350
		Inaccuracy: 128
	Warhead@1Dam: SpreadDamage
		Versus:
			Heavy: 35
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@2Dam: HealthPercentageDamage
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	-Warhead@2irrad:
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: enrvbolthit

^EnergyBlast:
	ReloadDelay: 50
	Range: 7c0
	Report: scrin5b.aud
	ValidTargets: Ground
	Warhead@1Dam: SpreadDamage
		ValidTargets: Ground
	Warhead@2Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: expnew17.aud, expnew16.aud
		ValidTargets: Ground, Air
	Warhead@3EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: splashm1.aud, splashm2.aud, splashm3.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure
	Warhead@4Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Structure, Wall

CyCannon:
	Inherits: ^EnergyBlast
	Projectile: Missile
		MaximumLaunchSpeed: 192
		Blockable: false
		HorizontalRateOfTurn: 8
		Shadow: true
		Image: rmbctorp
		Palette: tdeffect
		MinimumLaunchSpeed: 75
		Speed: 384
		RangeLimit: 8c0
		Jammable: false
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 13000
		Versus:
			None: 75
			Wood: 35
			Light: 100
			Heavy: 80
			Concrete: 25
			Brick: 75
		DamageTypes: Prone350Percent, TriggerProne, ExplosionDeath
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: expnew16.aud, expnew17.aud
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: 00ff00
		ValidTargets: Ground, Vehicle

NeutronCannon:
	ValidTargets: Temporal
	ReloadDelay: 5
	Burst: 22
	BurstDelays: 5
	Range: 5c0
	Projectile: InstantHit
	TargetActorCenter: true
	Warhead@Damage: WarpPercentDamage
		Damage: 2
		Spread: 1
		Versus:
			Wood: 15
			Concrete: 50
			Heavy: 90
			Light: 75
			Brick: 5
		ValidTargets: Temporal
		DamageTypes: ChronoDeath

NeutronCannonAA:
	Inherits: NeutronCannon
	ValidTargets: TemporalAir
	Warhead@Damage: WarpPercentDamage
		ValidTargets: TemporalAir
		InvalidTargets: Shielded
	Warhead@ShieldDamage: SpreadDamage
		Damage: 250
		ValidTargets: Shielded
	Warhead@ShieldWarp: GrantExternalConditionCA
		Range: 0c512
		Duration: 10
		Condition: shield-warped
		ValidTargets: Shielded

NeutronCannonBeam:
	ValidTargets: Temporal, TemporalAir
	ReloadDelay: 5
	Range: 5c0
	TargetActorCenter: true
	Projectile: LaserZapCA
		Width: 60
		Duration: 15
		Color: 80C8FF88
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 120
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: 80C8FF33
	Warhead@Damage: SpreadDamage
		Damage: 1
		Spread: 1
		Versus:
			None: 0
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
		ValidTargets: Temporal, TemporalAir
		DamageTypes: ChronoDeath

NeutronCannonPulse:
	Inherits: NeutronCannonBeam
	ReloadDelay: 20
	-Projectile:
	Projectile: AreaBeamCA
		Speed: 0c768
		Duration: 8
		Width: 60
		Shape: Flat
		ZOffset: 512
		TrackTarget: true
		Color: d8f9f7cc

NeutronCannonSound:
	Inherits: NeutronCannonBeam
	Report: chronogun.aud
	ReloadDelay: 60
	Projectile: InstantHit

SonicPulse:
	ReloadDelay: 68
	Range: 7c512
	Report: sonicpulse1.aud, sonicpulse2.aud
	TargetActorCenter: true
	Projectile: LinearPulse
		Speed: 768
		MinimumImpactDistance: 768
	Warhead@1Dam: SpreadDamage
		Spread: 0c256
		Falloff: 100, 75, 25, 0
		Damage: 6600
		ValidTargets: Ground, Water
		ValidRelationships: Enemy, Neutral
		Versus:
			None: 45
			Wood: 45
			Light: 90
			Heavy: 100
			Concrete: 70
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: sonicblast1, sonicblast2
		ExplosionPalette: effect-ignore-lighting-alpha40
		ValidTargets: Ground, Air, Ship, Trees
	Warhead@3Eff: CreateEffect
		Explosions: sonicpulse1, sonicpulse2
		ExplosionPalette: effect-ignore-lighting-alpha40
		ValidTargets: Ground, Air, Ship, Trees

SonicPulse.UPG:
	Inherits: SonicPulse
	Range: 8c256
	Warhead@1Dam: SpreadDamage
		Damage: 7200
	Warhead@2Eff: CreateEffect
		Explosions: sonicblastupg1, sonicblastupg2
	Warhead@3Eff: CreateEffect
		Explosions: sonicpulseupg1, sonicpulseupg2
	Warhead@ConcussionVehicles: GrantExternalConditionCA
		Range: 1c0
		Duration: 150
		Condition: concussion
		ValidTargets: Vehicle, Ship
		ValidRelationships: Enemy, Neutral
	Warhead@ConcussionInfantry: GrantExternalConditionCA
		Range: 1c0
		Duration: 25
		Condition: concussion
		ValidTargets: Infantry
		ValidRelationships: Enemy, Neutral

DisruptorPulse:
	Inherits: SonicPulse
	ReloadDelay: 80
	Range: 5c256
	-Report:
	StartBurstReport: sonic4.aud
	Burst: 9
	BurstDelays: 5
	Projectile: LinearPulse
		Speed: 384
	Warhead@1Dam: SpreadDamage
		Spread: 0c384
		Damage: 750
		Versus:
			None: 100
			Wood: 60
			Light: 28
			Heavy: 12
			Concrete: 35
	Warhead@friendlyFire: SpreadDamage
		Spread: 0c384
		Falloff: 100, 75, 25, 0
		Damage: 150
		Versus:
			None: 100
			Wood: 60
			Light: 28
			Heavy: 12
			Concrete: 45
		ValidRelationships: Ally
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		InvalidTargets: Disruptor
		DebugOverlayColor: ffaa00
	-Warhead@2Eff:
	Warhead@2Eff: CreateFacingEffect
		Explosions: sonicwave1, sonicwave2
		ExplosionPalette: effect
		ValidTargets: Ground, Air, Ship, Trees
	-Warhead@3Eff:

DisruptorPulse.Amp:
	Inherits: DisruptorPulse
	Range: 6c0
	Warhead@1Dam: SpreadDamage
		Damage: 825
	Warhead@2Eff: CreateFacingEffect
		Explosions: sonicwaveupg1, sonicwaveupg2
		ExplosionPalette: effect
	Warhead@4Conc: GrantExternalConditionCA
		Range: 0c768
		Duration: 75
		Condition: concussion
		ValidTargets: Ground, Infantry, Vehicle, Ship
		ValidRelationships: Enemy, Neutral

DisruptorPulse.Seek3:
	Inherits: DisruptorPulse
	Range: 5c640

DisruptorPulse.Amp.Seek3:
	Inherits: DisruptorPulse.Amp
	Range: 6c384

JDAM:
	ValidTargets: Ground, Water, Trees
	ReloadDelay: 50
	Report: bwhis.aud
	Range: 3c0
	MinRange: 2c0
	TargetActorCenter: true
	Projectile: GravityBomb
		Image: MOAB_BOMB
		Velocity: 75, 0, -66
		Acceleration: 0, 0, 0
		Shadow: true
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 5200
		Falloff: 1000, 448, 192, 50, 0
		ValidTargets: Ground, Water
		Versus:
			None: 0
			Wood: 80
			Light: 80
			Heavy: 100
			Concrete: 80
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster, AirToGround
	Warhead@2Dam: SpreadDamage
		Spread: 448
		Damage: 10000
		Falloff: 1000, 200, 45, 20, 0
		ValidTargets: Ground, Water
		Versus:
			None: 100
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, FlakVestMitigated, AirToGround
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
	Warhead@3Eff: CreateEffect
		Explosions: fuelbomb1, fuelbomb2
		ImpactSounds: xplobig4.aud
	Warhead@Flames: FireCluster
		InvalidTargets: Water
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@Concussion: GrantExternalConditionCA
		Range: 3c0
		Duration: 70
		Condition: concussion
		ValidTargets: Ground, Infantry, Vehicle, Ship
	Warhead@5Shake: ShakeScreen
		Duration: 5
		Intensity: 1
		Multiplier: 0.5,0.5
	Warhead@Shrap: FireShrapnel
		Weapon: TinyDebris
		Amount: 10
		AimChance: 0
		ValidTargets: Ground, Infantry, Vehicle
		ThrowWithoutTarget: true

HarvSwap:
	ReloadDelay: 500
	Projectile: InstantExplode
	Warhead@2Eff: CreateEffect
		Explosions: chronowarp_effect
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ValidTargets: Ground, Air, Water
		ImpactSounds: chrono2.aud
	Warhead@3Flash: ChronoFlashEffect

U2Camera:
	ValidTargets: Ground, Water, Trees
	ReloadDelay: 65
	Range: 2c0
	Report: cspydram.aud
	TargetActorCenter: true
	Projectile: InstantHit

KillZoneSpawner:
	Range: 1c0
	ReloadDelay: 100
	Report: chute1.aud
	Projectile: InstantHit
	Warhead@Spawn: SpawnActor
		Delay: 40
		Actors: killzone
		Range: 1
		ImpactActors: false

KillZoneFlare:
	Range: 1c0
	ReloadDelay: 100
	FirstBurstTargetOffset: -3072, 0, 0
	Projectile: Bullet
		LaunchAngle: 0
		Speed: 100
		Image: BOMB
		ContrailLength: 12
		ContrailStartColorUsePlayerColor: true
		ContrailEndColorUsePlayerColor: true
		ContrailStartColorAlpha: 128
		ContrailStartWidth: 64
		Shadow: true
	Warhead@Spawn: SpawnActor
		Actors: flare.killzone
		Range: 1
		Delay: 5
		ImpactActors: false

KillZoneFlare2:
	Inherits: KillZoneFlare
	FirstBurstTargetOffset: 1550,2720,0
	Warhead@Spawn: SpawnActor
		-Delay:

KillZoneFlare3:
	Inherits: KillZoneFlare
	FirstBurstTargetOffset: 1550,-2720,0
	Warhead@Spawn: SpawnActor
		-Delay:

DummyWeapon:
	Warhead@Dummy: Dummy

Lasher:
	ReloadDelay: 5
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Range: 0, 341, 682
		Falloff: 100, 100, 0
		Damage: 350
		ValidTargets: Infantry
		DamageTypes: BulletDeath
		ValidRelationships: Enemy, Neutral

ShadowBeaconLauncher:
	ReloadDelay: 25
	Range: 4c0
	Report: grenade1.aud
	Projectile: Bullet
		Blockable: false
		LaunchAngle: 62
		Speed: 180
		Image: BOMB
		ContrailLength: 30
		ContrailStartColor: 8888aa
		ContrailStartColorAlpha: 100
	Warhead@Beacon: SpawnActor
		Actors: shab
		Range: 1
		ImpactActors: false

AttachShadowBeacon:
	Warhead@AttachCamera: AttachActor
		Range: 341
		Actor: shadow.beacon.camera
		AttachSounds: shad-beacontrigger1.aud
		MissSounds: dud1.aud
		ValidTargets: ShadowBeaconAttachable
	Warhead@TargetValidator: SpreadDamage
		ValidTargets: ShadowBeaconAttachable
	Warhead@Flash: FlashTarget
		Spread: 341
		Color: ff0000
		ValidTargets: ShadowBeaconAttachable

^Debris:
	ValidTargets: Ground, Water, Trees, Air
	Range: 5c0
	Projectile: Bullet
		Speed: 55, 108
		LaunchAngle: 90, 192
		Image: dbrissm
		Palette: tseffect
		Sequences: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10
		Shadow: true
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 200
		Falloff: 100, 100, 0
		Damage: 1000
		Versus:
			None: 100
			Wood: 85
			Light: 70
			Heavy: 35
			Concrete: 28
		DamageTypes: Prone100Percent, TriggerProne, ExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: small_explosion, small_explosion_alt1, small_explosion_alt2, small_explosion_alt3
		ValidTargets: Ground, Air
		ImpactSounds: kaboom25.aud, kaboom12.aud, xplos.aud
	Warhead@3EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: splash9.aud
		ValidTargets: Water
		InvalidTargets: Vehicle
	Warhead@4Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Structure, Wall, Trees

TinyDebris:
	Inherits: ^Debris
	ValidTargets: Ground, Trees
	Projectile: Bullet
		Image: dirt
		Palette: terrain
		Sequences: 1, 2, 3, 4, 5
		BounceCount: 2
		BounceRangeModifier: 10
		InvalidBounceTerrain: Water
	Warhead@1Dam: SpreadDamage
		Damage: 1
	-Warhead@2Eff:
	-Warhead@3EffWater:
	-Warhead@4Smu:

BrassDebris:
	ReloadDelay: 10
	Range: 0c1
	ValidTargets: Infantry, Vehicle, Building, Wall, Ground, Water, Air
	Projectile: ProjectileHusk
		Velocity: 50, 0, 100
		VelocityRandomFactor: 0, 15, 30
		Acceleration: 0, 0, -30
		HorizontalRevert: true
		Image: brass
		Sequences: 1, 2, 3, 4, 5
		UseRangeModifierAsVelocityX: false

BrassDebrisAir:
	Inherits: BrassDebris
	ReloadDelay: 10
	Projectile: ProjectileHusk
		Velocity: 50, 0, 10
		VelocityRandomFactor: 0, 15, 30

SmallDebris:
	Inherits: ^Debris

FireDebris:
	Inherits: ^Debris
	Range: 3c0
	Projectile: Bullet
		Speed: 55, 108
		LaunchAngle: 90, 192
		Image: firetrail
		TrailImage: smokey
		TrailDelay: 1
		Palette: effect
		TrailPalette: effect
		-Sequences:
		Shadow: true
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 200
		Falloff: 100, 100, 0
		Damage: 1000
		Versus:
			None: 100
			Wood: 85
			Light: 70
			Heavy: 35
			Concrete: 28
		DamageTypes: Prone100Percent, TriggerProne, PoisonDeath
	Warhead@2Eff: CreateEffect
		ValidTargets: Ground, Air, Water
		Explosions: small_napalm
		ExplosionPalette: effect
		ImpactSounds: firebl3.aud
	-Warhead@3EffWater:
	Warhead@4Smu: LeaveSmudge
		SmudgeType: Scorch

ChemDebris:
	Inherits: ^Debris
	Range: 3c0
	Projectile: Bullet
		Speed: 55, 108
		LaunchAngle: 90, 192
		Image: tibglob
		Palette: scrin-ignore-lighting-alpha85
		TrailImage: tibspew1
		TrailPalette: scrin-ignore-lighting-alpha85
		-Sequences:
		Shadow: true
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 200
		Falloff: 100, 100, 0
		Damage: 1000
		Versus:
			None: 100
			Wood: 85
			Light: 70
			Heavy: 35
			Concrete: 28
		DamageTypes: Prone100Percent, TriggerProne, PoisonDeath
	Warhead@2Eff: CreateEffect
		ValidTargets: Ground, Air, Water
		Explosions: small_chem
		ExplosionPalette: tdeffect
		ImpactSounds: vtoxcona.aud, vtoxconb.aud, vtoxconc.aud
	-Warhead@3EffWater:
	Warhead@4Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
	Warhead@5Radio: CreateTintedCells
		Level: 300
		Falloff: 32, 5
		LayerName: radioactivity.weak

ChemDebrisSmall:
	Inherits: ChemDebris
	Range: 2c0
	-Warhead@5Radio:
	Warhead@1Dam: SpreadDamage
		Spread: 10

AirDummyAim:
	Range: 7c0
	ReloadDelay: 20
	ValidTargets: Air
	Projectile: InstantHit

YuriDummyAim:
	Range: 3c0
	ReloadDelay: 20
	ValidTargets: Structure
	Projectile: InstantHit

DropDummy:
	ValidTargets: Ground
	InvalidTargets: Infantry, Ship, Tank, Structure, Water Structure, Air
	ReloadDelay: 50
	Range: 4c0
	Projectile: Bullet
		Speed: 200

MineDefuser:
	Range: 1c512
	ReloadDelay: 6
	ValidTargets: Mine
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Damage: 12000
		ValidTargets: Mine
		Delay: 5
	Warhead@1Def: GrantExternalConditionCA
		Condition: defused
		Range: 0c511
		Duration: 25
		ValidTargets: Mine
	Warhead@3Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: mineblo1.aud

MineDefuserCharger:
	Inherits: MineDefuser
	-Warhead@1Dam:
	-Warhead@1Def:
	-Warhead@3Eff:

DecoyFlameTankSpawner:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@Spawn: SpawnActor
		Actors: ftnk.decoy, ftnk.decoy
		Range: 3
		ForceGround: false
		MatchSourceFacing: true
		ValidTargets: Ground, Water
		ImpactActors: false

DecoyHeavyFlameTankSpawner:
	Inherits: DecoyFlameTankSpawner
	Warhead@Spawn: SpawnActor
		Actors: hftk.decoy, hftk.decoy

DecoyDespawn:
	Report: decoydespawn.aud
	Projectile: InstantHit

MantisLaser:
	Inherits: Laser
	Burst: 2
	BurstDelays: 3
	ReloadDelay: 10
	ValidTargets: Air, AirSmall
	Range: 7c512
	Report: mantis-fire1.aud, mantis-fire2.aud
	Projectile: LaserZapCA
		Width: 42
		SecondaryBeamWidth: 75
		-HitAnim:
	Warhead@1Dam: SpreadDamage
		ValidTargets: Air, AirSmall
		Damage: 2500
	Warhead@3Eff: CreateEffect
		Explosions: small_air_explosion
		Inaccuracy: 256
		ValidTargets: Air, AirSmall

ViperLaser:
	Inherits: DevourerLaser
	Report: viper-fire1.aud
	Projectile: PlasmaBeam
		Colors: ff0000E6, cc0000E6
	Warhead@1Dam: SpreadDamage
		Damage: 1550

AvatarLaser:
	ReloadDelay: 100
	ValidTargets: Ground, Water
	Range: 5c768
	Report: oblfire.aud
	Projectile: LaserZapCA
		Width: 45
		Duration: 18
		HitAnim: laserfire
		Color: ff7143cc
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 90
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: FF000099
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 26000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 250
			Wood: 65
			Concrete: 60
			Brick: 50
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep
	Warhead@3Eff: CreateEffect
		Image: laserhit
		Explosions: idle1, idle2
		ExplosionPalette: caneon
		ValidTargets: Ground, Ship, Trees

AvatarLaser.Adv:
	Inherits: Laser
	Report: obelmod2.aud
	Range: 6c0
	-Projectile:
	Projectile: PlasmaBeam
		Duration: 9
		SecondaryCenterBeam: true
		Colors: FF00BB80, FF0000A0, FF002270
		InnerLightness: 200
		OuterLightness: 115
		Radius: 2
		Distortion: 0
		DistortionAnimation: 96
		StartOffset: 0,80,0
		FollowingOffset: 0,-20,0
		RecalculateColors: true
		TrackTarget: true
		ImpactTicks: 0, 2, 4, 6, 8
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 5500
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion, small_explosion_alt1, small_explosion_alt2, small_explosion_alt3

ShadeEmp:
	ReloadDelay: 100
	Range: 7c0
	Report: enli-empfire.aud
	Projectile: Bullet
		Inaccuracy: 64
		Blockable: false
		Shadow: true
		Speed: 384
		LaunchAngle: 0
		Image: enliempproj
		Palette: effect
		TrailImage: smokey
		TrailPalette: scrinplasma
		ContrailLength: 17
		ContrailStartColor: 6c6cd4aa
		ContrailStartColorAlpha: 170
		ContrailStartWidth: 0c48
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 17000
		ValidTargets: Ground, Water
		Versus:
			None: 75
			Wood: 50
			Light: 80
			Heavy: 100
			Concrete: 75
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath, AirToGround
	Warhead@1Emp: GrantExternalConditionCA
		Range: 1c0
		Duration: 225
		Condition: empdisable
		ValidTargets: Vehicle, Ship, Defense
		InvalidTargets: Cyborg, EmpImmune
	Warhead@2Emp: GrantExternalConditionCA
		Range: 1c0
		Duration: 100
		Condition: empdisable
		ValidTargets: Cyborg
		InvalidTargets: EmpImmune
	Warhead@3Eff_impact: CreateEffect
		Explosions: enliemphit1, enliemphit2
		ImpactSounds: enli-emphit.aud
		Inaccuracy: 341

ManticoreBolts:
	Inherits: ^AirToAirMissile
	Range: 14c0
	MinRange: 3c0
	Report: mcor-fire1.aud, mcor-fire2.aud
	ReloadDelay: 150
	Burst: 4
	BurstDelays: 3
	ValidTargets: Air, AirSmall
	TargetActorCenter: true
	Projectile: Missile
		Image: redplasmatorp
		Palette: caneon
		TrailImage: smokey
		TrailPalette: scrinplasma
		Speed: 360
		Blockable: false
		Jammable: false
		RangeLimit: 16c0
		ShadowColor: 00000033
		HorizontalRateOfTurn: 28
		VerticalRateOfTurn: 28
		CruiseAltitude: 0
	Warhead@1Dam: SpreadDamage
		Range: 0, 0c64, 0c256, 1c768
		Damage: 4000
	Warhead@smallDamage: SpreadDamage
		Damage: 4000

VertigoBomb:
	Inherits: JDAM
	Report: vert-bomb1.aud
	Warhead@3Eff: CreateEffect
		ImpactSounds: vert-bombhit1.aud, vert-bombhit2.aud
	Warhead@1Dam: SpreadDamage
		Versus:
			Wood: 80
			Concrete: 100
	-Warhead@Concussion:
	Warhead@StealthBreaker: GrantExternalConditionCA
		Range: 6c0
		Duration: 1125
		Condition: cloak-force-disabled
		ValidRelationships: Enemy, Neutral

VertigoBombTargeter:
	Range: 7c0
	MinRange: 5c0
	ReloadDelay: 50
	Projectile: InstantHit

PeacemakerBombs:
	Range: 1c832
	Report: bwhis.aud
	Burst: 5
	BurstDelays: 6
	TargetActorCenter: true
	Projectile: GravityBomb
		Image: medbomb
		Velocity: 20, 0, -60
		Acceleration: 0, 0, 0
		Shadow: true
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 24000
		Versus:
			None: 10
			Wood: 0
			Light: 70
			Heavy: 100
			Concrete: 55
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster, AirToGround, FlakVestMitigated
	Warhead@2Dam: SpreadDamage
		Spread: 512
		Damage: 24000
		Versus:
			None: 40
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, FlakVestMitigated
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
	Warhead@3Eff: CreateEffect
		Explosions: building, building2
		ImpactSounds: kaboom15.aud, kaboom12.aud
		ValidTargets: Ground, Ship, Trees
	Warhead@4EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: h2obomb2.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure
	Warhead@Shrap: FireShrapnel
		Weapon: TinyDebris
		Amount: 10
		AimChance: 0
		ValidTargets: Ground, Infantry, Vehicle
		ThrowWithoutTarget: true

PeacemakerBombsBuilding:
	Inherits: PeacemakerBombs
	-Report:
	-Projectile:
	Projectile: InstantHit
	TargetActorCenter: false
	Warhead@1Dam: SpreadDamage
		Delay: 35
		Versus:
			None: 0
			Wood: 40
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
		DebugOverlayColor: ffaa00
	-Warhead@2Dam:
	-Warhead@2Smu:
	-Warhead@3Eff:
	-Warhead@4EffWater:
	-Warhead@Shrap:

FloatingDiscLaser:
	Inherits: Laser
	ValidTargets: Ground, Water, Air, AirSmall
	ReloadDelay: 60
	Range: 6c0
	Report: vfloatta.aud
	Projectile: LaserZapCA
		Color: eb39d380
		SecondaryBeamColor: eb39d340
	Warhead@1Dam: SpreadDamage
		ValidTargets: Ground, Water, Air, AirSmall
		Damage: 13000
		Versus:
			None: 100
			Light: 75
			Wood: 100
			Heavy: 75
			Concrete: 75
			Brick: 25
			Aircraft: 35
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, AirToGround

^FloatingDiscDrainer:
	ReloadDelay: 10
	Range: 0c512
	Projectile: InstantHit
	TargetActorCenter: true

FloatingDiscPowerDrainer:
	Inherits: ^FloatingDiscDrainer
	ValidTargets: PowerDrainable
	Warhead@PowerDrain: GrantExternalConditionCA
		Range: 0c128
		Duration: 11
		Condition: discpowerdrain
		ValidRelationships: Enemy
		ValidTargets: PowerDrainable

FloatingDiscResourceDrainer:
	Inherits: ^FloatingDiscDrainer
	ValidTargets: ResourceDrainable
	Warhead@ResourceDrain: GrantExternalConditionCA
		Range: 0c128
		Duration: 11
		Condition: resourcedrain
		ValidRelationships: Enemy, Neutral
		ValidTargets: ResourceDrainable

ZoneTrooperRailgun:
	ReloadDelay: 65
	Range: 6c0
	Report: ztrp-fire1.aud, ztrp-fire2.aud, ztrp-fire3.aud
	Projectile: RailgunCA
		Duration: 10
		Blockable: true
		DamageActorsInLine: false
		BeamColor: 00FFFFC8
		BeamWidth: 40
		HelixThickness: 16
		HelixRadius: 32
		HitAnim: explosion
		HitAnimSequence: small_explosion
		ZOffset: 2046
	Warhead@1Dam: SpreadDamage
		Spread: 0c64
		Damage: 10000
		Versus:
			None: 30
			Wood: 50
			Light: 85
			Heavy: 100
			Concrete: 75
			Brick: 75
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath

ZoneRaiderGrenade:
	ReloadDelay: 60
	Range: 6c0
	Report: zrai-fire1.aud, zrai-fire2.aud
	Projectile: Bullet
		Speed: 700
		Image: BOMB
		Shadow: true
		Inaccuracy: 554
		LaunchAngle: 75
		Blockable: false
		ContrailLength: 30
		ContrailStartColor: 5bd8c3
		ContrailStartColorAlpha: 128
	Warhead@1Dam: SpreadDamage
		Spread: 384
		Damage: 8500
		Versus:
			None: 35
			Wood: 90
			Light: 100
			Heavy: 45
			Concrete: 50
			Brick: 80
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath, FlakVestMitigated
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
	Warhead@3Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: kaboom25.aud
		ValidTargets: Ground, Ship, Trees
	Warhead@4EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: splashm1.aud, splashm2.aud, splashm3.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

ZoneDefenderGun:
	ReloadDelay: 70
	Range: 6c0
	Report: ionrifle1.aud, ionrifle2.aud
	Projectile: LaserZap
		Width: 38
		Duration: 8
		Color: FFFFFFEE
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 80
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: FFFFFF66
	Warhead@1Dam: SpreadDamage
		Spread: 288
		Damage: 8250
		Versus:
			None: 18
			Wood: 50
			Light: 85
			Heavy: 100
			Concrete: 75
			Brick: 75
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@3Eff: CreateEffect
		Explosions: shock_wave_sm
		ExplosionPalette: tseffect
	Warhead@4Smu_impact: LeaveSmudge
		SmudgeType: Scorch

CryoSprayer:
	Range: 5c768
	ReloadDelay: 5
	Burst: 1
	TargetActorCenter: true
	Projectile: LinearPulse
		Speed: 341
		MinimumImpactDistance: 0
		MaximumImpactDistance: 5456
	Warhead@1Dam: SpreadDamage
		Spread: 0c128
		Falloff: 100, 75, 25, 0
		Damage: 400
		Versus:
			None: 100
			Wood: 2
			Light: 15
			Heavy: 10
			Concrete: 2
			Brick: 0
		ValidRelationships: Enemy, Neutral
		DamageTypes: Prone50Percent, TriggerProne, FrozenDeath
	Warhead@2Eff: CreateFacingEffect
		Explosions: cryobeam1, cryobeam2
		ExplosionPalette: effect
		ValidTargets: Ground, Air, Ship, Trees
		Inaccuracy: 110
	Warhead@chill1: GrantExternalConditionCA
		Condition: chilled
		Duration: 75
		Range: 0c341
		ValidRelationships: Enemy, Neutral

ZeusCloud:
	ReloadDelay: 150
	Range: 21c511
	Report: sweastra.aud, sweastrd.aud
	Projectile: AthenaProjectile
		Altitude: 5c768
	Warhead@1: FireFragment
		UseZOffsetAsAbsoluteHeight: true
		Weapon: ZeusBolt
		ValidTargets: Air, Ground, Water
		Delay: 28
		ImpactActors: false
	Warhead@TargetValidation: SpreadDamage
	Warhead@Cloud: CreateEffect
		Explosions: weathercloud1, weathercloud2, weathercloud1f, weathercloud2f
		ExplosionPalette: ra2unit
		ValidTargets: Ground, Air, Water
		Inaccuracy: 341
	Warhead@Shadow: FireFragment
		UseZOffsetAsAbsoluteHeight: true
		Weapon: ZeusShadow
		ValidTargets: Ground, Air, Water
		ImpactActors: false

ZeusShadow:
	Projectile: InstantHit
	Range: 512
	Warhead@Shadow: CreateEffect
		Explosions: weathercloudshadow
		ValidTargets: Ground, Air, Water
		Inaccuracy: 341

ZeusBolt:
	Projectile: InstantHit
	Range: 512
	Warhead@1Dam: SpreadDamage
		Spread: 384
		Falloff: 1000, 448, 192, 50, 18, 7, 0
		Damage: 3500
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath, FlakVestMitigatedMinor
		Versus:
			None: 85
			Wood: 100
			Concrete: 65
			Brick: 50
			Heavy: 70
			Light: 80
	Warhead@4: CreateEffect
		Explosions: weatherbolt1, weatherbolt2, weatherbolt3, weatherbolt1f, weatherbolt2f, weatherbolt3f
		ImpactSounds: sweastrb.aud, sweastrc.aud
		ValidTargets: Ground, Air, Water
		ExplosionPalette: ra2effect-ignore-lighting-alpha90
		Inaccuracy: 256
	Warhead@5: CreateEffect
		Explosions: large_explosion
		ValidTargets: Ground, Water
	Warhead@6Smu: LeaveSmudge
		SmudgeType: Scorch

BasiliskPulse:
	ReloadDelay: 60
	Burst: 3
	BurstDelays: 5
	Range: 8c0
	StartBurstReport: basiwave.aud
	TargetActorCenter: true
	Projectile: LinearPulse
		Speed: 512
		MinimumImpactDistance: 512
		Image: sparks_overlay
		Sequences: idle
		Palette: tseffect-ignore-lighting-alpha75
	Warhead@1Dam: SpreadDamage
		Spread: 0c256
		Falloff: 100, 75, 25, 0
		Damage: 500
		Versus:
			None: 0
			Wood: 0
			Light: 100
			Heavy: 100
			Concrete: 100
			Brick: 0
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		InvalidTargets: EmpImmune
	Warhead@2Eff: CreateFacingEffect
		Explosions: basiwave
		ExplosionPalette: scrin
		Inaccuracy: 80
		ValidTargets: Ground, Air, Ship, Trees
	Warhead@emp: GrantExternalConditionCA
		Range: 0c511
		Duration: 100
		Condition: empdisable
		ValidTargets: Vehicle, Ship, Defense
		InvalidTargets: EmpImmune

CyclopsZap:
	Inherits: TTrackZap
	Range: 8c0

JackknifeTargeter:
	Range: 8c0
	ValidTargets: Ground, Water
	MinRange: 0c768
	ReloadDelay: 25
	Projectile: InstantHit

TroopCrawlerDummyWeapon:
	Range: 1c0
	ValidTargets: Infantry
	ReloadDelay: 25
	Projectile: InstantHit

TargetPainter:
	Range: 13c0
	ReloadDelay: 25
	ValidTargets: Vehicle, Ship, Structure
	Report: targetpainterbeep.aud
	TargetActorCenter: true
	Projectile: ArcLaserZap
		Color: 55555560
		Angle: 70
		Width: 70
		Duration: 20
		HitAnim: empty
	Warhead@UnitReveal: RevealShroud
		Duration: 28
		Radius: 5c0
		InvalidTargets: Structure
	Warhead@BuildingReveal: RevealShroud
		Duration: 28
		Radius: 3c0
		ValidTargets: Structure
	Warhead@Cond: GrantExternalConditionCA
		Range: 0c42
		Duration: 28
		Condition: painted-target
		ValidTargets: Vehicle, Ship, Structure
