Background@ENCYCLOPEDIA_PANEL:
	Logic: EncyclopediaLogicCA
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 900
	Height: 600
	Children:
		Container@ENCYCLOPEDIA_CONTENT:
			Width: PARENT_WIDTH - 40
			Height: PARENT_HEIGHT - 40
			X: 20
			Y: 20
			Children:
				Label@ENCYCLOPEDIA_TITLE:
					Width: PARENT_WIDTH
					Height: 25
					Text: Encyclopedia
					Align: Center
					Font: Bold
				Container@ENCYCLOPEDIA_TABS:
					X: 0
					Y: 25
					Width: PARENT_WIDTH
					Height: 30
					Children:
						Button@ENCYCLOPEDIA_TAB:
							Width: 100
							Height: 30
							Font: Bold
							Visible: false
				ScrollPanel@ACTOR_LIST:
					Y: 60
					Width: 250
					Height: PARENT_HEIGHT - 25 - 60 - 10
					Children:
						ScrollItem@HEADER:
							Background: scrollheader
							Width: PARENT_WIDTH - 27
							Height: 26
							X: 2
							Visible: false
							Children:
								Image@ICON:
									X: 4
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: scrollpanel-decorations
									ImageName: right
								Label@LABEL:
									Font: Regular
									Width: PARENT_WIDTH
									X: 24
									Height: 26
									Align: Left
						ScrollItem@TEMPLATE:
							Width: PARENT_WIDTH - 27
							Height: 26
							X: 2
							EnableChildMouseOver: True
							Children:
								Image@ICON:
									X: 4
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: mission-completion-tick
									ImageName: incomplete
								LabelWithTooltip@TITLE:
									X: 26
									Width: PARENT_WIDTH - 26 - 20
									Height: 26
									TooltipContainer: TOOLTIP_CONTAINER
									TooltipTemplate: SIMPLE_TOOLTIP
				Container@ACTOR_INFO:
					X: PARENT_WIDTH - WIDTH
					Y: 60
					Width: PARENT_WIDTH - 250 - 10
					Height: PARENT_HEIGHT - 25 - 60 - 10
					Children:
						ScrollPanel@ACTOR_DESCRIPTION_PANEL:
							Width: PARENT_WIDTH - 148 - 10
							Height: PARENT_HEIGHT
							TopBottomSpacing: 8
							Background: observer-scrollpanel-button-pressed
							Children:
								Label@ACTOR_TITLE:
									X: 8
									Width: PARENT_WIDTH - 40
									Height: 20
									VAlign: Top
									Font: Bold
								Container@ACTOR_PRODUCTION:
									X: 8
									Width: PARENT_WIDTH - 40
									Height: 26
									Children:
										Image@NOT_PRODUCIBLE_ICON:
											X: 0
											Y: 1
											Width: 16
											Height: 16
											ImageCollection: order-icons
											ImageName: cancel
										Label@NOT_PRODUCIBLE:
											X: 0
											Height: 16
											Font: Bold
											Text: Not Producible
											TextColor: a58770
										Image@COST_ICON:
											Y: 1
											Width: 16
											Height: 16
											ImageCollection: sidebar-bits
											ImageName: production-tooltip-cost
										Label@COST:
											X: 17
											Height: 16
											Font: Bold
										Image@TIME_ICON:
											X: 75
											Y: 1
											Width: 16
											Height: 16
											ImageCollection: sidebar-bits
											ImageName: production-tooltip-time
										Label@TIME:
											X: 95
											Height: 16
											Font: Bold
										Image@ARMOR_TYPE_ICON:
											X: 160
											Y: 1
											Width: 16
											Height: 16
											ImageCollection: sidebar-bits
											ImageName: production-tooltip-armor
										Label@ARMOR_TYPE:
											X: 179
											Height: 16
											Font: Bold
										Image@POWER_ICON:
											X: 245
											Y: 1
											Width: 16
											Height: 16
											ImageCollection: sidebar-bits
											ImageName: production-tooltip-power
										Label@POWER:
											X: 262
											Height: 16
											Font: Bold
								Container@ACTOR_DETAILS:
									X: 8
									Width: PARENT_WIDTH - 40
									Children:
										Image@SUBFACTION_FLAG:
											X: 0
											Y: 0
											Width: 30
											Height: 15
											ImageCollection: flags
										Label@SUBFACTION:
											X: 36
											Width: PARENT_WIDTH - 25
											VAlign: Top
											Font: Regular
											TextColor: BBBBBB
										Label@ADDITIONAL_INFO:
											X: 0
											Width: PARENT_WIDTH
											VAlign: Top
											Font: Regular
											TextColor: BBBBBB
										Label@ACTOR_PREREQUISITES:
											X: 0
											Width: PARENT_WIDTH
											VAlign: Top
											Font: Regular
										Label@ACTOR_DESCRIPTION:
											X: 0
											Width: PARENT_WIDTH
											VAlign: Top
											Font: Regular
										Label@STRENGTHS:
											X: 0
											Width: PARENT_WIDTH
											Font: Regular
											TextColor: 33DD33
										Label@WEAKNESSES:
											X: 0
											Width: PARENT_WIDTH
											Font: Regular
											TextColor: EE5555
										Label@ATTRIBUTES:
											X: 0
											Width: PARENT_WIDTH
											Font: Regular
											TextColor: FFFF00
										Label@ENCYCLOPEDIA_DESCRIPTION:
											X: 0
											Width: PARENT_WIDTH
											Font: Regular
						Background@ACTOR_BG:
							X: PARENT_WIDTH - 148
							Width: 148
							Height: 170
							Background: observer-scrollpanel-button-pressed
							Children:
								ActorPreview@ACTOR_PREVIEW:
									X: 1
									Y: 1
									Width: PARENT_WIDTH - 2
									Height: PARENT_HEIGHT - 2
									Animate: True
						Sprite@BUILD_ICON:
							X: PARENT_WIDTH - 148 + 42
							Y: 60 + 170 - 50
							Width: 64
							Height: 48
				Button@BACK_BUTTON:
					Y: PARENT_HEIGHT - 30
					Width: 140
					Height: 30
					Text: button-back
					Key: escape

		TooltipContainer@TOOLTIP_CONTAINER:
