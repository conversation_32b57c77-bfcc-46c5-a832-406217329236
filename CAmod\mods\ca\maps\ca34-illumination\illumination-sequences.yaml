kane:
	Inherits: ^CommonDeaths
	stand:
		Filename: kane.shp
		Facings: 8
	run:
		Filename: kane.shp
		Start: 8
		Length: 6
		Facings: 8
		Tick: 100
	parachute:
		Filename: kane.shp
		Start: 3
	shoot:
		Filename: yuria.shp
		Length: 2
		Tick: 140
		Facings: 8
	die1:
		Filename: kane.shp
		Start: 139
		Length: 8
	die2:
		Filename: kane.shp
		Start: 147
		Length: 8
	die3:
		Filename: kane.shp
		Start: 155
		Length: 8
	die4:
		Filename: kane.shp
		Start: 163
		Length: 12
	die5:
		Filename: kane.shp
		Start: 175
		Length: 18
	die7:
		Filename: kane.shp
		Start: 139
		Length: 8
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	mind-overlay:
		Filename: mindanim.shp
		Length: *
		Tick: 200
		BlendMode: Alpha
		Offset: 0, 0

fragment:
	idle:
		Filename: fragment.shp
		Length: *
		BlendMode: Additive
	die:
		Filename: enrvbolthit.shp
		BlendMode: Additive
		Length: *
		ZOffset: 2047

pips:
	pip-nod:
		Filename: pip-nod.shp
		Offset: -4, -6
