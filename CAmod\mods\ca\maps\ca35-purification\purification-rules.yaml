^Palettes:
	PaletteFromFile@terrain-temperat:
		Filename: alien.pal
	TintPostProcessEffect:
		Red: 1.3
		Green: 1
		Blue: 1.5
		Ambient: 0.5
	FlashPostProcessEffect@PURIFICATION:
		Type: Purification

^BaseWorld:
	TerrainLighting:

World:
	LuaScript:
		Scripts: campaign.lua, purification.lua
	MissionData:
		Briefing: The final phase of our mission here begins. I have always known that Tiberium was the key to unlocking humanity's potential, however my understanding was incomplete. The green crystal now so familiar to us is in fact a corrupted abomination.\n\nMillennia ago, the Scrin discovered Tiberium on this world. It heralded a glorious age of progress and peace, as wars over resources and territory faded away. However this earlier form of the crystal did not have the same propensity to grow and spread, and scarcity threatened to undo the progress that had been made.\n\nExperiments to increase the rate at which Tiberium propagates were conducted, and were ultimately successful, but the result was not without its drawbacks. The crystal grew much more rapidly, but emitted a type of radiation that led to changes in Scrin physiology. Exposure led to dependence and addiction.\n\nThere were those who felt the risks were too great, however their voices were silenced. The green crystal allowed Scrin civilization to progress, but there were those who saw its potential as a tool of control. To be deprived of Tiberium would now often mean death to the majority of the Scrin, so those who controlled its production had great power. Eventually, a supreme leader rose to power - the Overlord - who has ruled the Scrin ever since.\n\nPockets of resistance emerged and secret experiments were undertaken to find ways to eliminate the negative effects of the green crystal, however they were mercilessly hunted down. Seeing the futility of their efforts, they decided to hide their research in the hope that one day it would be rediscovered and the Scrin could be liberated.\n\nData storage devices were hastily sent to planets that would be potential future targets for Scrin harvesting operations, along with small samples of Tiberium. A device had been created which could purify Tiberium, but it was completed too late and the rebels did not have sufficient crystal to power it.\n\nAs the Overlord's forces closed in, the rebels hid the device and accepted their fate.\n\nSo we do not come here as conquerors commander, but as saviours. The Overlord and his sycophants have ruled the Scrin for thousands of years, using the population's dependence on Tiberium, and the threat of its supply being interrupted, as an excuse for unlimited control. We now have the means of ending this tyranny.\n\nWe must bring the device to full power. Once enough Scrin have their dependence on Tiberium broken, and the truth has been revealed to them, the choice between freedom and servitude will finally be theirs to make.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: elusive

Player:
	PlayerResources:
		DefaultCash: 6000

ACOL:
	Buildable:
		Prerequisites: obliortmpl, anyradar, ~infantry.nod, ~!tmpp

TPLR:
	Buildable:
		Prerequisites: obliortmpl, anyradar, ~infantry.nod, ~tmpp

BH:
	Buildable:
		Prerequisites: anyradar, ~infantry.nod, ~techlevel.medium

LTNK:
	Buildable:
		Prerequisites: ~vehicles.nod, ~!lastnk.upgrade

LTNK.Laser:
	Buildable:
		Prerequisites: ~vehicles.nod, ~lastnk.upgrade

MTNK:
	Buildable:
		Prerequisites: ~!bdrone.upgrade, ~vehicles.gdi, ~techlevel.low

MTNK.Laser:
	Inherits@CAMPAIGNDISABLED: ^Disabled

FTNK:
	Buildable:
		Prerequisites: anyradar, ~vehicles.nod

HFTK:
	Buildable:
		Prerequisites: anyradar, ~vehicles.nod

WTNK:
	Buildable:
		Prerequisites: anyradar, ~vehicles.nod

MLRS:
	Buildable:
		Prerequisites: tmpl, ~vehicles.nod

SPEC:
	Buildable:
		Prerequisites: tmpl, ~vehicles.nod

VENM:
	Buildable:
		Prerequisites: ~aircraft.nod

APC2:
	Inherits@CAMPAIGNDISABLED: ^Disabled

AIRS:
	Inherits@CAMPAIGNDISABLED: ^Disabled

WEAP.TD:
	Buildable:
		Prerequisites: anyrefinery, ~structures.td

blacknapalm.upgrade:
	Buildable:
		Prerequisites: tmpl

quantum.upgrade:
	Buildable:
		Prerequisites: tmpl

hstk.upgrade:
	Buildable:
		Prerequisites: tmpl

microwave.upgrade:
	Buildable:
		Prerequisites: tmpl

LQTF:
	Inherits: ^Building
	Inherits@SHAPE: ^2x2Shape
	RenderSprites:
		Image: bio
	Selectable:
		Bounds: 2048, 2048
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Liquid Tiberium Processing Plant
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	CaptureManager:
		-BeingCapturedCondition:
	AmmoPool@LIQUIDTIB:
		Ammo: 10
		InitialAmmo: 0
	WithAmmoPipsDecoration@LIQUIDTIB:
		PipCount: 10
		RequiresSelection: false
		Position: BottomLeft
		Margin: 4, 3
	FireWarheadsOnDeath:
		Weapon: UnitExplodeToxinTruck
		EmptyWeapon: UnitExplodeToxinTruck

TTRK:
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Prerequisites: vehicles.nod
		Description: Tanker for carrying liquid Tiberium
	Valued:
		Cost: 1000
	TooltipExtras:
		Weaknesses: • Very weak armor\n• Extremely volatile when full
		-Attributes:
	Tooltip:
		Name: Liquid Tiberium Tanker
	-KillsSelf:
	-AttackFrontal:
	-Armament@PRIMARY:
	-GrantConditionOnAttack:
	-GrantConditionOnDeploy:
	-VoiceAnnouncement:
	-ExternalCondition@PRODUCED:
	Voiced:
		VoiceSet: VehicleVoice
	Mobile:
		Speed: 49
		Voice: Action
	Passenger:
		Voice: Action
	AmmoPool@LIQUIDTIB:
		Ammo: 1
		InitialAmmo: 0
		AmmoCondition: ammo
	WithAmmoPipsDecoration@LIQUIDTIB:
		PipCount: 1
		RequiresSelection: false
		Position: BottomLeft
		Margin: 4, 3
	FireWarheadsOnDeath:
		RequiresCondition: ammo
	FireWarheadsOnDeath@EMPTY:
		Weapon: UnitExplodeSmall
		EmptyWeapon: UnitExplodeSmall
		RequiresCondition: !ammo
	MustBeDestroyed:
		RequiredForShortGame: true
	Targetable@TTRK:
		TargetTypes: TibTruck

NERV:
	DetonateWeaponPower@BUZZERSWARMAI:
		Prerequisites: nerv
		ChargeInterval: 7500
	DetonateWeaponPower@STORMSPIKE:
		Prerequisites: nerv
		ChargeInterval: 8250

# Hunt() requires only 1 AttackBase
DEVA:
	-AttackFrontalCharged:
	-Armament@PRIMARYUPG:
	-AmbientSound@CHARGE:
	-WithIdleOverlay@CHARGE1:
	-WithIdleOverlay@CHARGE2:
	-WithIdleOverlay@CHARGE3:

WORMHOLE:
	Inherits@INF: ^ProducesInfantry
	Inherits@VEH: ^ProducesVehicles
	-TeleportNetwork:
	Health:
		HP: 400000
	ChangesHealth:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 101
		DamageCooldown: 0
		RequiresCondition: !regen-disabled
	ExternalCondition@NOREGEN:
		Condition: regen-disabled
	ExternalCondition@FIX1:
		Condition: forceshield
	ExternalCondition@FIX2:
		Condition: being-warped
	MustBeDestroyed:
		RequiredForShortGame: true

# Removing TeleportNetwork from Wormhole above causes exception as no actors with TeleportNetwork are defined
dummyteleport:
	Inherits: ^InvisibleDummy
	TeleportNetwork:
		Type: Wormhole
