^Palettes:
	TintPostProcessEffect:
		Red: 0.85
		Green: 0.85
		Blue: 1
		Ambient: 0.9
	WeatherOverlay:
		ParticleDensityFactor: 4
		Gravity: 16, 24
		WindTick: 150, 425
		ScatterDirection: -12, 12
		ParticleSize: 2, 3
		ParticleColors: ECECEC44, E4E4E444, D0D0D044, BCBCBC44
		LineTailAlphaValue: 0

World:
	LuaScript:
		Scripts: campaign.lua, ironclad.lua
	MissionData:
		Briefing: GDI and Allied forces have launched an offensive in an attempt to take control of one of our Iron Curtain devices. Our situation remains precarious, so it is vital that we do not lose any more of our advanced military assets.\n\nThe battle has raged for days and the base in which the Iron Curtain resides has been badly damaged. It is now besieged on all sides and our troops will soon be overrun - or worse - be forced to surrender.\n\nWe have dispatched a team of engineers to restore power. Take command, break the siege, and wipe out all GDI and Allied forces.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: bigf226m

Player:
	PlayerResources:
		DefaultCash: 0

IRON:
	GrantExternalConditionPowerCA@IRONCURTAIN:
		StartFullyCharged: True

MSLO:
	Inherits@CAMPAIGNDISABLED: ^Disabled

HALO.engis:
	Inherits: HALO.paradrop
	RevealsShroud:
		Range: 8c0
		Type: GroundPosition

# Hunt() requires only 1 AttackBase
BATF.AI:
	-AttackFrontal:
