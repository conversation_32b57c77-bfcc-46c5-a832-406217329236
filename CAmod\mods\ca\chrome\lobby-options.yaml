Container@LOBBY_OPTIONS_BIN:
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Children:
		Label@TITLE:
			Y: 0 - 24
			Width: PARENT_WIDTH
			Height: 25
			Font: Bold
			Align: Center
			Text: Map Options
		Button@RESET_OPTIONS:
			X: PARENT_WIDTH - WIDTH
			Y: 0 - 26
			Width: 50
			Height: 26
			TooltipText: Reset options to map defaults
			TooltipContainer: TOOLTIP_CONTAINER
			Text: Reset
		But<PERSON>@LOAD_OPTIONS:
			X: PARENT_WIDTH - WIDTH - 50
			Y: 0 - 26
			Width: 44
			Height: 26
			TooltipText: Load saved options
			TooltipContainer: TOOLTIP_CONTAINER
			Text: Load
		Button@SAVE_OPTIONS:
			X: PARENT_WIDTH - WIDTH - 50 - 44
			Y: 0 - 26
			Width: 44
			Height: 26
			TooltipText: Save options
			TooltipContainer: TOOLTIP_CONTAINER
			Text: Save
		ScrollPanel:
			Logic: LobbyOptionsLogicCA
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Children:
				Container@LOBBY_OPTIONS:
					Y: 10
					Width: PARENT_WIDTH - 24
					Children:
						Container@CHECKBOX_ROW_TEMPLATE:
							Width: PARENT_WIDTH
							Height: 30
							Children:
								Checkbox@A:
									X: 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								Checkbox@B:
									X: PARENT_WIDTH / 3 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								Checkbox@C:
									X: (PARENT_WIDTH / 3) * 2 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
						Container@DROPDOWN_ROW_TEMPLATE:
							Height: 60
							Width: PARENT_WIDTH
							Children:
								LabelForInput@A_DESC:
									X: 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									For: A
								DropDownButton@A:
									X: 10
									Width: PARENT_WIDTH / 3 - 20
									Y: 25
									Height: 25
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								LabelForInput@B_DESC:
									X: PARENT_WIDTH / 3 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									For: B
								DropDownButton@B:
									X: PARENT_WIDTH / 3 + 10
									Width: PARENT_WIDTH / 3 - 20
									Y: 25
									Height: 25
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								LabelForInput@C_DESC:
									X: (PARENT_WIDTH / 3) * 2 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									For: C
								DropDownButton@C:
									X: (PARENT_WIDTH / 3) * 2 + 10
									Width: PARENT_WIDTH / 3 - 20
									Y: 25
									Height: 25
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
