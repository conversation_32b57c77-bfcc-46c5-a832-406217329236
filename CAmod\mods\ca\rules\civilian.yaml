C1:
	Inherits@1: ^ArmedCivilian
	Inherits@CREW: ^CanCaptureDriverlessVehicles
	Valued:
		Cost: 50

C2:
	Inherits@1: C1

C3:
	Inherits@1: C1
	Voiced:
		VoiceSet: CivilianFemaleVoice

C4:
	Inherits@1: C1

C5:
	Inherits@1: C1

C6:
	Inherits@1: C1

C7:
	Inherits@1: C1

C8:
	Inherits@1: C1

C9:
	Inherits@1: C1

C10:
	Inherits@1: C1

#For RA map compatibility
TECN:
	Inherits@1: C1
	RenderSprites:
		Image: c1
	Tooltip:
		Name: Technician

FCOM:
	Inherits: ^TechBuilding
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	OwnerLostAction:
		Action: ChangeOwner
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 80000
	Armor:
		Type: Wood
	Tooltip:
		Name: Forward Command
	TooltipDescription@ally:
		Description: Provides buildable area.
		ValidRelationships: Ally
	TooltipDescription@other:
		Description: Capture to give buildable area.
		ValidRelationships: Neutral, Enemy
	RevealsShroud:
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	CaptureManager:
	Capturable:
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	GivesBuildableArea:
		AreaTypes: building
	BaseProvider:
		Range: 8c0
	InstantlyRepairable:
	Power:
		Amount: 0
	ProvidesPrerequisite@buildingname:
	SpawnActorOnDeath:
		Actor: FCOM.Husk
		OwnerType: InternalName
	-FireWarheadsOnDeath:
	AppearsOnMapPreview:

HOSP:
	Inherits: ^TechBuilding
	Inherits@shape: ^2x2Shape
	OwnerLostAction:
		Action: ChangeOwner
	Selectable:
		Priority: 0
		Bounds: 2048, 2048
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 80000
	CaptureManager:
	Capturable:
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	InstantlyRepairable:
	Tooltip:
		Name: Hospital
	TooltipExtras:
		Description: When controlled, heals nearby infantry and trains a Medic or Rejuvenator unit every two minutes.
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: true
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	SpawnActorOnDeath:
		Actor: HOSP.Husk
		OwnerType: InternalName
	-FireWarheadsOnDeath:
	AppearsOnMapPreview:
	ProximityExternalCondition@HOSPITAL:
		Condition: hospitalheal
		Range: 9c0
	WithRangeCircle@HOSPITAL:
		Type: HospitalHeal
		Range: 9c0
		Color: 00FF0080
	RallyPoint:
	PeriodicProducerCA@MEDIC:
		Type: Hospital
		ChargeDuration: 3000
		Actors: medi
		ShowSelectionBar: true
		RequiresCondition: !is-neutral && !scrinplayer
		ResetTraitOnOwnerChange: true
		Immediate: true
		ReadyAudio: UnitReady
		ChargeColor: ffffff
	PeriodicProducerCA@REJUVENATOR:
		Type: Hospital
		ChargeDuration: 3000
		Actors: smedi
		ShowSelectionBar: true
		RequiresCondition: !is-neutral && scrinplayer
		ResetTraitOnOwnerChange: true
		Immediate: true
		ReadyAudio: UnitReady
		ChargeColor: ffffff
	Exit@1:
		SpawnOffset: -190,880,0
		ExitCell: 1,2
		ProductionTypes: Hospital
	Exit@2:
		SpawnOffset: 190,-400,0
		ExitCell: 1,-1
		ProductionTypes: Hospital
	Production:
		Produces: Hospital
	GrantConditionIfOwnerIsNeutral:
		Condition: is-neutral
	GrantConditionOnPrerequisite@SCRIN:
		Condition: scrinplayer
		Prerequisites: player.scrin

MACS:
	Inherits: ^TechBuilding
	Inherits@shape: ^2x2Shape
	OwnerLostAction:
		Action: ChangeOwner
	Selectable:
		Priority: 0
		Bounds: 2048, 2048
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 80000
	CaptureManager:
	Capturable:
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	InstantlyRepairable:
	Tooltip:
		Name: Machine Shop
	TooltipExtras:
		Description: When controlled, repairs nearby vehicles and trains a Mechanic or Artificer unit every three minutes.
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: true
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	SpawnActorOnDeath:
		Actor: MACS.Husk
		OwnerType: InternalName
	-FireWarheadsOnDeath:
	AppearsOnMapPreview:
	ProximityExternalCondition@MACS:
		Condition: macsrepair
		Range: 9c0
	WithRangeCircle@MACS:
		Type: macsrepair
		Range: 9c0
		Color: 00FF0080
	RallyPoint:
	PeriodicProducerCA@MECHANIC:
		Type: Macs
		ChargeDuration: 4500
		Actors: mech
		ShowSelectionBar: true
		RequiresCondition: !is-neutral && !scrinplayer
		ResetTraitOnOwnerChange: true
		Immediate: true
		ReadyAudio: UnitReady
		ChargeColor: ffffff
	PeriodicProducerCA@ARTIFICER:
		Type: Macs
		ChargeDuration: 4500
		Actors: arti
		ShowSelectionBar: true
		RequiresCondition: !is-neutral && scrinplayer
		ResetTraitOnOwnerChange: true
		Immediate: true
		ReadyAudio: UnitReady
		ChargeColor: ffffff
	Exit@1:
		SpawnOffset: -190,880,0
		ExitCell: 1,2
		ProductionTypes: Macs
	Exit@2:
		SpawnOffset: 190,-400,0
		ExitCell: 1,-1
		ProductionTypes: Macs
	Production:
		Produces: Macs
	GrantConditionIfOwnerIsNeutral:
		Condition: is-neutral
	GrantConditionOnPrerequisite@SCRIN:
		Condition: scrinplayer
		Prerequisites: player.scrin

V01:
	Inherits: ^CivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Church
	RevealsShroud:
		Range: 10c0
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: -490,-384,0, 0,0,0, 0,470,0
		Type: Rectangle
			TopLeft: -768, -597
			BottomRight: 896, 683

V02:
	Inherits: ^CivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: -490,-512,0, 0,0,0, 0,512,0
		Type: Rectangle
			TopLeft: -1024, -512
			BottomRight: 1024, 597

V03:
	Inherits: ^CivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: -490,-512,0, 0,0,0, 421,512,0, -210,512,0
		Type: Rectangle
			TopLeft: -1024, -597
			BottomRight: 1024, 597

V04:
	Inherits: ^CivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, -421,-256,0, -421,256,0
		Type: Rectangle
			TopLeft: -683, -432
			BottomRight: 683, 683

V05:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

V06:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

V07:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		ExcludeTilesets: INTERIOR

V08:
	Inherits: ^CivBuilding
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V09:
	Inherits: ^CivBuilding
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V10:
	Inherits: ^CivBuilding
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V11:
	Inherits: ^CivBuilding
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V12:
	Inherits: ^CivBuilding
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V13:
	Inherits: ^CivBuilding
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V14:
	Inherits: ^CivField
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V15:
	Inherits: ^CivField
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V16:
	Inherits: ^CivField
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V17:
	Inherits: ^CivField
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V18:
	Inherits: ^CivField
	MapEditorData:
		ExcludeTilesets: INTERIOR
	MirageTarget:

V19:
	Inherits: ^CivBuilding
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Oil Pump
	SpawnActorOnDeath:
		Actor: V19.Husk
	Targetable:
		TargetTypes: Ground, Structure, NoAutoTarget

V19.Husk:
	Inherits: ^CivBuilding
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Husk (Oil Pump)
	WithSpriteBody:
	WithIdleOverlay:
		StartSequence: fire-start
		Sequence: fire-loop
	-Selectable:
	-Targetable:
	-Targetable@C4Plantable:
	-Targetable@TNTPlantable:
	-FireWarheadsOnDeath:
	-FireWarheadsOnDeath@CIVPANIC:
	Interactable:

BARL:
	Inherits: ^Barrel

BRL3:
	Inherits: ^Barrel

AMMOBOX1:
	Inherits: ^AmmoBox

AMMOBOX2:
	Inherits: ^AmmoBox

AMMOBOX3:
	Inherits: ^AmmoBox

MISS:
	Inherits: ^TechBuilding
	Inherits@shape: ^3x2Shape
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 840,0,0, 840,-1024,0, 420,768,0, -840,0,0, -840,-1024,0, -840,1024,0
	Selectable:
		Priority: 0
		Bounds: 3072, 2048
	OwnerLostAction:
		Action: ChangeOwner
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	RevealsShroud:
		Range: 10c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Armor:
		Type: Wood
	Tooltip:
		Name: Communications Center
	TooltipDescription@ally:
		Description: Provides vision of the surrounding area.
		ValidRelationships: Ally
	TooltipDescription@other:
		Description: Capture to provide vision of surrounding area.
		ValidRelationships: Neutral, Enemy
	WithBuildingBib:
	CaptureManager:
	Capturable:
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	InstantlyRepairable:
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
	SpawnActorOnDeath:
		Actor: MISS.Husk
		OwnerType: InternalName
	-FireWarheadsOnDeath:

BIO:
	Inherits: ^TechBuilding
	Inherits@shape: ^2x2Shape
	Selectable:
		Bounds: 2048, 2048
	OwnerLostAction:
		Action: ChangeOwner
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	RevealsShroud:
		Range: 4c0
	CaptureManager:
	CapturableProgressBlink:
	Capturable:
		Types: building
	CapturableProgressBar:
	InstantlyRepairable:
	Tooltip:
		Name: Biological Lab
	TooltipDescription@ally:
		Description: Provides prerequisite for Bio-Lab units.
		ValidRelationships: Ally
	TooltipDescription@other:
		Description: Capture to produce Bio-Lab units.
		ValidRelationships: Neutral, Enemy
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
	SpawnActorOnDeath:
		Actor: BIO.Husk
		OwnerType: InternalName
	-FireWarheadsOnDeath:
	AppearsOnMapPreview:
	ProvidesPrerequisite@mortar:
		Prerequisite: stolentech.hand
	ProvidesPrerequisite@toxintruck:
		Prerequisite: vehicles.ttrk

OILB:
	Inherits: ^TechBuilding
	Inherits@shape: ^2x2Shape
	Selectable:
		Priority: 0
		Bounds: 2048, 2048
	OwnerLostAction:
		Action: ChangeOwner
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 80000
	RevealsShroud:
		Range: 4c0
	CaptureManager:
	Capturable:
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	InstantlyRepairable:
	CashTrickler:
		Interval: 375
		Amount: 100
		RequiresCondition: enabled
	Tooltip:
		Name: Oil Derrick
	TooltipDescription@ally:
		Description: Provides additional funds.
		ValidRelationships: Ally
	TooltipDescription@other:
		Description: Capture to receive additional funds.
		ValidRelationships: Neutral, Enemy
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	SpawnActorOnDeath:
		Actor: OILB.Husk
		OwnerType: InternalName
	-FireWarheadsOnDeath:
	UpdatesDerrickCount:
	AppearsOnMapPreview:
	GrantConditionOnCombatantOwner:
		Condition: enabled
	GrantConditionOnDamageState:
		Condition: damaged
	WithIdleOverlay@flare:
		Sequence: flare
		Offset: 2078,-480,0
		Palette: effect
		RequiresCondition: !damaged
	WithIdleOverlay@damaged-flare:
		Sequence: damaged-flare
		Offset: 2500,250,0
		Palette: effect
		RequiresCondition: damaged
	GrantConditionIfOwnerIsNeutral:
		Condition: is-neutral
	GivesCashOnCapture@defloration:
		Amount: 300
		RequiresCondition: !has-been-captured
	GivesCashOnCapture@subsequent:
		Amount: 100
		RequiresCondition: has-been-captured
	GrantDelayedCondition:
		Delay: 1
		Condition: has-been-captured
		RequiresCondition: !is-neutral

OILR:
	Inherits: ^TechBuilding
	Inherits@shape: ^3x2Shape
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 840,0,0, 840,-1024,0, 420,768,0, -840,0,0, -840,-1024,0, -840,1024,0
	Selectable:
		Priority: 0
		Bounds: 3072, 2048
	OwnerLostAction:
		Action: ChangeOwner
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 80000
	RevealsShroud:
		Range: 4c0
	CaptureManager:
	Capturable:
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	InstantlyRepairable:
	Tooltip:
		Name: Oil Refinery
	TooltipDescription@ally:
		Description: Provides 10% unit discount.
		ValidRelationships: Ally
	TooltipDescription@other:
		Description: Capture to receive a 10% production discount.
		ValidRelationships: Neutral, Enemy
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	ProvidesPrerequisite@buildingname:
	SpawnActorOnDeath:
		Actor: OILR.Husk
		OwnerType: InternalName
	-FireWarheadsOnDeath:
	AppearsOnMapPreview:
	GrantConditionOnDamageState:
		Condition: damaged
	WithIdleOverlay@flare:
		Sequence: flare
		Offset: 2500,-390,0
		Palette: effect
		RequiresCondition: !damaged
	WithIdleOverlay@damaged-flare:
		Sequence: damaged-flare
		Offset: 2600,180,0
		Palette: effect
		RequiresCondition: damaged
	GrantConditionIfOwnerIsNeutral:
		Condition: is-neutral
	GivesCashOnCapture@defloration:
		Amount: 300
		RequiresCondition: !has-been-captured
	GivesCashOnCapture@subsequent:
		Amount: 100
		RequiresCondition: has-been-captured
	GrantDelayedCondition:
		Delay: 1
		Condition: has-been-captured
		RequiresCondition: !is-neutral

V20:
	Inherits: ^DesertCivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: -840,-512,0, 0,0,0, -840,512,0
		Type: Rectangle
			TopLeft: -1024, -512
			BottomRight: 1024, 896

V21:
	Inherits: ^DesertCivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 840,-512,0, 420,0,0, 840,512,0
		Type: Rectangle
			TopLeft: -1024, -1024
			BottomRight: 1024, 0
	HitShape@WELL:
		TargetableOffsets: -770,512,0
		Type: Rectangle
			TopLeft: 0, 0
			BottomRight: 1024, 598

V22:
	Inherits: ^DesertCivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1

V23:
	Inherits: ^DesertCivBuilding

V24:
	Inherits: ^DesertCivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: -630,-512,0, 0,0,0, -630,256,0, 420,-512,0
		Type: Rectangle
			TopLeft: -1024, -683
			BottomRight: 640, 853

V25:
	Inherits: ^DesertCivBuilding
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Church
	RevealsShroud:
		Range: 10c0
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,-128,0, 420,512,0
		Type: Rectangle
			TopLeft: -683, -683
			BottomRight: 1024, 512

V26:
	Inherits: ^DesertCivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1

V27:
	Inherits: ^DesertCivBuilding

V28:
	Inherits: ^DesertCivBuilding

V29:
	Inherits: ^DesertCivBuilding

V30:
	Inherits: ^DesertCivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1

V31:
	Inherits: ^DesertCivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1

V32:
	Inherits: ^DesertCivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1

V33:
	Inherits: ^DesertCivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1

V34:
	Inherits: ^DesertCivBuilding

V35:
	Inherits: ^DesertCivBuilding

V36:
	Inherits: ^DesertCivBuilding

V37:
	Inherits: ^DesertCivBuilding
	Building:
		Footprint: __xx_ ___xx
		Dimensions: 5,2
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 0,1024,0
		Type: Rectangle
			TopLeft: -512, -597
			BottomRight: 1536, 597

RICE:
	Inherits: ^CivField
	MapEditorData:
		RequireTilesets: TEMPERAT

RUSHOUSE:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: TEMPERAT
	Building:
		Footprint: x x
		Dimensions: 1,2
	HitShape:
		UseTargetableCellsOffsets: false

RUSHOUSE2:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, BARREN
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	HitShape:
		UseTargetableCellsOffsets: false

RUSHOUSE3:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, BARREN
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	HitShape:
		UseTargetableCellsOffsets: false

RUSHOUSE4:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, BARREN
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	HitShape:
		UseTargetableCellsOffsets: false

ASIANHUT:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: TEMPERAT

SNOWHUT:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: SNOW
	Building:
		Footprint: x x
		Dimensions: 1,2
	RenderSprites:
	HitShape:
		UseTargetableCellsOffsets: false

LHUS:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: TEMPERAT
	Selectable:
		Bounds: 1024, 2048, 0, -682
	Tooltip:
		Name: Lighthouse
	Building:
		Footprint: x
		Dimensions: 1,1

WINDMILL:
	Inherits: ^CivBuilding
	MapEditorData:
		RequireTilesets: TEMPERAT
	Selectable:
		Bounds: 1024, 1024, 0, -597
		DecorationBounds: 1536, 1536, 0, -597
	SelectionDecorations:
	Tooltip:
		Name: Windmill
	Building:
		Footprint: x
		Dimensions: 1,1

ARCO:
	Inherits: ^CivBuilding
	RenderSprites:
		Palette: temptd
	Building:
		Footprint: xx
		Dimensions: 2,1
	Tooltip:
		Name: Oil Pump
		ShowOwnerRow: True
