^Palettes:
	FlashPostProcessEffect@CHRONO:
		Type: Chronoshift

World:
	LuaScript:
		Scripts: campaign.lua, ca-prologue-01.lua
	MissionData:
		Briefing: The Soviets have captured one of our most important scientists, Professor <PERSON>.\n\nHe is currently being held inside a Soviet base not far from the border. It is crucial that he be rescued before he can be transported deeper into Soviet territory.\n\nUsing a small task force led by our special commando <PERSON>, rescue <PERSON> and bring him to the extraction point.\n\nThe Soviet base is protected by Tesla Coils. Cutting their power supply will render them inoperative.
	ScriptLobbyDropdown@DIFFICULTY:
		ID: prologuedifficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
		Default: easy
		Locked: True
	MusicPlaylist:
		StartingMusic: bigf226m

TRAN.evac:
	Cargo:
		Types: Einstein
		MaxWeight: 1
	RejectsOrders:
	-Selectable:
	Interactable:

EINSTEIN:
	Passenger:
		CargoType: Einstein

C7:
	-Crushable:
	-Wanders:

C8:
	-Crushable:
	-Wanders:

TSLA:
	Power:
		Amount: -150

E7:
	AutoTarget:
		InitialStance: HoldFire
