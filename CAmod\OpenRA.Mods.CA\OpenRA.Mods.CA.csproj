<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <EngineRootPath>../engine</EngineRootPath>
  </PropertyGroup>
  <Import Project="../engine/Directory.Build.props" />
  <ItemGroup>
    <ProjectReference Include="$(EngineRootPath)/OpenRA.Game/OpenRA.Game.csproj">
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="$(EngineRootPath)/OpenRA.Mods.Cnc/OpenRA.Mods.Cnc.csproj">
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="$(EngineRootPath)/OpenRA.Mods.Common/OpenRA.Mods.Common.csproj">
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
</Project>