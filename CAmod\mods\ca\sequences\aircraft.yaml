mig:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mig.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: migicon.shp

yak:
	Inherits: ^VehicleOverlays
	idle:
		Filename: yak.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: yakicon.shp

p51:
	Inherits: ^VehicleOverlays
	idle:
		Filename: p51.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: yakicon.shp

heli:
	Inherits: ^VehicleOverlays
	idle:
		Filename: heli.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lbrotor.shp
		Length: 4
	slow-rotor:
		Filename: lbrotor.shp
		Start: 4
		Length: 8
	icon:
		Filename: heliicon.shp

hind:
	Inherits: ^VehicleOverlays
	Defaults:
		Offset: 0, -3
	idle:
		Filename: hind.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotorlg.shp
		Length: 4
	slow-rotor:
		Filename: lrotorlg.shp
		Start: 4
		Length: 8
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: hindicon.shp
		Offset: 0,0

tran:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tran.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotor.shp
		Length: 4
	rotor2:
		Filename: rrotor.shp
		Length: 4
	slow-rotor:
		Filename: lrotor.shp
		Start: 4
		Length: 8
	slow-rotor2:
		Filename: rrotor.shp
		Start: 4
		Length: 8
	open:
		Filename: tran.shp
		Start: 32
		Length: 4
	close:
		Filename: tran.shp
		Frames: 35, 34, 33, 32
		Length: 4
	unload:
		Filename: tran.shp
		Start: 35
	icon:
		Filename: tranicon.shp

tran2:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tran2.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotor.shp
		Length: 4
	rotor2:
		Filename: rrotor.shp
		Length: 4
	slow-rotor:
		Filename: lrotor.shp
		Start: 4
		Length: 8
	slow-rotor2:
		Filename: rrotor.shp
		Start: 4
		Length: 8
	open:
		Filename: tran2.shp
		Start: 32
		Length: 4
	close:
		Filename: tran2.shp
		Frames: 35, 34, 33, 32
		Length: 4
	unload:
		Filename: tran2.shp
		Start: 35
	icon:
		Filename: tranicnh.shp

tran3:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tran3.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotor.shp
		Length: 4
	rotor2:
		Filename: rrotor.shp
		Length: 4
	slow-rotor:
		Filename: lrotor.shp
		Start: 4
		Length: 8
	slow-rotor2:
		Filename: rrotor.shp
		Start: 4
		Length: 8
	open:
		Filename: tran3.shp
		Start: 32
		Length: 4
	close:
		Filename: tran3.shp
		Frames: 35, 34, 33, 32
		Length: 4
	unload:
		Filename: tran3.shp
		Start: 35
	icon:
		Filename: tranicon.shp

tran1husk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tran1husk.shp

tran2husk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tran2husk.shp

halo:
	Inherits: ^VehicleOverlays
	idle:
		Filename: halo.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotorhuge.shp
		Length: 4
	slow-rotor:
		Filename: lrotorhuge.shp
		Start: 4
		Length: 8
	icon:
		Filename: haloicon.shp

nhaw:
	Inherits: ^VehicleOverlays
	idle:
		Filename: nhaw.shp
		Facings: 32
		UseClassicFacings: True
	empty:
		Filename: nhaw0.shp
		Facings: 32
		UseClassicFacings: True
	oneshot:
		Filename: nhaw1.shp
		Facings: 32
		UseClassicFacings: True
	idle-upg:
		Filename: nhaw2.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotor.shp
		Length: 4
	rotor2:
		Filename: rrotor.shp
		Length: 4
	slow-rotor:
		Filename: lrotor.shp
		Start: 4
		Length: 8
	slow-rotor2:
		Filename: rrotor.shp
		Start: 4
		Length: 8
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: nhawicon.shp

u2:
	Inherits: ^VehicleOverlays
	idle:
		Filename: u2.shp
		Facings: 16

smig:
	Inherits: ^VehicleOverlays
	idle:
		Filename: smig.shp
		Facings: 32

badr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: badr.shp
		Facings: 16

b2b:
	Inherits: ^VehicleOverlays
	idle:
		Filename: b2b.shp
		Facings: 32

a10:
	Inherits: ^VehicleOverlays
	idle:
		Filename: a10.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Frames: 2,2,6,6,12,12,18,18,24,24,30,30,38,38,42,42,48,48,54,54,62,62,66,66,72,72,78,78,84,84,90,90
		Length: 2
		Facings: 16
	icon:
		Filename: a10icnh.shp

c17:
	Inherits: ^VehicleOverlays
	idle:
		Filename: c17.shp
		Facings: 32
		UseClassicFacings: True

galx:
	Inherits: ^VehicleOverlays
	idle:
		Filename: galx.shp
		Facings: 32
		UseClassicFacings: True

anto:
	Inherits: ^VehicleOverlays
	idle:
		Filename: anto.shp
		Facings: 32
		UseClassicFacings: True

orca:
	Inherits: ^VehicleOverlays
	idle:
		Filename: orca.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: orca.shp
		Start: 32
		Facings: 32
	move-afterburner:
		Filename: orcajet.shp
		Facings: 32
		Length: 3
	icon:
		Filename: orcaicnh.shp

orcab:
	Inherits: ^VehicleOverlays
	idle:
		Filename: orcab.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: orcab.shp
		Start: 32
		Facings: 32
	icon:
		Filename: orcabicnh.shp

apch:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apch.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotor.shp
		Length: 4
	slow-rotor:
		Filename: lrotor.shp
		Start: 4
		Length: 8
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: apchicnh.shp

uav:
	Inherits: ^VehicleOverlays
	idle:
		Filename: uav.shp
		Facings: 32
		UseClassicFacings: True

rah66:
	Inherits: ^VehicleOverlays
	idle:
		Filename: rah66.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lbrotor.shp
		Length: 4
	slow-rotor:
		Filename: lbrotor.shp
		Start: 4
		Length: 8
	icon:
		Filename: rah66icnh.shp

kirov:
	Inherits: ^VehicleOverlays
	idle:
		Filename: kirov.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: krotor.shp
		Length: 4
	slow-rotor:
		Filename: krotor.shp
		Start: 4
		Length: 8
	icon:
		Filename: kirovicon.shp

suk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: suk.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	icon:
		Filename: sukicon.shp

orcaca:
	Inherits: ^VehicleOverlays
	idle:
		Filename: orcaca.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: orcaca.shp
		Start: 32
		Facings: 32
	xo:
		Filename: xo.shp
		Start: 80
		Facings: 8
		ZOffset: -1024
	icon:
		Filename: orcacaicnh.shp

orcacapod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: orcacapod.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 256
	move:
		Filename: orcacapod.shp
		Start: 32
		Facings: 32
	icon:
		Filename: orcacaicnh.shp

harr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harr.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: harricon.shp

horn:
	Inherits: ^VehicleOverlays
	idle:
		Filename: horn.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: harricon.shp

scrin:
	Inherits: ^VehicleOverlays
	idle:
		Filename: scrin.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: scrinicnh.shp

yf23:
	Inherits: ^VehicleOverlays
	idle:
		Filename: yf23.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: a10icnh.shp

pod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: pod.shp
		Start: 1
		Length: 1

pod2:
	Inherits: ^VehicleOverlays
	idle:
		Filename: pod.shp
		Length: 1

mh60:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mh60.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotorlg.shp
		Length: 4
	slow-rotor:
		Filename: lrotorlg.shp
		Start: 4
		Length: 8
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: mh60icon.shp

venm:
	Inherits: ^VehicleOverlays
	idle:
		Filename: venm.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: venm.shp
		Start: 32
		Facings: 32
	muzzle:
		Filename: lasermuzzle.shp
		Length: *
	icon:
		Filename: venmicnh.shp

auro:
	Inherits: ^VehicleOverlays
	idle:
		Filename: auro.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: auroicnh.shp

pmak:
	Inherits: ^VehicleOverlays
	idle:
		Filename: pmak.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: pmakicon.shp

beag:
	Inherits: ^VehicleOverlays
	idle:
		Filename: beag.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: beagicon.shp

phan:
	Inherits: ^VehicleOverlays
	idle:
		Filename: phan.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: phanicon.shp

kamv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: kamv.shp
		Facings: 32
		UseClassicFacings: True
	rotor:
		Filename: lrotorlg.shp
		Length: 4
	slow-rotor:
		Filename: lrotorlg.shp
		Start: 4
		Length: 8
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: kamvicon.shp

shde:
	Inherits: ^VehicleOverlays
	idle:
		Filename: shde.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: shdeicnh.shp

vert:
	Inherits: ^VehicleOverlays
	idle:
		Filename: vert.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: verticnh.shp

mcor:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mcor.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Frames: 0,1,2,1,2,1,2,3
		Filename: lasermuzzle.shp
		Length: *
	icon:
		Filename: mcoricnh.shp

disc:
	Inherits: ^VehicleOverlays
	idle:
		Filename: disc.shp
		Length: *
	muzzle:
		Filename: scrinmuzz6.shp
		Length: *
		Tick: 40
		ZOffset: 2049
		BlendMode: Additive
	drain:
		Filename: diskray.shp
		Length: *
		ZOffset: -128
		Offset: 0, 10
		Tick: 60
		Alpha: 0.65
	charge:
		Filename: disccharge.shp
		Length: *
		Tick: 70
		ZOffset: 1023
		BlendMode: Additive
		AlphaFade: True
	icon:
		Filename: discicon.shp

disccrash:
	Inherits: ^VehicleOverlays
	idle:
		Filename: disccrash.shp
		Facings: 32
		UseClassicFacings: True

jack:
	Inherits: ^VehicleOverlays
	idle:
		Filename: jack.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: jackicon.shp
