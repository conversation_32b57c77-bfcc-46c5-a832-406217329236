^Palettes:
	PaletteFromFile@terrain-temperat:
		Filename: alien.pal
	WeatherOverlay:
		ParticleDensityFactor: 4
		Gravity: 8, 16
		WindTick: 150, 425
		ScatterDirection: -12, 12
		ParticleSize: 2, 3
		ParticleColors: 00000066, 11111166, 22222266
		LineTailAlphaValue: 0
	TintPostProcessEffect:
		Red: 0.9
		Green: 0.9
		Blue: 1.1
		Ambient: 0.65
	FixedColorPalette@BlueTiberium:
		Color: ffffff

^BaseWorld:
	TerrainLighting:
	ResourceRenderer:
		ResourceTypes:
			BlueTiberium:
				Name: Purified Tiberium


^BasePlayer:
	PlayerResources:
		ResourceValues:
			BlueTiberium: 25

World:
	LuaScript:
		Scripts: campaign.lua, annexation.lua
	MissionData:
		Briefing: With the gateway under our control, <PERSON> has been prevented from returning to Earth, and our forces are now prepared to journey to the Scrin homeworld where we will assist the Overlord to defeat the rebels and eliminate <PERSON>.\n\nA Nod base exists on the other side. <PERSON> may attempt to make a stand there, but it is more likely he has fled into the shadows as he has done before. Regardless, our first task will be to establish a base of operations and secure the immediate vicinity. The nearby Nerve Center will need to be captured to ensure the gateway remains open.\n\nWe will be venturing deep into rebel territory, so do not expect the Overlord to be able to assist you. Opening up a new front here should relieve some of the pressure his forces are experiencing elsewhere on the planet.\n\nWipe out any Nod or rebel forces you encounter. Show the Overlord what a powerful ally we can be.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: phatatta

Player:
	PlayerResources:
		DefaultCash: 6000

# Purified Tib doesn't damage infantry

^Infantry:
	DamagedByTerrain@TIBDAMAGE:
		Terrain: Tiberium

# Disable tech

MSHP:
	Inherits@CAMPAIGNDISABLED: ^Disabled

imppara.upgrade:
	Inherits@CAMPAIGNDISABLED: ^Disabled

# Disable powers

^SpyPlanePower:
	AirstrikePowerCA@spyplane:
		Prerequisites: ~disabled

^ParabombsPower:
	AirstrikePowerCA@Russianparabombs:
		Prerequisites: ~disabled

^ParatroopersPower:
	ParatroopersPowerCA@paratroopers:
		Prerequisites: ~disabled

^TankDropPower:
	ParatroopersPowerCA@TankDrop:
		Prerequisites: ~disabled

# Misc

recall.effect:
	Inherits: CAMERA
	PeriodicExplosion:
		Weapon: FleetRecallEffect

NodGateway:
	Inherits: ^WormholeBase
	Tooltip:
		Name: Gateway
	RenderSprites:
		Image: wormholexl
	MapEditorData:
		Categories: System
	TerrainLightSource:
		Range: 4c0
		Intensity: 0.5
		RedTint: 1

SPLITBLUE:
	Tooltip:
		Name: Blossom Tree

WORMHOLE:
	-TeleportNetwork:
	Health:
		HP: 300000

SIGN:
	Inherits@CAMPAIGNDISABLED: ^Disabled
	CaptureManager:
	Capturable:
		RequiresCondition: !build-incomplete && !being-warped
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	Tooltip:
		GenericVisibility: None
	TooltipExtras:
		-Attributes:

# Removing TeleportNetwork from Wormhole above causes exception as no actors with TeleportNetwork are defined
dummyteleport:
	Inherits: ^InvisibleDummy
	TeleportNetwork:
		Type: Wormhole

# Hunt() requires only 1 AttackBase
DEVA:
	-AttackFrontalCharged:
	-Armament@PRIMARYUPG:
	-AmbientSound@CHARGE:
	-WithIdleOverlay@CHARGE1:
	-WithIdleOverlay@CHARGE2:
	-WithIdleOverlay@CHARGE3:
