^Upgrade:
	Inherits@DUMMY: ^InvisibleDummy
	Buildable:
		Queue: Upgrade
		BuildLimit: 1
		BuildDurationModifier: 100
	ProvidesPrerequisite@upgradename:
	ProvidesUpgrade@upgradename:
	WithProductionIconOverlay:
		Types: Upgrade

#########################SHARED#########
########################################

hazmat.upgrade:
	Inherits: ^Upgrade
	Tooltip:
		Name: Upgrade: Hazmat Suits
	Buildable:
		BuildPaletteOrder: 19
		Prerequisites: anyradar, infantry.any, ~!player.soviet, ~!player.zocom, ~!player.scrin, ~techlevel.medium
		IconPalette: chrometd
		Description: Infantry are equipped with hazmat suits which provides protection against Tiberium and radiation.\n\nUpgrades: Infantry
	TooltipExtras:
		Strengths: + Tiberium immunity\n+ 50% resistance to irradiated terrain
	Valued:
		Cost: 750
	RenderSprites:
		Image: hazmat.upgrade
	ProductionCostMultiplier@NODDISCOUNT:
		Multiplier: 70
		Prerequisites: player.nod
	WithProductionIconOverlay:
		Prerequisites: hazmat.upgrade
	Encyclopedia:
		Category: Allies/Upgrades; GDI/Upgrades; Nod/Upgrades

flakarmor.upgrade:
	Inherits: ^Upgrade
	Tooltip:
		Name: Upgrade: Advanced Flak Armor
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: techcenter.any, infantry.any, ~!player.nod, ~!player.scrin, ~techlevel.high
		Description: Infantry are equipped with advanced flak armor which provides protection against explosives.\n\nUpgrades: Infantry
	TooltipExtras:
		Strengths: + 40% reduced damage from explosives\n+ 20% reduced damage from incendiary explosives
	Valued:
		Cost: 1500
	RenderSprites:
		Image: flakarmor.upgrade
	WithProductionIconOverlay:
		Prerequisites: flakarmor.upgrade
	Encyclopedia:
		Category: Allies/Upgrades; Soviets/Upgrades; GDI/Upgrades; Nod/Upgrades

#########################GDI#########
########################################

ANYSTRATEGY:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Active Strategy
	Buildable:
		Description: Active Strategy
	ProvidesPrerequisite:

^GDIUpgrade:
	Inherits: ^Upgrade
	Encyclopedia:
		Category: GDI/Upgrades

bombard.strat:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Strategy: Bombardment
	Buildable:
		BuildPaletteOrder: 7
		Prerequisites: ~player.gdi, anyradar, ~!seek.strat, ~!hold.strat, ~techlevel.medium
		IconPalette: chrometd
		Description: Firepower focused strategy, required for:\n• Zone Trooper\n• Tomahawk Launcher Research\n• Hailstorm Missiles Upgrade\n• Avenger Upgrade\n• Firestorm Missiles Power\n\n
	TooltipExtras:
		Strengths: + Increases firepower and rate of fire of vehicles and aircraft by 3%
		Attributes: \n(!) Only ONE Strategy may be chosen.
	Valued:
		Cost: 750
	RenderSprites:
		Image: bombard.strat
	AnnounceOnCreation:
		SpeechNotification: Bombardment
		Delay: 60
	UpdatesCount:
		Type: StrategyLevel
	WithProductionIconOverlay:
		Prerequisites: bombard.strat

bombard2.strat:
	Inherits: bombard.strat
	-AnnounceOnCreation:
	Tooltip:
		Name: Strategy: Bombardment II
	Buildable:
		BuildPaletteOrder: 7
		Prerequisites: ~player.gdi, gtek, ~bombard.strat, ~techlevel.high
		Description: Increase Bombardment strategy bonuses.
	TooltipExtras:
		Strengths: + Increases firepower and rate of fire of vehicles and aircraft by a further 3%
		-Attributes:
	RenderSprites:
		Image: bombard2.strat
	UpdatesCount:
		Type: StrategyLevel
	WithProductionIconOverlay:
		Prerequisites: bombard2.strat

seek.strat:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Strategy: Seek & Destroy
	Buildable:
		BuildPaletteOrder: 9
		Prerequisites: ~player.gdi, anyradar, ~!bombard.strat, ~!hold.strat, ~techlevel.medium
		IconPalette: chrometd
		Description: Speed and weapon range focused strategy, required for:\n• Zone Raider\n• TOW Missile Upgrade\n• Hypersonic Missiles Upgrade\n• Sidewinders Upgrade\n• Advanced Radar Power\n\n
	TooltipExtras:
		Strengths: + Increases speed and weapon range of vehicles and aircraft by 5%
		Attributes: \n(!) Only ONE Strategy may be chosen.
	Valued:
		Cost: 750
	RenderSprites:
		Image: seek.strat
	AnnounceOnCreation:
		SpeechNotification: SeekAndDestroy
		Delay: 60
	UpdatesCount:
		Type: StrategyLevel
	WithProductionIconOverlay:
		Prerequisites: seek.strat

seek2.strat:
	Inherits: seek.strat
	-AnnounceOnCreation:
	Tooltip:
		Name: Strategy: Seek & Destroy II
	Buildable:
		BuildPaletteOrder: 9
		Prerequisites: ~player.gdi, gtek, ~seek.strat, ~techlevel.high
		Description: Increase Seek & Destroy strategy bonuses.
	TooltipExtras:
		Strengths: + Increases speed and weapon range of vehicles and aircraft by a further 5%
		-Attributes:
	RenderSprites:
		Image: seek2.strat
	UpdatesCount:
		Type: StrategyLevel
	WithProductionIconOverlay:
		Prerequisites: seek2.strat

hold.strat:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Strategy: Hold the Line
	Buildable:
		BuildPaletteOrder: 11
		Prerequisites: ~player.gdi, anyradar, ~!bombard.strat, ~!seek.strat, ~techlevel.medium
		IconPalette: chrometd
		Description: Defensive strategy, required for:\n• Zone Defender\n• Point Defense Systems Upgrade\n• Hammerhead Missiles Upgrade\n• Ceramic Armor Upgrade\n• Nanite Shield Power\n\n
	TooltipExtras:
		Strengths: + Increases armor of vehicles and aircraft by 5%
		Attributes: \n(!) Only ONE Strategy may be chosen.
	Valued:
		Cost: 750
	RenderSprites:
		Image: hold.strat
	AnnounceOnCreation:
		SpeechNotification: HoldTheLine
		Delay: 60
	UpdatesCount:
		Type: StrategyLevel
	WithProductionIconOverlay:
		Prerequisites: hold.strat

hold2.strat:
	Inherits: hold.strat
	-AnnounceOnCreation:
	Tooltip:
		Name: Strategy: Hold the Line II
	Buildable:
		BuildPaletteOrder: 11
		Prerequisites: ~player.gdi, gtek, ~hold.strat,  ~techlevel.high
		Description: Increase Hold the Line strategy bonuses.
	TooltipExtras:
		Strengths: + Increases armor of vehicles and aircraft by a further 5%
		-Attributes:
	RenderSprites:
		Image: hold2.strat
	UpdatesCount:
		Type: StrategyLevel
	WithProductionIconOverlay:
		Prerequisites: hold2.strat

vulcan.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Vulcan
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.gdi, anyradar, ~techlevel.medium
		Description: Replaces: APC
	TooltipExtras:
		Strengths: + Adds anti-air capability\n+ Increased damage against ground targets\n+ Adds turret
		Weaknesses: – Increased cost\n– Reduced speed
	Valued:
		Cost: 750
	RenderSprites:
		Image: vulcan.upgrade
	WithProductionIconOverlay:
		Prerequisites: vulcan.upgrade

bjet.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Bombardier
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.gdi, anyradar, ~techlevel.medium
		Description: Allows training of Bombardiers.
	TooltipExtras:
		Strengths: + Strong vs Buildings, Defenses, Light Armor
		Weaknesses: – Weak vs Heavy Armor\n– Has difficulty hitting moving targets\n– Cannot attack Aircraft
	Valued:
		Cost: 750
	RenderSprites:
		Image: bjet.upgrade
	WithProductionIconOverlay:
		Prerequisites: bjet.upgrade

empgren.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: EMP Grenades
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~techlevel.high
		IconPalette: chrometd
		Description: Upgrades Grenadier to use EMP Grenades.\n\nUpgrades: Grenadier
	TooltipExtras:
		Strengths: + Grenades briefly disable vehicles and defenses\n+ Grenadiers no longer explode when killed
	Valued:
		Cost: 1000
	RenderSprites:
		Image: empgren.upgrade
	WithProductionIconOverlay:
		Prerequisites: empgren.upgrade

strata10.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Strategy Dependent Upgrade
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, anystrategy, ~!bombard.strat, ~!seek.strat, ~!hold.strat, ~techlevel.high
		IconPalette: chrometd
		Description: Upgrade depends on chosen strategy.\n\n• Bombardment: Avenger upgrade\n• Seek & Destroy: Sidewinders upgrade\n• Hold the Line: Ceramic Armor upgrade
	Valued:
		Cost: 750
	RenderSprites:
		Image: strategic.upgrade
	WithProductionIconOverlay:
		Prerequisites: strata10.upgrade
	-Encyclopedia:

avenger.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Avenger
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~bombard.strat, ~techlevel.high
		IconPalette: chrome
		Description: Replaces Warthog's incendiary bombs with GAU-8 Avenger rotary cannon and Hydra rockets.\n\nUpgrades: Warthog
	TooltipExtras:
		Strengths: + Increased damage\n+ Increased range
		Weaknesses: – Requires two passes
	Valued:
		Cost: 1000
	RenderSprites:
		Image: avenger.upgrade
	WithProductionIconOverlay:
		Prerequisites: avenger.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Bombardment Strategy.

sidewinders.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Sidewinders
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~seek.strat, ~techlevel.high
		IconPalette: chrometd
		Description: Equips Warthogs with air-to-air missiles.\n\nUpgrades: Warthog
	TooltipExtras:
		Strengths: + Added air-to-air missiles
	Valued:
		Cost: 750
	RenderSprites:
		Image: sidewinders.upgrade
	WithProductionIconOverlay:
		Prerequisites: sidewinders.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Seek & Destroy Strategy.

ceramic.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Ceramic Armor
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~hold.strat, ~techlevel.high
		IconPalette: chrometd
		Description: Upgrades Warthogs with ceramic armor.\n\nUpgrades: Warthog
	TooltipExtras:
		Strengths: + Increased durability
	Valued:
		Cost: 750
	RenderSprites:
		Image: ceramic.upgrade
	WithProductionIconOverlay:
		Prerequisites: ceramic.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Hold the Line Strategy.

strategic.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Strategy Dependent Upgrade
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, anystrategy, ~!bombard.strat, ~!seek.strat, ~!hold.strat, ~techlevel.high
		IconPalette: chrometd
		Description: Upgrade depends on chosen strategy.\n\n• Bombardment: Tomahawk Launcher research\n• Seek & Destroy: TOW Missile upgrade\n• Hold the Line: Point Defense Systems upgrade
	Valued:
		Cost: 1000
	RenderSprites:
		Image: strategic.upgrade
	WithProductionIconOverlay:
		Prerequisites: strategic.upgrade
	-Encyclopedia:

thwk.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Tomahawk Launcher
	Buildable:
		BuildPaletteOrder: 30
		IconPalette: chrometd
		Prerequisites: ~player.gdi, gtek, ~bombard.strat, ~techlevel.high
		Description: Allows construction of Tomahawk Missile Launchers.
	TooltipExtras:
		Strengths: + Strong vs Buildings\n+ Extremely long range
		Weaknesses: – Projectiles can be shot down\n– Slow rate of fire
	Valued:
		Cost: 1000
	RenderSprites:
		Image: thwk.upgrade
	WithProductionIconOverlay:
		Prerequisites: thwk.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Bombardment Strategy.

tow.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: TOW Missile
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~seek.strat, ~techlevel.high
		Description: Hum-Vees and Guardian Drones will be produced with TOW missile launchers.\n\nReplaces: Hum-Vee\nReplaces: Guardian Drone
	TooltipExtras:
		Strengths: + Added TOW missile launcher (strong vs Heavy Armor)
		Weaknesses: – Increased cost\n– Reduced speed
	Valued:
		Cost: 1000
	RenderSprites:
		Image: tow.upgrade
	WithProductionIconOverlay:
		Prerequisites: tow.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Seek & Destroy Strategy.

pointdef.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Point Defense Systems
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~hold.strat, ~techlevel.high
		IconPalette: chrometd
		Description: Equips several units with point defense shields and/or point defense lasers which protect against enemy fire.\n\nUpgrades: Battle Tank (Shield and Laser)\nUpgrades: Battle Drone (Shield and Laser)\nUpgrades: Hum-Vee (Shield)\nUpgrades: Guardian Drone (Shield)\nUpgrades: Harvester (Shield)\nUpgrades: Frigate (Laser)
	TooltipExtras:
		Strengths: + Added Point Defense Shield\n+ Added Point Defense Laser
	Valued:
		Cost: 1500
	RenderSprites:
		Image: pointdef.upgrade
	WithProductionIconOverlay:
		Prerequisites: pointdef.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Hold the Line Strategy.

stratmiss.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Strategy Dependent Upgrade
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, anystrategy, ~!bombard.strat, ~!seek.strat, ~!hold.strat, ~techlevel.high
		IconPalette: chrometd
		Description: Equip the MLRS/HMLRS with improved missiles depending on chosen strategy.\n\n• Bombardment: Hailstorm Missiles\n• Seek & Destroy: Hypersonic Missiles\n• Hold the Line: Hammerhead Missiles
	Valued:
		Cost: 1000
	RenderSprites:
		Image: strategic.upgrade
	WithProductionIconOverlay:
		Prerequisites: stratmiss.upgrade
	-Encyclopedia:

hailstorm.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Hailstorm Missiles
	Buildable:
		Prerequisites: ~player.gdi, gtek, ~bombard.strat, ~techlevel.high, ~!hypersonic.upgrade, ~!hammerhead.upgrade
		BuildPaletteOrder: 30
		IconPalette: chrometd
		Description: Equip the MLRS/HMLRS with Hailstorm Missiles.\n\nUpgrades: MLRS/HMLRS
	TooltipExtras:
		Strengths: + Increased damage, area of effect and range\n+ Fires six missiles per salvo
		Weaknesses: – Unguided
	Valued:
		Cost: 1000
	RenderSprites:
		Image: hailstorm.upgrade
	WithProductionIconOverlay:
		Prerequisites: hailstorm.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Bombardment Strategy.

hypersonic.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Hypersonic Missiles
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~seek.strat,~techlevel.high, ~!hailstorm.upgrade, ~!hammerhead.upgrade
		IconPalette: chrometd
		Description: Equip the MLRS/HMLRS with Hypersonic Missiles.\n\nUpgrades: MLRS/HMLRS
	TooltipExtras:
		Strengths: + Increased speed, precision and damage
	Valued:
		Cost: 1000
	RenderSprites:
		Image: hypersonic.upgrade
	WithProductionIconOverlay:
		Prerequisites: hypersonic.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Seek & Destroy Strategy.

hammerhead.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Hammerhead Missiles
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.gdi, gtek, ~hold.strat, ~techlevel.high, ~!hypersonic.upgrade, ~!hailstorm.upgrade
		IconPalette: chrometd
		Description: Equip the MLRS/HMLRS with Hammerhead Missiles.\n\nUpgrades: MLRS/HMLRS
	TooltipExtras:
		Strengths: + Increased damage and area of effect\n+ Briefly slows movement and rate of fire of impacted targets
	Valued:
		Cost: 1000
	RenderSprites:
		Image: hammerhead.upgrade
	WithProductionIconOverlay:
		Prerequisites: hammerhead.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Hold the Line Strategy.

sonic.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Sonic Amplifiers
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.zocom, gtek, ~techlevel.high
		IconPalette: chrometd
		Description: Improve the weapons of Disruptors and Sonic Towers.\n\nUpgrades: Sonic Tower\nUpgrades: Disruptor
	TooltipExtras:
		Strengths: + Increased damage\n+ Increased range\n+ Slows enemy movement and rate of fire
	Valued:
		Cost: 1000
	RenderSprites:
		Image: sonic.upgrade
	WithProductionIconOverlay:
		Prerequisites: sonic.upgrade
	EncyclopediaExtras:
		Subfaction: zocom

abur.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Afterburner
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.eagle, gtek, ~techlevel.high
		IconPalette: chrometd
		Description: Equips Orcas and Orca Bombers with afterburners.\n\nUpgrades: Orca\nUpgrades: Orca Bomber
	TooltipExtras:
		Strengths: + Added Afterburner for temporary speed boost
	Valued:
		Cost: 750
	RenderSprites:
		Image: abur.upgrade
	WithProductionIconOverlay:
		Prerequisites: abur.upgrade
	EncyclopediaExtras:
		Subfaction: eagle

bdrone.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Battle Drone
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.arc, gtek, ~techlevel.high
		IconPalette: chrometd
		Description: Allows construction of remotely piloted Battle Tanks.\n\nReplaces: Battle Tank
	TooltipExtras:
		Strengths: + Immunity to crew kill, chaos gas and mind control\n+ Reduced cost\n+ Increased rate of fire\n+ Self-repair
		Weaknesses: – Disabled if radar is down\n– Can be hacked
	Valued:
		Cost: 1250
	RenderSprites:
		Image: bdrone.upgrade
	AnnounceOnCreation:
		SoundNotification: UPG-bdrone
		Delay: 60
	WithProductionIconOverlay:
		Prerequisites: bdrone.upgrade
	EncyclopediaExtras:
		Subfaction: arc

railgun.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Railgun Titan
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.talon, upgc, ~techlevel.high
		IconPalette: chrometd
		Description: Allows construction of the Railgun Titan.\n\nReplaces: Titan
	TooltipExtras:
		Strengths: + Increased range\n+ Damages targets in a line
	Valued:
		Cost: 1000
	AnnounceOnCreation:
		SoundNotification: UPG-railgun
		Delay: 60
	RenderSprites:
		Image: railgun.upgrade
	WithProductionIconOverlay:
		Prerequisites: railgun.upgrade
	EncyclopediaExtras:
		Subfaction: talon

ionmam.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Ion Mammoth
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.zocom, upgc, ~techlevel.high
		IconPalette: chrometd
		Description: Allows construction of the Ion Mammoth Tank.\n\nReplaces: Mammoth Tank
	TooltipExtras:
		Strengths: + Increased range\n+ Increased area of effect
	Valued:
		Cost: 1000
	RenderSprites:
		Image: ionmam.upgrade
	AnnounceOnCreation:
		SoundNotification: UPG-ionmam
		Delay: 60
	WithProductionIconOverlay:
		Prerequisites: ionmam.upgrade
	EncyclopediaExtras:
		Subfaction: zocom

hovermam.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Hover Mammoth
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.eagle, upgc, ~techlevel.high
		IconPalette: chrometd
		Description: Allows construction of the Hover Mammoth Tank.\n\nReplaces: Mammoth Tank
	TooltipExtras:
		Strengths: + Faster movement speed\n+ Can traverse water
	Valued:
		Cost: 1000
	RenderSprites:
		Image: hovermam.upgrade
	AnnounceOnCreation:
		SoundNotification: UPG-hovermam
		Delay: 60
	WithProductionIconOverlay:
		Prerequisites: hovermam.upgrade
	EncyclopediaExtras:
		Subfaction: eagle

mdrone.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Research: Mammoth Drone
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.arc, upgc, ~techlevel.high
		IconPalette: chrometd
		Description: Allows construction of remotely piloted Mammoth Tanks.\n\nReplaces: Mammoth Tank
	TooltipExtras:
		Strengths: + Immunity to crew kill, chaos gas and mind control\n+ Reduced cost\n+ Increased rate of fire\n+ Improved self-repair
		Weaknesses: – Disabled if radar is down\n– Can be hacked
	Valued:
		Cost: 1000
	RenderSprites:
		Image: mdrone.upgrade
	AnnounceOnCreation:
		SoundNotification: UPG-mdrone
		Delay: 60
	WithProductionIconOverlay:
		Prerequisites: mdrone.upgrade
	EncyclopediaExtras:
		Subfaction: arc

gyro.upgrade:
	Inherits: ^GDIUpgrade
	Tooltip:
		Name: Upgrade: Gyro Stabilizers
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.talon, upgc, ~techlevel.high
		IconPalette: chrometd
		Description: Allows mech units to activate gyro stabilizers, increasing range but reducing rate of fire.\n\nUpgrades: Wolverine\nUpgrades: Titan\nUpgrades: Railgun Titan\nUpgrades: Juggernaut
	TooltipExtras:
		Strengths: + Added Gyro Stabilizers ability (increased range, reduced rate of fire)\n+ Increases Juggernaut range by 50%.
	Valued:
		Cost: 1000
	RenderSprites:
		Image: gyro.upgrade
	WithProductionIconOverlay:
		Prerequisites: gyro.upgrade
	EncyclopediaExtras:
		Subfaction: talon

#########################NOD#########
########################################

ANYCOVENANT:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Active Covenant
	Buildable:
		Description: Active Covenant
	ProvidesPrerequisite:

^NodUpgrade:
	Inherits: ^Upgrade
	Encyclopedia:
		Category: Nod/Upgrades

wrath.covenant:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Covenant of Wrath
	Buildable:
		BuildPaletteOrder: 13
		Prerequisites: ~player.nod, nod.covenants.available, !unity.covenant, !zeal.covenant, ~techlevel.medium
		Description: Required for:\n• Avatar\n• Assassin Squad\n• Compressed Plasma (+3 Banshee ammo)\n• Cyborg Firepower Upgrade\n\n
		IconPalette: chrometd
	TooltipExtras:
		Attributes: (!) Only ONE Covenant may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: wrath.covenant
	WithProductionIconOverlay:
		Prerequisites: wrath.covenant
	FreeActorCA@AssassinSquad:
		Actor: assassinsquad.provider

unity.covenant:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Covenant of Unity
	Buildable:
		BuildPaletteOrder: 14
		Prerequisites: ~player.nod, nod.covenants.available, !wrath.covenant, !zeal.covenant, ~techlevel.medium
		Description: Required for:\n• Cyborg Reaper\n• Hacker Cell\n• Siphoned Funds ($ from Hackers in Comms Center)\n• Augmented Fabrication Upgrade\n\n
		IconPalette: chrometd
	TooltipExtras:
		Attributes: (!) Only ONE Covenant may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: unity.covenant
	WithProductionIconOverlay:
		Prerequisites: unity.covenant
	FreeActorCA@HackerCell:
		Actor: hackercell.provider

zeal.covenant:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Covenant of Zeal
	Buildable:
		BuildPaletteOrder: 15
		Prerequisites: ~player.nod, nod.covenants.available, !unity.covenant, !wrath.covenant, ~techlevel.medium
		Description: Required for:\n• Enlightened\n• Confessor Cabal\n• Fast Harrassers (+15% Bike/Buggy/Howitzer speed)\n• Cyborg Speed Upgrade\n\n
		IconPalette: chrometd
	TooltipExtras:
		Attributes: (!) Only ONE Covenant may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: zeal.covenant
	WithProductionIconOverlay:
		Prerequisites: zeal.covenant
	FreeActorCA@ConfessorCabal:
		Actor: confessorcabal.provider

howi.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Research: Howitzer
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.nod, anyradar, ~techlevel.medium
		Description: Allows construction of Howitzers.\n\nReplaces: Artillery
	TooltipExtras:
		Strengths: + Increased projectile speed\n+ Increased movement speed\n+ Increased accuracy\n+ Has turret
		Weaknesses: – Reduced damage\n– Reduced range
	Valued:
		Cost: 1000
	RenderSprites:
		Image: howi.upgrade
	ProductionCostMultiplier@blackh:
		Multiplier: 0
		Prerequisites: radar.blackh
	ProductionTimeMultiplier@blackh:
		Multiplier: 0
		Prerequisites: radar.blackh
	WithProductionIconOverlay:
		Prerequisites: howi.upgrade

decoy.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Decoy Projectors
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.nod, anyradar, ~techlevel.medium
		Description: Grant Buggies the ability to project a pair of decoy Flame Tanks. Decoys provide small area of vision for a short time after being killed.\n\nUpgrades: Buggy
	TooltipExtras:
		Strengths: + Decoy projection ability
	Valued:
		Cost: 750
	RenderSprites:
		Image: decoy.upgrade
	WithProductionIconOverlay:
		Prerequisites: decoy.upgrade

sharv.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Stealth Harvester
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.nod, tmpl, ~techlevel.high
		Description: Allows construction of Stealth Harvesters which are cloaked while moving.\n\nUpgrades: Harvester
	TooltipExtras:
		Strengths: + Hidden while moving
		Weaknesses: – Reduced health
	Valued:
		Cost: 1500
	RenderSprites:
		Image: sharv.upgrade
	WithProductionIconOverlay:
		Prerequisites: sharv.upgrade

tibcore.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Tiberium Core Missiles
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.nod, tmpl, ~techlevel.high
		Description: Upgrade to Tiberium Core missiles.\n\nUpgrades: Recon Bike\nUpgrades: Stealth Tank\nUpgrades: Cyborg Rocket Soldier\nUpgrades: Rocket Soldier
		IconPalette: chrometd
	TooltipExtras:
		Strengths: + Increased range\n+ Increased damage\n+ Increased projectile speed
	Valued:
		Cost: 1000
	RenderSprites:
		Image: tibcore.upgrade
	AnnounceOnCreation:
		SoundNotification: UPG-tibcore
		Delay: 60
	WithProductionIconOverlay:
		Prerequisites: tibcore.upgrade

lastnk.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Laser Tanks
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.nod, tmpl, ~techlevel.high
		Description: Upgrades: Light Tank\nUpgrades: Battle Tank
	TooltipExtras:
		Strengths: + Increased damage vs infantry & light armor
		Weaknesses: – Reduced damage vs heavy armor
	Valued:
		Cost: 750
	RenderSprites:
		Image: lastnk.upgrade
	WithProductionIconOverlay:
		Prerequisites: lastnk.upgrade

blacknapalm.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Black Napalm
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.blackh, tmpl, ~techlevel.high
		Description: Superheated flame weapons that can melt armored targets.\n\nUpgrades: Heavy Flame Tank\nUpgrades: SSM Launcher\nUpgrades: Avatar
	TooltipExtras:
		Strengths: + Increased damage against vehicles and defenses
		Weaknesses: – Reduced rate of fire
	Valued:
		Cost: 1000
	RenderSprites:
		Image: blacknapalm.upgrade
	WithProductionIconOverlay:
		Prerequisites: blacknapalm.upgrade
	EncyclopediaExtras:
		Subfaction: blackh

microwave.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Intensified Microwaves
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.legion, tmpl, ~techlevel.high
		IconPalette: chrometd
		Description: Upgrades: Microwave Tank
	TooltipExtras:
		Strengths: + Kills crew of vehicles with less than 75% HP\n + Increased EMP duration for direct target\n+ EMP affects adjacent vehicles/defenses
	Valued:
		Cost: 1000
	RenderSprites:
		Image: microwave.upgrade
	AnnounceOnCreation:
		SoundNotification: UPG-microwave
		Delay: 60
	WithProductionIconOverlay:
		Prerequisites: microwave.upgrade
	EncyclopediaExtras:
		Subfaction: legion

quantum.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Quantum Capacitors
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.marked, tmpl, ~techlevel.high
		Description: Enhances heavy laser weapons.\n\nUpgrades: Obelisk of Light\nUpgrades: Avatar\nUpgrades: Laser Turret\nUpgrades: Venom\nUpgrades: Templar
	TooltipExtras:
		Strengths: + Increased range\n+ Increased damage
	Valued:
		Cost: 1000
	RenderSprites:
		Image: quantum.upgrade
	WithProductionIconOverlay:
		Prerequisites: quantum.upgrade
	EncyclopediaExtras:
		Subfaction: marked

hstk.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Research: Heavy Stealth Tank
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.shadow, tmpl, ~techlevel.high
		Description: Upgrades: Stealth Tank
	TooltipExtras:
		Strengths: + Increased damage\n+ Increased durability
		Weaknesses: – Reduced mobility\n– Increased cost\n– Increased cloaking delay
	Valued:
		Cost: 1000
	RenderSprites:
		Image: hstk.upgrade
	WithProductionIconOverlay:
		Prerequisites: hstk.upgrade
	EncyclopediaExtras:
		Subfaction: shadow

advcyber.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Research: Advanced Cybernetics
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.nod, tmpp, ~techlevel.high
		IconPalette: chrometd
		Description: Allows production of advanced cybernetic units.\n\nUnlocks: Cyborg Elite\nUnlocks: Avatar (Wrath Covenant Only)\nUnlocks: Cyborg Reaper (Unity Convenant only)\nUnlocks: Enlightened (Zeal Covenant only)
	Valued:
		Cost: 1000
	RenderSprites:
		Image: advcyber.upgrade
	WithProductionIconOverlay:
		Prerequisites: advcyber.upgrade

cyborgarmor.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Improved Cyborg Armor
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.nod, tmpp, ~techlevel.high
		IconPalette: chrometd
		Description: Improves cyborg armor.\n\nUpgrades: Cyborg\nUpgrades: Cyborg Rocket Soldier\nUpgrades: Chemical Warrior\nUpgrades: Acolyte/Templar\nUpgrades: Cyborg Mechanic\nUpgrades: Enlightened\nUpgrades: Cyborg Reaper\nUpgrades: Cyborg Elite
	TooltipExtras:
		Strengths: + 20% reduced damage taken\n+ Additional 10% reduced damage from explosives
	Valued:
		Cost: 1000
	RenderSprites:
		Image: cyborgarmor.upgrade
	WithProductionIconOverlay:
		Prerequisites: cyborgarmor.upgrade

covenant.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Covenant Dependent Upgrade
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.nod, anycovenant, ~!wrath.covenant, ~!unity.covenant, ~!zeal.covenant, ~techlevel.high
		IconPalette: chrometd
		Description: Upgrade depends on chosen covenant.\n\n• Wrath: Cyborg Firepower\n• Unity: Augmented Fabrication\n• Zeal: Cyborg Speed
	Valued:
		Cost: 1000
	RenderSprites:
		Image: covenant.upgrade
	WithProductionIconOverlay:
		Prerequisites: covenant.upgrade
	-Encyclopedia:

cyborgdmg.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Improved Cyborg Firepower
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.nod, tmpp, ~wrath.covenant, ~techlevel.high
		IconPalette: chrometd
		Description: Increases cyborg firepower.\n\nUpgrades: Cyborg\nUpgrades: Cyborg Rocket Soldier\nUpgrades: Chemical Warrior\nUpgrades: Acolyte/Templar\nUpgrades: Cyborg Mechanic\nUpgrades: Cyborg Elite
	TooltipExtras:
		Strengths: + 10% increased firepower
	Valued:
		Cost: 1000
	RenderSprites:
		Image: cyborgdmg.upgrade
	WithProductionIconOverlay:
		Prerequisites: cyborgdmg.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Wrath Covenant.

cyborgprod.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Augmented Fabrication
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.nod, tmpp, ~unity.covenant, ~techlevel.high
		IconPalette: chrometd
		Description: Leverage CABAL AI to increase cyborg production speed.\n\nUpgrades: Cyborg\nUpgrades: Cyborg Rocket Soldier\nUpgrades: Chemical Warrior\nUpgrades: Acolyte/Templar\nUpgrades: Cyborg Mechanic\nUpgrades: Cyborg Reaper\nUpgrades: Cyborg Elite
	TooltipExtras:
		Strengths: + 10% faster production
	Valued:
		Cost: 1000
	RenderSprites:
		Image: cyborgprod.upgrade
	WithProductionIconOverlay:
		Prerequisites: cyborgprod.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Unity Covenant.

cyborgspeed.upgrade:
	Inherits: ^NodUpgrade
	Tooltip:
		Name: Upgrade: Improved Cyborg Speed
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.nod, tmpp, ~zeal.covenant, ~techlevel.high
		IconPalette: chrometd
		Description: Increases cyborg movement speed.\n\nUpgrades: Cyborg\nUpgrades: Cyborg Rocket Soldier\nUpgrades: Chemical Warrior\nUpgrades: Acolyte/Templar\nUpgrades: Cyborg Mechanic\nUpgrades: Enlightened\nUpgrades: Cyborg Elite
	TooltipExtras:
		Strengths: + 20% faster movement
	Valued:
		Cost: 1000
	RenderSprites:
		Image: cyborgspeed.upgrade
	WithProductionIconOverlay:
		Prerequisites: cyborgspeed.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Zeal Covenant.

#########################SOVIET#########
########################################

ANYDOCTRINE:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Active Doctrine
	Buildable:
		Description: Active Doctrine
	ProvidesPrerequisite:

^SovietUpgrade:
	Inherits: ^Upgrade
	Encyclopedia:
		Category: Soviets/Upgrades

infantry.doctrine:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Doctrine: Infantry
	Buildable:
		BuildPaletteOrder: 4
		Prerequisites: ~player.soviet, !infantry.doctrine, !armor.doctrine, !arty.doctrine, playerxp.level1, ~techlevel.medium
		Description: Specialize in infantry:\n• Troop Crawler\n• Overlord Tank (replaces Mammoth Tank)\n• Improved Paratroopers Upgrade\n• Heroes of the Union\n• Cloning Vat\n\n
	TooltipExtras:
		Attributes: (!) Only ONE Doctrine may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: infantry.doctrine
	WithProductionIconOverlay:
		Prerequisites: infantry.doctrine

armor.doctrine:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Doctrine: Armor
	Buildable:
		BuildPaletteOrder: 5
		Prerequisites: ~player.soviet, !infantry.doctrine, !armor.doctrine, !arty.doctrine, playerxp.level1, ~techlevel.medium
		Description: Specialize in tanks:\n• Rhino Tank (replaces Heavy Tank)\n• Apocalypse Tank (replaces Mammoth Tank)\n• Reactive Armor Upgrade\n• Tank Drop\n• Industrial Plant\n\n
	TooltipExtras:
		Attributes: (!) Only ONE Doctrine may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: armor.doctrine
	WithProductionIconOverlay:
		Prerequisites: armor.doctrine

arty.doctrine:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Doctrine: Artillery
	Buildable:
		BuildPaletteOrder: 6
		Prerequisites: ~player.soviet, !infantry.doctrine, !armor.doctrine, !arty.doctrine, playerxp.level1, ~techlevel.medium
		Description: Specialize in artillery:\n• Grad (replaces Katyusha)\n• Nuke Cannon\n• Rocket Pods Upgrade\n• Kill Zone\n• Munitions Plant\n\n
	TooltipExtras:
		Attributes: (!) Only ONE Doctrine may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: arty.doctrine
	WithProductionIconOverlay:
		Prerequisites: arty.doctrine

hazmatsoviet.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Heavy Hazmat Suits
	Buildable:
		BuildPaletteOrder: 19
		Prerequisites: ~player.soviet, anyradar, infantry.any, ~techlevel.medium
		Description: Infantry are equipped with heavy hazmat suits which provides protection against Tiberium and radiation.\n\nUpgrades: Infantry
	TooltipExtras:
		Strengths: + Tiberium immunity\n+ High resistance to irradiated terrain
		Weaknesses: – 33% slower movement across resources and rough terrain
	Valued:
		Cost: 850
	RenderSprites:
		Image: hazmats.upgrade
	WithProductionIconOverlay:
		Prerequisites: hazmatsoviet.upgrade

tdog.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Terror Dog
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.soviet, anyradar, ~techlevel.medium
		Description: Attack Dogs are strapped with explosives.\n\nReplaces: Attack Dog
	TooltipExtras:
		Strengths: + Able to damage vehicles and buildings
		Weaknesses: – Explodes on attack or on death\n– Increased cost
	Valued:
		Cost: 750
	RenderSprites:
		Image: tdog.upgrade
	WithProductionIconOverlay:
		Prerequisites: tdog.upgrade

lasher.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Lasher Tank
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.yuri, anyradar, ~techlevel.medium
		Description: Upgrades Heavy/Rhino tanks with infantry crushing attachment at the cost of some armor and firepower.\n\nReplaces: Heavy Tank\nReplaces: Rhino Tank
	TooltipExtras:
		Strengths: + 25% faster movement\n+ Improved crushing performance
		Weaknesses: – Reduced hit points\n– Reduced firepower
	Valued:
		Cost: 1000
	RenderSprites:
		Image: lasher.upgrade
	WithProductionIconOverlay:
		Prerequisites: lasher.upgrade
	EncyclopediaExtras:
		Subfaction: yuri

gattling.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Gattling BTR
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.yuri, anyradar, ~techlevel.medium
		Description: Upgrades BTRs with dual gattling cannons.\n\nReplaces: BTR
	TooltipExtras:
		Strengths: + Increased firepower
		Weaknesses: – Increased production cost
	Valued:
		Cost: 750
	RenderSprites:
		Image: gattling.upgrade
	WithProductionIconOverlay:
		Prerequisites: gattling.upgrade
	EncyclopediaExtras:
		Subfaction: yuri

v3.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: V3 Launcher
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.soviet, stek, ~techlevel.high
		Description: Allows construction of the V3 Launcher.\n\nReplaces: V2 Launcher
	TooltipExtras:
		Strengths: + Significantly increased range
		Weaknesses: – Slower projectiles\n– Missiles can be shot down\n– Increased cost
	Valued:
		Cost: 750
	RenderSprites:
		Image: v3.upgrade
	WithProductionIconOverlay:
		Prerequisites: v3.upgrade

ttrp.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Tesla Trooper
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.soviet, ~!player.iraq, stek, ~techlevel.high
		Description: Shock Troopers are phased out in favor of Tesla Troopers.\n\nReplaces: Shock Trooper
	TooltipExtras:
		Strengths: + Increased health\n+ Increased rate of fire\n+ Increased damage
		Weaknesses: – Increased cost
	Valued:
		Cost: 500
	RenderSprites:
		Image: ttrp.upgrade
	WithProductionIconOverlay:
		Prerequisites: ttrp.upgrade

deso.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Desolator
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.soviet, ~player.iraq, stek, ~techlevel.high
		Description: Rad Troopers are phased out in favor of Desolators.\n\nReplaces: Rad Trooper
	TooltipExtras:
		Strengths: + Increased health\n+ Increased damage\n+ Irradiates vehicles\n+ Desolate Ground ability
		Weaknesses: – Increased cost
	Valued:
		Cost: 500
	RenderSprites:
		Image: deso.upgrade
	WithProductionIconOverlay:
		Prerequisites: deso.upgrade
	EncyclopediaExtras:
		Subfaction: iraq

tarc.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Tesla Arcing
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.russia, stek, ~techlevel.medium
		Description: Upgrades the Tesla Tank and Tesla Track weapon.\n\nUpgrades: Tesla Tank\nUpgrades: Tesla Track
	TooltipExtras:
		Strengths: + Shots jump to up to 2 additional targets
	Valued:
		Cost: 1000
	RenderSprites:
		Image: tarc.upgrade
	WithProductionIconOverlay:
		Prerequisites: tarc.upgrade
	EncyclopediaExtras:
		Subfaction: russia

seismic.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Seismic Missiles
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.ukraine, stek, ~techlevel.high
		Description: Arms the Sukhoi with Seismic Missiles.\n\nUpgrades: Sukhoi
	TooltipExtras:
		Strengths: + High area damage vs structures and vehicles\n+ Slows movement and rate of fire of impacted targets
		Weaknesses: – Slow moving projectiles
	Valued:
		Cost: 1000
	RenderSprites:
		Image: seismic.upgrade
	AnnounceOnCreation:
		SoundNotification: UPG-seismic
		Delay: 60
	WithProductionIconOverlay:
		Prerequisites: seismic.upgrade
	EncyclopediaExtras:
		Subfaction: ukraine

doctrine.upgrade1:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Doctrine Dependent Upgrade
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.soviet, anydoctrine, playerxp.level2, ~!infantry.doctrine, ~!armor.doctrine, ~!arty.doctrine, ~techlevel.medium
		Description: Upgrade depends on chosen doctrine.\n\n• Infantry: Improved Paratroopers\n• Tanks: Reactive Armor\n• Artillery: Rocket Pods
	Valued:
		Cost: 750
	RenderSprites:
		Image: doctrine.upgrade1
	WithProductionIconOverlay:
		Prerequisites: doctrine.upgrade1

imppara.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Improved Paratroopers
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: playerxp.level2, ~player.soviet, ~!player.russia, ~!player.yuri, ~infantry.doctrine, ~techlevel.medium
		Description: Upgrades the Paratroopers support power.\n\nUpgrades: Paratroopers
	TooltipExtras:
		Strengths: + Additional units dropped
	Valued:
		Cost: 750
	RenderSprites:
		Image: imppara.upgrade
	WithProductionIconOverlay:
		Prerequisites: imppara.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Infantry Doctrine.

impstorm.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Improved Stormtroopers
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: playerxp.level2, ~player.russia, ~infantry.doctrine, ~techlevel.medium
		Description: Upgrades the Stormtroopers support power.\n\nUpgrades: Stormtroopers
	TooltipExtras:
		Strengths: + Additional units dropped
	Valued:
		Cost: 750
	RenderSprites:
		Image: impstorm.upgrade
	WithProductionIconOverlay:
		Prerequisites: impstorm.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Infantry Doctrine.

impmuta.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Improved Genetic Mutation
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: playerxp.level2, ~player.yuri, ~infantry.doctrine, ~techlevel.medium
		Description: Enhances Genetic Mutation Bomb and Brutes.\n\nUpgrades: Genetic Mutation Bomb\nUpgrades: Brute
	TooltipExtras:
		Strengths: + Increased area of effect (Mutation Bomb)\n+ Increased maximum number of targets (Mutation Bomb)\n+ Increased movement speed (Brute)
	Valued:
		Cost: 750
	RenderSprites:
		Image: impmuta.upgrade
	WithProductionIconOverlay:
		Prerequisites: impmuta.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Infantry Doctrine.

reactive.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Reactive Armor
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.soviet, ~armor.doctrine, playerxp.level2, ~techlevel.high
		Description: Upgrades Rhino Tanks and BTRs with reactive armor for additional protection.\n\nUpgrades: Rhino Tank\nUpgrades: BTR\nUpgrades: Gattling BTR
	TooltipExtras:
		Strengths: + Increased armor
	Valued:
		Cost: 750
	RenderSprites:
		Image: reactive.upgrade
	WithProductionIconOverlay:
		Prerequisites: reactive.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Armor Doctrine.

rocketpods.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Upgrade: Rocket Pods
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.soviet, ~arty.doctrine, playerxp.level2, ~techlevel.high
		Description: Upgrades Grad and Hind rocket pods.\n\nUpgrades: Grad\nUpgrades: Hind
	TooltipExtras:
		Strengths: + Increased salvo size (Grad)\n+ Increased ammo & rate of fire (Hind)
	Valued:
		Cost: 750
	RenderSprites:
		Image: rocketpods.upgrade
	WithProductionIconOverlay:
		Prerequisites: rocketpods.upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Artillery Doctrine.

atomicengines.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Atomic Engines
	Buildable:
		BuildPaletteOrder: 35
		Prerequisites: ~player.soviet, npwr, ~techlevel.high
		Description: Upgrades tanks with volatile atomic engines.\n\nReplaces: Heavy/Lasher Tank\nReplaces: Rhino/Thrasher Tank\nReplaces: Eradicator\nReplaces: Mammoth Tank\nReplaces: Overlord Tank\nReplaces: Apocalypse Tank
	TooltipExtras:
		Strengths: + 25% faster movement
		Weaknesses: – Explodes on death
	Valued:
		Cost: 750
	RenderSprites:
		Image: atomicengines.upgrade
	WithProductionIconOverlay:
		Prerequisites: atomicengines.upgrade

erad.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Eradicator
	Buildable:
		BuildPaletteOrder: 35
		Prerequisites: ~player.iraq, npwr, ~techlevel.high
		Description: Upgrades Mammoth tanks with volatile radiation cannon.\n\nReplaces: Mammoth Tank
	TooltipExtras:
		Strengths: + High area damage vs infantry and vehicles\n+ Irradiates impacted vehicles\n+ Increased range
		Weaknesses: – Loses AA missiles\n– Reduced single-target anti-tank damage\n– Reduced damage vs buildings
	Valued:
		Cost: 1000
	RenderSprites:
		Image: erad.upgrade
	WithProductionIconOverlay:
		Prerequisites: erad.upgrade
	EncyclopediaExtras:
		Subfaction: iraq

doctrine.upgrade2:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Doctrine Dependent Upgrade
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.soviet, stek, anydoctrine, ~!infantry.doctrine, ~!armor.doctrine, ~!arty.doctrine, ~techlevel.high
		Description: Upgrade depends on chosen doctrine.\n\n• Infantry: Overlord Tank\n• Tanks: Apocalypse Tank\n• Artillery: Nuke Cannon
	Valued:
		Cost: 1000
	RenderSprites:
		Image: doctrine.upgrade2
	WithProductionIconOverlay:
		Prerequisites: doctrine.upgrade2

ovld.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Overlord Tank
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: playerxp.level3, ~player.soviet, ~infantry.doctrine, ~techlevel.high
		Description: Allows production of Overlord Tanks.\n\nReplaces: Mammoth Tank
	TooltipExtras:
		Strengths: + Increased health\n+ Increased firepower\n+ Increased range\n+ Empowers nearby infantry
		Weaknesses: – Reduced speed\n– Increased cost
	Valued:
		Cost: 1000
	RenderSprites:
		Image: ovld.upgrade
	WithProductionIconOverlay:
		Prerequisites: ovld.upgrade

apoc.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Apocalypse Tank
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: playerxp.level3, ~player.soviet, ~armor.doctrine, ~techlevel.high
		Description: Allows production of Apocalypse Tanks.\n\nReplaces: Mammoth Tank
	TooltipExtras:
		Strengths: + Increased firepower\n+ Increased health\n+ Increased range\n+ Increased splash damage
		Weaknesses: – Reduced speed\n– Increased cost
	Valued:
		Cost: 1000
	RenderSprites:
		Image: apoc.upgrade
	WithProductionIconOverlay:
		Prerequisites: apoc.upgrade

nukc.upgrade:
	Inherits: ^SovietUpgrade
	Tooltip:
		Name: Research: Nuke Cannon
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: playerxp.level3, ~player.soviet, ~arty.doctrine, ~techlevel.high
		Description: Allows production of Nuclear long range artillery.
	TooltipExtras:
		Strengths: + Strong vs Buildings, Defenses, Infantry, Light Armor\n+ Extremely long range
		Weaknesses: – Must deploy to fire\n– Slow rate of fire\n– Slow projectiles
	Valued:
		Cost: 1000
	RenderSprites:
		Image: nukc.upgrade
	WithProductionIconOverlay:
		Prerequisites: nukc.upgrade

#######################ALLIES############################
#########################################################

ANYCOALITION:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Active Coalition
	Buildable:
		Description: Active Coalition
	ProvidesPrerequisite:

^AlliedUpgrade:
	Inherits: ^Upgrade
	Encyclopedia:
		Category: Allies/Upgrades

economy.policy:
	Inherits: ^AlliedUpgrade
	Tooltip:
		Name: Policy: Economy
	Buildable:
		BuildPaletteOrder: 1
		Prerequisites: ~player.allies, influence.level1, !economy.policy, !defense.policy, !development.policy, ~techlevel.medium
		Description: Pursue economic policy.\n\n• Level 1: -10% cost of Refineries, Harvesters & MCVs\n• Level 2: -20% cost of Refineries, Harvesters & MCVs, +25% Construction Yard build radius\n• Level 3: -30% cost of Refineries, Harvesters & MCVs, +50% Ore Purifier income
	TooltipExtras:
		Attributes: (!) Only ONE Policy may be chosen.
	Valued:
		Cost: 300
	RenderSprites:
		Image: economy.policy
	WithProductionIconOverlay:
		Prerequisites: economy.policy

defense.policy:
	Inherits: ^AlliedUpgrade
	Tooltip:
		Name: Policy: Defense
	Buildable:
		BuildPaletteOrder: 1
		Prerequisites: ~player.allies, influence.level1, !economy.policy, !defense.policy, !development.policy, ~techlevel.medium
		Description: Pursue defense policy.\n\n• Level 1: +10% building HP\n• Level 2: +20% building HP, +40% building repair rate & +20% defense repair rate\n• Level 3: +30% building HP, +15% defense weapon damage
	TooltipExtras:
		Attributes: (!) Only ONE Policy may be chosen.
	Valued:
		Cost: 300
	RenderSprites:
		Image: defense.policy
	WithProductionIconOverlay:
		Prerequisites: defense.policy

development.policy:
	Inherits: ^AlliedUpgrade
	Tooltip:
		Name: Policy: Development
	Buildable:
		BuildPaletteOrder: 1
		Prerequisites: ~player.allies, influence.level1, !economy.policy, !defense.policy, !development.policy, ~techlevel.medium
		Description: Pursue development policy.\n\n• Level 1: +20% unit experience gain\n• Level 2: +40% unit experience gain, 10% discount on Tech Center & all upgrades\n• Level 3: +60% unit experience gain, rank 3 units gain additional health & damage
	TooltipExtras:
		Attributes: (!) Only ONE Policy may be chosen.
	Valued:
		Cost: 300
	RenderSprites:
		Image: development.policy
	WithProductionIconOverlay:
		Prerequisites: development.policy

greece.coalition:
	Inherits: ^AlliedUpgrade
	Tooltip:
		Name: Coalition: Greece
	Buildable:
		BuildPaletteOrder: 1
		Prerequisites: ~player.allies, alhq, !greece.coalition, !korea.coalition, !sweden.coalition, ~techlevel.medium
		Description: Form a coalition with Greece, required for:\n• Hoplite\n• Zeus Artillery\n• Helios Bomb
	TooltipExtras:
		Attributes: (!) Only ONE Coalition may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: greece.coalition
	AnnounceOnCreation:
		SpeechNotification: CoalitionActive
		Delay: 40
	WithProductionIconOverlay:
		Prerequisites: greece.coalition

korea.coalition:
	Inherits: ^AlliedUpgrade
	Tooltip:
		Name: Coalition: South Korea
	Buildable:
		BuildPaletteOrder: 2
		Prerequisites: ~player.allies, alhq, !greece.coalition, !korea.coalition, !sweden.coalition, ~techlevel.medium
		Description: Form a coalition with South Korea, required for:\n• Tiger Guard\n• Black Eagle\n• Black Sky Strike
	TooltipExtras:
		Attributes: (!) Only ONE Coalition may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: korea.coalition
	AnnounceOnCreation:
		SpeechNotification: CoalitionActive
		Delay: 40
	WithProductionIconOverlay:
		Prerequisites: korea.coalition

sweden.coalition:
	Inherits: ^AlliedUpgrade
	Tooltip:
		Name: Coalition: Sweden
	Buildable:
		BuildPaletteOrder: 3
		Prerequisites: ~player.allies, alhq, !greece.coalition, !korea.coalition, !sweden.coalition, ~techlevel.medium
		Description: Form a coalition with Sweden, required for:\n• Cryo Trooper\n• Cryo Launcher\n• Cryostorm
	TooltipExtras:
		Attributes: (!) Only ONE Coalition may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: sweden.coalition
	AnnounceOnCreation:
		SpeechNotification: CoalitionActive
		Delay: 40
	WithProductionIconOverlay:
		Prerequisites: sweden.coalition

rapc.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Research: Raider APC
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.allies, anyradar, ~techlevel.medium
		Description: Allows construction of the Raider APC.\n\nUpgrades: APC
	TooltipExtras:
		Strengths: + Adds strong anti-structure damage\n+ Increased splash damage\n+ Adds turret
		Weaknesses: – Increased cost\n– Slower projectile speed\n– Reduced speed
	Valued:
		Cost: 750
	RenderSprites:
		Image: rapc.upgrade
	WithProductionIconOverlay:
		Prerequisites: rapc.upgrade

optics.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Upgrade: Advanced Optics
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.allies, anyradar, ~techlevel.medium
		Description: Equips rangers with advanced optics which temporarily extends their vision and detection range.\n\nUpgrades: Ranger
	TooltipExtras:
		Strengths: + Rangers can temporarily increase their vision & detection range
	Valued:
		Cost: 1000
	RenderSprites:
		Image: optics.upgrade
	WithProductionIconOverlay:
		Prerequisites: optics.upgrade

entrench.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Upgrade: Entrenchment
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.france, anyradar, ~techlevel.medium
		Description: Enables Engineers to facilitate the building of defenses.\n\nUpgrades: Engineer
	TooltipExtras:
		Strengths: + Allows Engineers to deploy, enabling defensive structures to be built
	Valued:
		Cost: 1000
	RenderSprites:
		Image: entrench.upgrade
	WithProductionIconOverlay:
		Prerequisites: entrench.upgrade

cryw.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Upgrade: Cryo Warheads
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.allies, atek, ~techlevel.high
		Description: Arms certain rocket equipped units with Cryo warheads which cause targets to move more slowly and take increased damage.\n\nUpgrades: Rocket Soldier\nUpgrades: Longbow\nUpgrades: Guardian GI (USA only)\nUpgrades: Nighthawk (USA only)
	TooltipExtras:
		Strengths: + Equip with Cryo Warheads which slow movement and increase damage taken
	Valued:
		Cost: 1000
	RenderSprites:
		Image: cryw.upgrade
	WithProductionIconOverlay:
		Prerequisites: cryw.upgrade

pcan.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Research: Prism Cannon
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.allies, atek, ~techlevel.high
		Description: Allows construction of Prism Cannons.\n\nReplaces: Prism Tank
	TooltipExtras:
		Strengths: + Increased range\n+ Increased burst damage
		Weaknesses: – Reduced rate of fire\n– Reduced mobility
	Valued:
		Cost: 750
	RenderSprites:
		Image: pcan.upgrade
	WithProductionIconOverlay:
		Prerequisites: pcan.upgrade

apb.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Upgrade: Raufoss Ammo
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.england, atek, ~techlevel.high
		Description: Upgrades Snipers granting them Raufoss armor piercing rounds, which stun and damage vehicles.\n\nUpgrades: Sniper
	TooltipExtras:
		Strengths: + Can damage vehicles with AP rounds which slow movement and rate of fire
		Weaknesses: – Reduced range
	Valued:
		Cost: 1000
	RenderSprites:
		Image: apb.upgrade
	WithProductionIconOverlay:
		Prerequisites: apb.upgrade

airborne.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Upgrade: Advanced Airborne Training
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.usa, atek, ~techlevel.high
		Description: Improved training and logistics for airborne troops, resulting in greater effectiveness and faster preparation.\n\nUpgrades: Airdrop: Guardian GIs\nUpgrades: Airdrop: Grizzly Tanks
	TooltipExtras:
		Strengths: + Airborne units have veterancy\n+ Airborne units take 25% less time to prepare
	Valued:
		Cost: 1000
	RenderSprites:
		Image: airborne.upgrade
	WithProductionIconOverlay:
		Prerequisites: airborne.upgrade

tflx.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Upgrade: Temporal Flux
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.germany, pdox, ~techlevel.high
		Description: Upgrades Chrono Prisons granting them the ability to teleport through time. Improves Chrono Tank chronoshifting.\n\nUpgrades: Chrono Prison\nUpgrades: Chrono Tank
	TooltipExtras:
		Strengths: + Chrono Prison gains teleport ability\n+ Chrono Prison weapon range increased\n+ Chrono Tank can teleport twice before recharging
	Valued:
		Cost: 750
	RenderSprites:
		Image: tflx.upgrade
	WithProductionIconOverlay:
		Prerequisites: tflx.upgrade

charv.upgrade:
	Inherits: ^AlliedUpgrade
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Tooltip:
		Name: Upgrade: Chrono Miner
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.allies, alhq, ~techlevel.high
		Description: Allows construction of Chrono Miners which\nteleport back to refineries.\n\nUpgrades: Ore Truck
	TooltipExtras:
		Strengths: + Teleport back to refinery
	Valued:
		Cost: 1250
	RenderSprites:
		Image: charv.upgrade
	WithProductionIconOverlay:
		Prerequisites: charv.upgrade

################################SCRIN##############################
###################################################################

ANYALLEGIANCE:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Declared Allegiance
	Buildable:
		Description: Declared Allegiance
	ProvidesPrerequisite:

^ScrinUpgrade:
	Inherits: ^Upgrade
	Encyclopedia:
		Category: Scrin/Upgrades

loyalist.allegiance:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Allegiance: Loyalist
	Buildable:
		BuildPaletteOrder: 16
		Prerequisites: ~player.scrin, scrin.allegiances.available, !rebel.allegiance, !malefic.allegiance, ~techlevel.medium
		Description: Declare your allegiance and side with the Scrin Overlord.\n\nRequired for:\n• Eviscerator\n• Ruiner\n• Overlord's Wrath\n• 25% faster regrowth of resources near Colony Platforms\n\n
		IconPalette: chromes
	TooltipExtras:
		Attributes: (!) Only ONE allegiance may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: loyalist.allegiance
	WithProductionIconOverlay:
		Prerequisites: loyalist.allegiance

rebel.allegiance:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Allegiance: Rebel
	Buildable:
		BuildPaletteOrder: 17
		Prerequisites: ~player.scrin, scrin.allegiances.available, !loyalist.allegiance, !malefic.allegiance, ~techlevel.medium
		Description: Declare your allegiance and side with the Scrin Rebels.\n\nRequired for:\n• Impaler\n• Nullifier\n• Gateway\n• Colony Ships can produce structures on the move\n\n
		IconPalette: chromes
	TooltipExtras:
		Attributes: (!) Only ONE allegiance may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: rebel.allegiance
	WithProductionIconOverlay:
		Prerequisites: rebel.allegiance

malefic.allegiance:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Allegiance: Malefic
	Buildable:
		BuildPaletteOrder: 18
		Prerequisites: ~player.scrin, scrin.allegiances.available, !loyalist.allegiance, !rebel.allegiance, ~techlevel.medium
		Description: Declare your allegiance and side with the Malefic Scrin.\n\nRequired for:\n• Stalker\n• Darkener\n• Anathema\n• Voidspike\n\n
		IconPalette: chromes
	TooltipExtras:
		Attributes: (!) Only ONE allegiance may be chosen.
	Valued:
		Cost: 500
	RenderSprites:
		Image: malefic.allegiance
	WithProductionIconOverlay:
		Prerequisites: malefic.allegiance
	FreeActorCA@VoidSpike:
		Actor: voidspike.provider

hyper.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Hypercharge
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.scrin, anyradar, ~techlevel.medium
		Description: Seekers and Lacerators gain Hypercharge ability, allow them to fire an uninterrupted barrage for a limited time. After use, weapon damage and speed is reduced for a short time.\n\nUpgrades: Seeker\nUpgrades: Lacerator
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Hypercharge ability
	Valued:
		Cost: 1250
	RenderSprites:
		Image: hyper.upgrade
	WithProductionIconOverlay:
		Prerequisites: hyper.upgrade

allegiance.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Allegiance Dependent Upgrade
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.scrin, anyradar, anyallegiance, ~!loyalist.allegiance, ~!rebel.allegiance, ~!malefic.allegiance, ~techlevel.medium
		IconPalette: chromes
		Description: Upgrade depends on chosen allegiance.\n\n• Loyalist: Eviscerator research\n• Rebel: Impaler research\n• Malefic: Stalker research
	Valued:
		Cost: 750
	RenderSprites:
		Image: allegiance.upgrade
	WithProductionIconOverlay:
		Prerequisites: allegiance.upgrade
	-Encyclopedia:

evis.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Research: Eviscerator
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.scrin, anyradar, ~loyalist.allegiance, ~techlevel.medium
		Description: Replaces: Ravager
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Increased damage\n+ Increased health\n+ Increased range\n+ Increased rate of fire\n+ Affected by Resource Conversion upgrade
		Weaknesses: – Increased cost
	Valued:
		Cost: 750
	RenderSprites:
		Image: evis.upgrade
	WithProductionIconOverlay:
		Prerequisites: evis.upgrade

impl.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Research: Impaler
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.scrin, anyradar, ~rebel.allegiance, ~techlevel.medium
		Description: Replaces: Ravager
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Increased range\n+ Increased damage\n+ Increased health\n+ Slows targets
		Weaknesses: – Increased cost\n– Reduced speed\n– Reduced rate of fire
	Valued:
		Cost: 750
	RenderSprites:
		Image: impl.upgrade
	WithProductionIconOverlay:
		Prerequisites: impl.upgrade

stlk.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Research: Stalker
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.scrin, anyradar, ~malefic.allegiance, ~techlevel.medium
		Description: Replaces: Ravager
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Increased damage\n+ Increased health\n+ Cloaking ability with speed boost
		Weaknesses: – Increased cost\n– Reduced range
	Valued:
		Cost: 750
	RenderSprites:
		Image: stlk.upgrade
	WithProductionIconOverlay:
		Prerequisites: stlk.upgrade

shrw.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Research: Shard Walker
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.reaper, anyradar, ~techlevel.medium
		Description: Allows production of Shard Walkers, a heavier and more powerful variant of the Gun Walker.\n\nReplaces: Gun Walker
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Increased durability\n+ Increased damage
		Weaknesses: – Slower movement\n– Slower projectiles
	Valued:
		Cost: 750
	RenderSprites:
		Image: shrw.upgrade
	WithProductionIconOverlay:
		Prerequisites: shrw.upgrade

advart.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Advanced Articulators
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: ~player.traveler, anyradar, ~techlevel.medium
		Description: Enable Scrin infantry to move faster.\n\nUpgrades: Scrin Infantry
		IconPalette: chromes
	TooltipExtras:
		Strengths: + 15% faster movement
	Valued:
		Cost: 1250
	RenderSprites:
		Image: advart.upgrade
	WithProductionIconOverlay:
		Prerequisites: advart.upgrade

carapace.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Hardened Carapace
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.scrin, scrt, infantry.any, ~techlevel.high
		Description: Infantry gain a hardened carapace which provides protection against explosives.\n\nUpgrades: Infantry
		IconPalette: chromes
	TooltipExtras:
		Strengths: + 40% reduced damage from explosives\n+ 20% reduced damage from incendiary explosives
	Valued:
		Cost: 1500
	RenderSprites:
		Image: carapace.upgrade
	WithProductionIconOverlay:
		Prerequisites: carapace.upgrade

blink.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Blink Packs
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.scrin, scrt, ~techlevel.high
		Description: Equips Intruders and Watchers with blink packs allowing them to teleport.\n\nUpgrades: Intruder\nUpgrades: Marauder\nUpgrades: Watcher
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Teleport ability
	Valued:
		Cost: 750
	RenderSprites:
		Image: blink.upgrade
	WithProductionIconOverlay:
		Prerequisites: blink.upgrade

resconv.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Resource Conversion
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.scrin, scrt, ~techlevel.high
		Description: Weapons of certain units are empowered by Tiberium and other resources. Reaper Tripod gains additional charges.\n\nUpgrades: Devourer\nUpgrades: Ruiner\nUpgrades: Reaper Tripod\nUpgrades: Eviscerator
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Increased damage when charged\n+ Doubled charge capacity (Reaper Tripod only)
	Valued:
		Cost: 1000
	RenderSprites:
		Image: resconv.upgrade
	WithProductionIconOverlay:
		Prerequisites: resconv.upgrade

ioncon.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Ion Conduits
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.scrin, scrt, ~techlevel.high
		Description: Allows Storm Columns and Stormcrawlers to store ion energy and release it into the atmosphere, creating a localized Ion Storm.\n\nUpgrades: Storm Column\nUpgrades: Stormcrawler
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Increases damage and range over time when active\n+ Reduces incoming damage over time when active\n+ Ion storm can damage enemy units and structures
	Valued:
		Cost: 1250
	RenderSprites:
		Image: ioncon.upgrade
	WithProductionIconOverlay:
		Prerequisites: ioncon.upgrade

regen.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Regenerative Hull
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.scrin, scrt, ~techlevel.high
		Description: Scrin aircraft and Stormcrawlers will regenerate health over time when not in combat.\n\nUpgrades: Stormrider\nUpgrades: Tormentor\nUpgrades: Enervator\nUpgrades: Devastator Warship\nUpgrades: Planetary Assault Carrier\nUpgrades: Mothership\nUpgrades: Stormcrawler
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Out-of-combat health regeneration
	Valued:
		Cost: 1500
	RenderSprites:
		Image: regen.upgrade
	WithProductionIconOverlay:
		Prerequisites: regen.upgrade

stellar.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Stellar Fusion Cannon
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.harbinger, scrt, ~techlevel.high
		Description: Devastator Warships upgraded with stellar fusion cannon.\n\nUpgrades: Devastator Warship
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Increased damage\n+ Increased area of effect\n+ Improved accuracy
		Weaknesses: – Must charge before firing
	Valued:
		Cost: 1000
	RenderSprites:
		Image: stellar.upgrade
	WithProductionIconOverlay:
		Prerequisites: stellar.upgrade

coalescence.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Coalescence
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: ~player.collector, scrt, ~techlevel.high
		Description: On death or when deployed, Leechers will transform into a ball of bio-matter which heals nearby allies and eventually coalesces into a new Leecher.\n\nUpgrades: Leecher
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Leecher able to resurrect\n+ Heals allies while in coalescence form
		Weaknesses: – No longer regenerates while dealing damage
	Valued:
		Cost: 1000
	RenderSprites:
		Image: coalescence.upgrade
	WithProductionIconOverlay:
		Prerequisites: coalescence.upgrade

shields.upgrade:
	Inherits: ^ScrinUpgrade
	Tooltip:
		Name: Upgrade: Shield Generation
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: ~player.scrin, sign, ~techlevel.high
		Description: Equip Scrin airborne units and certain ground units with shields.\n\nUpgrades: Interloper\nUpgrades: Stormcrawler\nUpgrades: Stormrider\nUpgrades: Tormentor\nUpgrades: Enervator\nUpgrades: Devastator Warship\nUpgrades: Planetary Assault Carrier\nUpgrades: Mothership
		IconPalette: chromes
	TooltipExtras:
		Strengths: + Grants an additonal health pool\n+ Resistance to EMP for aircraft
	Valued:
		Cost: 1500
	RenderSprites:
		Image: shields.upgrade
	WithProductionIconOverlay:
		Prerequisites: shields.upgrade
