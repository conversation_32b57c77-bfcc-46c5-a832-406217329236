^AACannon:
	ReloadDelay: 10
	Range: 8c0
	Report: aacanon3.aud
	ValidTargets: Air, AirSmall
	Projectile: Bullet
		Speed: 3c0
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Range: 0, 0c64, 0c256, 3c0
		Falloff: 100, 100, 30, 15
		Damage: 2000
		ValidTargets: Air
		ValidRelationships: Enemy, Neutral
		DamageTypes: Prone50<PERSON><PERSON>cent, <PERSON><PERSON><PERSON><PERSON>, Default<PERSON>eath
	Warhead@smallDamage: SpreadDamage
		Spread: 128
		Falloff: 100, 50, 14, 0
		Damage: 2000
		ValidTargets: AirSmall
		ValidRelationships: Enemy, Neutral
		DamageTypes: Prone50Percent, <PERSON><PERSON><PERSON>rone, DefaultDeath
	Warhead@2Eff: CreateEffect
		Explosions: small_explosion_air
		ValidTargets: Air, AirSmall, Ground, Water, Trees
		ImpactSounds: flakhit1.aud, flakhit2.aud, flakhit3.aud
		ImpactSoundChance: 100
		Inaccuracy: 171

ZSU-23:
	Inherits: ^AACannon
	ValidTargets: Air, AirSmall, ICBM
	ReloadDelay: 8
	Warhead@1Dam: SpreadDamage
		Damage: 2500
		Range: 0, 0c64, 0c256, 3c768
	Warhead@smallDamage: SpreadDamage
		Damage: 2500
		ValidTargets: AirSmall, ICBM
		Range: 0, 0c64, 0c256, 1c768
		Falloff: 100, 70, 30, 10
	Warhead@2Eff: CreateEffect
		ValidTargets: Air, AirSmall, ICBM, Ground, Water, Trees

FLAK-23-AA:
	Inherits: ^AACannon
	Range: 7c512
	Report: flak1.aud, flak2.aud
	Warhead@1Dam: SpreadDamage
		Damage: 1500
	Warhead@smallDamage: SpreadDamage
		Damage: 1500

FLAK-23-AG:
	Inherits: ^AACannon
	Report: flak1.aud, flak2.aud
	Range: 4c768
	ValidTargets: Ground, Water
	Projectile: Bullet
		LaunchAngle: 62
		Speed: 682
		Image: FLAKBALL
		Blockable: True
		Inaccuracy: 341
	Warhead@1Dam: SpreadDamage
		ValidRelationships: Enemy, Neutral, Ally
		-Range:
		Damage: 1750
		Spread: 0c128
		Falloff: 100, 37, 14, 5, 0
		Versus:
			None: 100
			Wood: 10
			Light: 75
			Heavy: 10
			Concrete: 10
			Brick: 20
		ValidTargets: Ground, Water
	-Warhead@smallDamage:
	Warhead@2Eff: CreateEffect
		Explosions: flak_explosion_ground
		ValidTargets: Ground, Ship, Trees
		Inaccuracy: 341
	Warhead@3EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: splashm1.aud, splashm2.aud, splashm3.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

FLAK-SEAS-AA:
	Inherits: FLAK-23-AA
	Report: vflaat1a.aud, vflaat1b.aud
	Range: 8c0
	Warhead@1Dam: SpreadDamage
		Damage: 2200
	Warhead@smallDamage: SpreadDamage
		Damage: 2200

FLAK-SEAS-AG:
	Inherits: FLAK-23-AG
	Range: 5c256
	Report: vflaat1a.aud, vflaat1b.aud
	Projectile: Bullet
		LaunchAngle: 82
		Speed: 768
	Warhead@1Dam: SpreadDamage
		Damage: 2200
		Versus:
			None: 150
			Wood: 60
			Light: 40
			Heavy: 30
			Concrete: 20

Gatt:
	Inherits: ^AACannon
	ReloadDelay: 5
	-Report:
	ValidTargets: Air, AirSmall, ICBM
	Warhead@1Dam: SpreadDamage
		Damage: 1300
		Range: 0, 0c64, 0c256, 3c768
	Warhead@smallDamage: SpreadDamage
		Damage: 1300
		ValidTargets: AirSmall, ICBM
		Range: 0, 0c64, 0c256, 1c768
		Falloff: 100, 70, 30, 10
	Warhead@2Eff: CreateEffect
		Explosions: piff
		ValidTargets: Air, AirSmall, ICBM
		ImpactSounds: sirodefa.aud, sirodefb.aud, sirodefc.aud, sirodefd
		ImpactSoundChance: 60

MGatt:
	Inherits: ^AACannon
	ReloadDelay: 5
	-Report:
	Range: 7c512
	Warhead@1Dam: SpreadDamage
		Damage: 665
	Warhead@smallDamage: SpreadDamage
		Damage: 665
	Warhead@2Eff: CreateEffect
		Explosions: piff
		ValidTargets: Air, AirSmall
		ImpactSounds: sirodefa.aud, sirodefb.aud, sirodefc.aud, sirodefd
		ImpactSoundChance: 60

^HeavyMG:
	ReloadDelay: 30
	Range: 6c0
	Report: gun13.aud
	Projectile: Bullet
		Speed: 2c341
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 2500
		Versus:
			None: 120
			Wood: 60
			Light: 72
			Heavy: 28
			Concrete: 28
			Brick: 20
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@2Eff: CreateEffect
		Explosions: piffs
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piffs
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

^LightMG:
	Inherits: ^HeavyMG
	Warhead@1Dam: SpreadDamage
		Damage: 1000
		Versus:
			None: 150
			Wood: 10
			Light: 40
			Heavy: 10
			Concrete: 10
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath
	Warhead@2Eff: CreateEffect
		Explosions: piff
		Inaccuracy: 171
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		Inaccuracy: 171

Vulcan:
	Inherits: ^HeavyMG
	-Projectile:
	Projectile: PlasmaBeam
		ImpactTicks: 0, 2, 4, 6, 8, 10
		Radius: 1
		Duration: 13
		TrackTarget: true
		Blockable: true
		Invisible: true
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 1000
		Versus:
			None: 220
			Wood: 56
			Light: 58
			Heavy: 15
			Concrete: 35
			Brick: 20
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath

GTChainGun:
	Inherits: Vulcan
	Report: mg42.aud

ChainGun:
	Inherits: ^HeavyMG
	Report: gun13.aud
	ReloadDelay: 16
	Range: 5c512
	MinRange: 0c768
	Projectile: Bullet
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Damage: 7800
		Versus:
			None: 100
			Wood: 15
			Light: 25
			Heavy: 18
			Concrete: 15
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath, AirToGround

KamovChainGun:
	Inherits: ChainGun
	Warhead@1Dam: SpreadDamage
		Damage: 8600

GAU8:
	Inherits: ^HeavyMG
	Range: 6c0
	ReloadDelay: 80
	BurstDelays: 1
	Burst: 15
	-Report:
	StartBurstReport: brrrrt1.aud, brrrrt2.aud
	FirstBurstTargetOffset: -512,0,0
	FollowingBurstTargetOffset: 448,0,0
	MinRange: 0c256
	Projectile: Bullet
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 1750
		Versus:
			None: 200
			Wood: 0
			Light: 100
			Heavy: 85
			Concrete: 30
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath, AirToGround
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Building, Wall

GAU8.Building:
	Inherits: GAU8
	-StartBurstReport:
	-Projectile:
	-FirstBurstTargetOffset:
	-FollowingBurstTargetOffset:
	-Warhead@2Smu:
	-Warhead@2Eff:
	-Warhead@3EffWater:
	TargetActorCenter: true
	Projectile: InstantHit
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 1280
		Versus:
			Wood: 100
		ValidTargets: Building
		InvalidTargets: Defense

Vulcan2:
	Inherits: ^LightMG
	ReloadDelay: 50
	Burst: 3
	Report: tsgun4.aud
	Warhead@1Dam: SpreadDamage
		Damage: 5000
	Warhead@2Eff: CreateEffect
		Explosions: piffs
		Inaccuracy: 0

Vulcan3:
	Inherits: ^LightMG
	Range: 5c0
	Burst: 3
	Report: infgun3.aud
	Warhead@1Dam: SpreadDamage
		Damage: 1700
		Versus:
			Wood: 30
			Concrete: 18
			Heavy: 13
	Warhead@2Eff: CreateEffect
		Explosions: piffs
		Inaccuracy: 0

ChainGun.Yak.R:
	Inherits: ^HeavyMG
	ReloadDelay: 60
	Burst: 4
	BurstDelays: 5
	Range: 6c0
	MinRange: 0c768
	Projectile: InstantHit
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Damage: 4000
		Versus:
			None: 100
			Wood: 35
			Light: 55
			Heavy: 25
			Concrete: 25
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath, AirToGround

ChainGun.Yak.L:
	Inherits: ChainGun.Yak.R
	-Report:

ChainGun.Yak.AA:
	Inherits: ChainGun.Yak.R
	ValidTargets: Air, AirSmall
	-MinRange:
	Warhead@1Dam: SpreadDamage
		Range: 0, 0c64, 0c256, 1c768
		Falloff: 100, 100, 30, 15
		ValidTargets: Air
		ValidRelationships: Enemy, Neutral
		Damage: 1500
		Versus:
			None: 100
			Wood: 100
			Light: 100
			Heavy: 100
			Concrete: 100
			Brick: 100
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@smallDamage: SpreadDamage
		Spread: 341
		Falloff: 100, 50, 14, 0
		Damage: 1500
		ValidTargets: AirSmall
		ValidRelationships: Enemy, Neutral
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath
	Warhead@2Eff: CreateEffect
		Explosions: small_explosion_air
		ValidTargets: Air
		Inaccuracy: 0c341

ChainGun.P51.R:
	Inherits: ^HeavyMG
	Burst: 9
	Range: 7c0
	ReloadDelay: 80
	BurstDelays: 2
	-Report:
	StartBurstReport: gun13ext.aud
	FirstBurstTargetOffset: -1792,213,0
	FollowingBurstTargetOffset: 448,0,0
	MinRange: 0c768
	Projectile: Bullet
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 3000
		Versus:
			None: 200
			Wood: 10
			Light: 80
			Heavy: 25
			Concrete: 25

ChainGun.P51.L:
	Inherits: ChainGun.P51.R
	FirstBurstTargetOffset: -1792,-213,0

Pistol:
	Inherits: ^LightMG
	Burst: 10
	BurstDelays: 8
	ReloadDelay: 80
	Range: 3c0
	Report: gun27.aud
	Warhead@1Dam: SpreadDamage
		Damage: 134
		Versus:
			None: 100
	Warhead@2Eff: CreateEffect
		Explosions: piff
		Inaccuracy: 128
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		Inaccuracy: 128

PistolTD:
	Inherits: Pistol
	Report: gun18.aud

CommissarPistol:
	Inherits: Pistol
	Report: icivattb.aud, icivatta.aud
	Range: 6c0
	Warhead@1Dam: SpreadDamage
		Damage: 900

M1Carbine:
	Inherits: ^LightMG
	ReloadDelay: 20
	Range: 5c0
	Report: gun11.aud
	Warhead@1Dam: SpreadDamage
		Versus:
			Concrete: 18
			Wood: 30
			Heavy: 13
	Warhead@2Eff2: CreateEffect
		Delay: 2
		Explosions: piff
		ValidTargets: Ground, Air, AirSmall, Trees
		Inaccuracy: 171
	Warhead@3EffWater2: CreateEffect
		Delay: 2
		ValidTargets: Water, Underwater
		InvalidTargets: Bridge
		Explosions: water_piff
		Inaccuracy: 171
	Warhead@2Eff3: CreateEffect
		Delay: 4
		Explosions: piff
		ValidTargets: Ground, Air, AirSmall, Trees
		Inaccuracy: 171
	Warhead@3EffWater3: CreateEffect
		Delay: 4
		ValidTargets: Water, Underwater
		InvalidTargets: Bridge
		Explosions: water_piff
		Inaccuracy: 171

M14:
	Inherits: M1Carbine
	Report: islyat1a.aud, islyat1b.aud

M16Carbine:
	Inherits: M1Carbine
	Report: mgun2.aud

M1CarbineBATF:
	Inherits: M1Carbine
	Range: 5c768
	Burst: 3
	BurstDelays: 2
	-Report:
	StartBurstReport: baocatta.aud, baocattb.aud, baocattc.aud
	ReloadDelay: 10
	Warhead@1Dam: SpreadDamage
		Damage: 750

M16CarbineBATF:
	Inherits: M1CarbineBATF

BATFGun:
	Inherits: M1CarbineBATF

M60mg:
	Inherits: ^LightMG
	Range: 4c0
	Report: pillbox1.aud
	Burst: 5
	Warhead@1Dam: SpreadDamage
		Versus:
			Light: 75
	Warhead@2Eff: CreateEffect
		Explosions: piffs
		Inaccuracy: 0

M60mgTD:
	Inherits: M60mg
	Report: m60.aud

M60mgJJ:
	Inherits: M60mg
	Range: 4c768
	Projectile: Bullet
		Blockable: false
	Report: vblhatta.aud, vblhattb.aud
	Burst: 3
	Warhead@1Dam: SpreadDamage
		Damage: 3250
		Versus:
			None: 100
			Light: 40
			Heavy: 4
			Concrete: 4
			Wood: 4
			Brick: 4
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath, AirToGround

M60mgJJ.ground:
	Inherits: M60mgJJ
	Projectile: Bullet
		Blockable: true

M60mgJJAA:
	Inherits: M60mgJJ
	Range: 5c512
	ValidTargets: Air, AirSmall
	Warhead@1Dam: SpreadDamage
		ValidTargets: Air, AirSmall
		Damage: 600

M60mgNHAW:
	Inherits: M60mg
	Range: 4c768
	Projectile: Bullet
		Blockable: false
	Report: vblhatta.aud, vblhattb.aud
	Burst: 3
	Warhead@1Dam: SpreadDamage
		Versus:
			None: 165
			Light: 50
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath, AirToGround

M60mgIFV:
	Inherits: M60mg
	Range: 5c768
	-Report:
	StartBurstReport: ifvmg1.aud, ifvmg2.aud
	BurstDelays: 2
	Burst: 3
	ReloadDelay: 50
	Warhead@1Dam: SpreadDamage
		Damage: 2500
		Versus:
			Wood: 40
			Light: 90
			None: 100
		DamageTypes: Prone50Percent, BulletDeath
	Warhead@PercDam: HealthPercentageDamage
		Spread: 42
		Damage: 20
		Versus:
			Light: 1
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
		ValidTargets: Infantry
	Warhead@2Eff: CreateEffect
		Explosions: piff
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff

M60mgMD:
	Inherits: M60mgTD
	ReloadDelay: 40

M60mgMD.Attached:
	Range: 5c256
	Inherits: M60mgMD

MGattG:
	Inherits: ^HeavyMG
	Range: 5c0
	ReloadDelay: 5
	-Report:
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 300
		Versus:
			None: 160
			Wood: 35
			Concrete: 35
			Light: 155
			Heavy: 35
		DamageTypes: Prone50Percent, BulletDeath
	Warhead@PercDam: HealthPercentageDamage
		Spread: 42
		Damage: 6
		Versus:
			Light: 1
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
		ValidTargets: Infantry
	Warhead@2Eff: CreateEffect
		Explosions: piffs
		Inaccuracy: 64

^SnipeWeapon:
	ReloadDelay: 80
	Range: 5c512
	Report: gun5.aud
	InvalidTargets: Vehicle, Ship, Water, Structure
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 15000
		Versus:
			Light: 15
			Brick: 15
		ValidTargets: Barrel, Infantry, Mine, Wall, Husk
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath
	Warhead@2Eff: CreateEffect
		Explosions: piff
		ImpactActors: false
		ValidTargets: Ground, Water, Air, AirSmall

SilencedPPK:
	Inherits: ^SnipeWeapon
	Range: 3c512
	Report: silppk.aud
	Warhead@1Dam: SpreadDamage
		Spread: 128
	Warhead@2Eff: CreateEffect
		Explosions: piff
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

SilencedPPKBATF:
	Inherits: SilencedPPK
	Range: 5c0

Colt45:
	Inherits: ^SnipeWeapon
	ReloadDelay: 5
	Burst: 2
	BurstDelays: 2
	Range: 7c0
	Warhead@1Dam: SpreadDamage
		Damage: 4700
		Versus:
			Light: 25
	Warhead@2Eff: CreateEffect
		Explosions: piff
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

Colt45BATF:
	Inherits: Colt45
	Range: 5c768

SMG:
	Inherits: ^SnipeWeapon
	Report: silppk.aud
	ReloadDelay: 10
	Range: 7c0
	Warhead@1Dam: SpreadDamage
		Damage: 25000
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@2Eff: CreateEffect
		Explosions: piff
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

SMGBATF:
	Inherits: SMG
	Range: 5c768

MP5:
	StartBurstReport: iseaatta.aud, iseaattb.aud
	ReloadDelay: 6
	Burst: 3
	BurstDelays: 2
	Range: 6c0
	InvalidTargets: Structure
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 1570
		Versus:
			None: 100
			Wood: 15
			Light: 35
			Heavy: 15
			Concrete: 10
			Brick: 10
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@2Eff: CreateEffect
		Explosions: piff
		Inaccuracy: 171
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		Inaccuracy: 171
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

MP5BATF:
	Inherits: MP5
	Range: 5c768

AKM:
	StartBurstReport: mg11.aud
	ReloadDelay: 8
	Burst: 3
	BurstDelays: 2
	Range: 7c0
	InvalidTargets: Structure
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 4700
		Versus:
			None: 100
			Wood: 10
			Light: 30
			Heavy: 10
			Concrete: 10
			Brick: 10
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@2Eff: CreateEffect
		Explosions: piff
		Inaccuracy: 171
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		Inaccuracy: 171
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

AKMBATF:
	Inherits: AKM
	Range: 5c768

SNIPER:
	ReloadDelay: 120
	Range: 8c512
	Report: snipe.aud
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 25000
		Versus:
			Wood: 1
			Concrete: 1
			Brick: 1
			Heavy: 2
			Light: 2
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
		InvalidTargets: Infantry
	Warhead@2Dam: HealthPercentageDamage
		Spread: 42
		Damage: 300
		ValidTargets: Infantry
		DamageTypes: BulletDeath
		Versus:
			Light: 25
	Warhead@2Eff: CreateEffect
		Explosions: piff
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piff
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

SNIPER.vehicle:
	Inherits: SNIPER
	Range: 7c0
	Report: ijarwe1a.aud
	Warhead@1Dam: SpreadDamage
		Versus:
			Light: 28
			Heavy: 28
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
	Warhead@2Eff: CreateEffect
		Explosions: piff
		-ValidTargets:
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion
		ImpactSounds: kaboom12.aud
		ValidTargets: Vehicle, Ship, Trees
	-Warhead@3EffWater:
	Warhead@4EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: splash9.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge
	Warhead@Concussion: GrantExternalConditionCA
		Range: 0c256
		Duration: 75
		Condition: concussion
		ValidTargets: Vehicle, Ship

SNIPER.vehicleElite:
	Inherits: SNIPER.vehicle
	Report: ijarwe2a.aud
	Warhead@Concussion: GrantExternalConditionCA
		InvalidTargets: DriverKillLow
	Warhead@DriverKill: ChangeOwnerToNeutral
		ValidTargets: DriverKillLow
		InvalidTargets: DriverKillImmune
		ValidRelationships: Enemy
		CargoEffect: Block
		Range: 0c511
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: ffffff
		ValidTargets: DriverKillLow
		InvalidTargets: DriverKillImmune

SNIPERBATF:
	Inherits: SNIPER
	Range: 7c0

SNIPERBATF.UPG:
	Inherits: SNIPER.vehicle
	Range: 6c0

SNIPER.ASSA:
	Inherits: SNIPER
	InvalidTargets: Structure
	ReloadDelay: 100
	Range: 8c512
	Report: iviratta.aud
	Warhead@1Dam: SpreadDamage
		DamageTypes: Prone50Percent, TriggerProne, PoisonDeath
	Warhead@2Dam: HealthPercentageDamage
		DamageTypes: PoisonDeath
	Warhead@Cloud: SpawnActor
		Actors: viruscloud
		Range: 5
		ImpactActors: false

SNIPERBATF.ASSA:
	Inherits: SNIPER.ASSA
	Range: 7c0

SniperIFVGun.UPG:
	Inherits: SNIPER.vehicle
	ReloadDelay: 105
	Warhead@1Dam: SpreadDamage
		Versus:
			Light: 40
			Heavy: 50

SniperIFVGun:
	Inherits: SniperIFVGun.UPG
	-Warhead@Concussion:

CommandoIFVGun:
	Inherits: SMG
	ReloadDelay: 10
	Burst: 3
	BurstDelays: 2
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Damage: 4700

SealIFVGun:
	Inherits: MP5
	ReloadDelay: 4
	-InvalidTargets:
	Projectile: InstantHit
		Blockable: true

ShadowOperativeGun:
	Inherits: ^HeavyMG
	Range: 6c0
	ReloadDelay: 25
	Burst: 2
	BurstDelays: 3
	Report: shad-fire1.aud, shad-fire2.aud, shad-fire3.aud
	Projectile: InstantHit
	ValidTargets: Infantry
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 2800
		Versus:
			None: 100
			Wood: 40
			Concrete: 20
			Light: 60
			Heavy: 20
	Warhead@2Eff: CreateEffect
		Explosions: piff

HeliGunAG:
	Inherits: ^HeavyMG
	Report: mgbtr1.aud, mgbtr2.aud, mgbtr3.aud
	ReloadDelay: 14
	Range: 5c512
	MinRange: 0c768
	Projectile: Bullet
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Damage: 5000
		Versus:
			None: 144
			Wood: 35
			Light: 54
			Heavy: 25
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath, AirToGround

HeliGunAA:
	Inherits: ^AACannon
	ReloadDelay: 14
	Burst: 1
	Report: mgbtr1.aud, mgbtr2.aud, mgbtr3.aud
	Range: 5c0
	Warhead@1Dam: SpreadDamage
		Damage: 2000
		Range: 0, 0c64, 0c256, 1c768
	Warhead@smallDamage: SpreadDamage
		Damage: 2000
		Spread: 341
	Warhead@2Eff: CreateEffect
		-ImpactSounds: flakhit1.aud, flakhit2.aud, flakhit3.aud
		-ImpactSoundChance: 100

WolverineGun:
	Inherits: M60mg
	Range: 4c768
	MinRange: 0c128
	ReloadDelay: 25
	BurstDelays: 3
	-Report:
	StartBurstReport: wolv-fire1.aud, wolv-fire2.aud
	Warhead@1Dam: SpreadDamage
		Spread: 256
		Damage: 750
		Versus:
			Wood: 45
			Light: 70
			Heavy: 12
	-Warhead@2Eff:

WolverineGunLine:
	Inherits: WolverineGun
	-StartBurstReport:
	-Projectile:
	Projectile: RailgunCA
		Blockable: true
		Invisible: true
		PassthroughToMaxRange: true
		PassthroughParallelToMuzzleOffset: true
		DamageActorsInLine: true
		BeamWidth: 256
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 750
		ValidRelationships: Enemy, Neutral
	Warhead@2Eff: CreateEffect
		Explosions: piff, empty
		ValidTargets: Infantry, Structure, Vehicle, Ship, Trees
		ValidRelationships: Enemy, Neutral
	-Warhead@3EffWater:

WolverineGunTracer:
	Inherits: WolverineGun
	-StartBurstReport:
	-Projectile:
	-Warhead@1Dam:
	-Warhead@3EffWater:
	Projectile: BulletCA
		Speed: 1350
		ContrailStartWidth: 40
		ContrailLength: 9
		ContrailStartColor: d6b4a044
		ContrailStartColorAlpha: 68
		Inaccuracy: 128
		PassthroughToMaxRange: true
		PassthroughParallelToMuzzleOffset: true
	Warhead@2Eff: CreateEffect
		Inaccuracy: 0
		Explosions: piff
		ValidTargets: Ground, Ship, Air, AirSmall, Trees

EnforcerShotgun:
	Range: 4c0
	Report: enfo-fire1.aud, enfo-fire2.aud
	ReloadDelay: 55
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 320
		Falloff: 100, 26, 10, 4, 0
		Damage: 7000
		Versus:
			None: 100
			Wood: 40
			Light: 80
			Heavy: 15
			Concrete: 15
			Brick: 30
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@2Eff: CreateEffect
		Explosions: piffs
		ValidTargets: Ground, Ship, Air, AirSmall, Trees

EnforcerShotgunLine:
	Inherits: EnforcerShotgun
	-Report:
	Projectile: RailgunCA
		Blockable: true
		Invisible: true
		PassthroughToMaxRange: true
		PassthroughParallelToMuzzleOffset: true
		DamageActorsInLine: true
		BeamWidth: 256
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 2000
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
		ValidRelationships: Enemy, Neutral
	Warhead@2Eff: CreateEffect
		Explosions: piff, empty
		ValidTargets: Infantry, Structure, Vehicle, Ship, Trees
		ValidRelationships: Enemy, Neutral

EnforcerIFVShotgun:
	Inherits: EnforcerShotgun
	Burst: 2
	BurstDelays: 5

EnforcerIFVShotgunLine:
	Inherits: EnforcerShotgunLine
	Burst: 2
	BurstDelays: 5

ConfessorGun:
	Inherits: M1Carbine
	Report: conf-fire1.aud, conf-fire2.aud
	Range: 6c0
	Warhead@1Dam: SpreadDamage
		Damage: 3000
		Versus:
			Light: 50
