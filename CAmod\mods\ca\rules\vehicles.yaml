V2RL:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 180
		Prerequisites: anyradar, ~vehicles.soviet, ~!v3.upgrade, ~techlevel.medium
		Description: actor-v2rl.description
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets
	Valued:
		Cost: 900
	Tooltip:
		Name: actor-v2rl.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 15000
	Armor:
		Type: Light
	Mobile:
		Speed: 60
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: SCUD
		ReloadingCondition: reloading
	AutoTarget:
		ScanRadius: 10
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	WithFacingSpriteBody:
		RequiresCondition: !reloading
		Name: loaded
	WithFacingSpriteBody@EMPTY:
		RequiresCondition: reloading
		Sequence: empty-idle
		Name: reloading
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	ReplacedInQueue:
		Actors: v3rl
	Upgradeable@V3:
		Type: v3.upgrade
		UpgradingCondition: upgrading
		Actor: v3rl
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Soviets/Vehicles

KATY:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 130
		IconPalette: chrometd
		Prerequisites: ~vehicles.soviet, ~!arty.doctrine, ~techlevel.low
		Description: Long-range rocket barrage artillery.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Buildings, Defenses
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets
	Valued:
		Cost: 750
	Tooltip:
		Name: Katyusha
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 13000
	Armor:
		Type: Light
	Mobile:
		Speed: 54
		TurnSpeed: 12
		Voice: Move
	Passenger:
		Voice: Move
	AttackMove:
		Voice: Attack
	Voiced:
		VoiceSet: GradVoice
	RevealsShroud:
		Range: 5c0
	Armament@PRIMARY:
		Weapon: KatyushaRockets
		LocalOffset: 268,-100,440
	Armament@SECONDARY:
		Weapon: KatyushaRocketsWide
		LocalOffset: 268,100,440
		FireDelay: 6
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
		Voice: Attack
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	ReplacedInQueue:
		Actors: grad
	Upgradeable@Grad:
		Type: arty.doctrine
		UpgradingCondition: upgrading
		Actor: grad
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Soviets/Vehicles

GRAD:
	Inherits: KATY
	Buildable:
		BuildPaletteOrder: 131
		Prerequisites: ~vehicles.soviet, ~arty.doctrine, ~techlevel.medium
		IconPalette: chrome
	Tooltip:
		Name: Grad
	Health:
		HP: 14000
	-Upgradeable@Grad:
	-WithDecoration@UpgradeOverlay:
	-AttackFrontal:
	AttackTurreted:
		TargetFrozenActors: True
		Voice: Attack
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
	Turreted:
		TurnSpeed: 10
		RealignDelay: 3
		Offset: -200, 0, 0
	WithSpriteTurret:
		Sequence: turret
		RequiresCondition: !rocketpods-upgrade
	WithSpriteTurret@UPG:
		Sequence: turret-upg
		RequiresCondition: rocketpods-upgrade
	Armament@PRIMARY:
		Weapon: GradRockets
		RequiresCondition: !rocketpods-upgrade
	Armament@SECONDARY:
		Weapon: GradRocketsWide
		RequiresCondition: !rocketpods-upgrade
	Armament@PRIMARYUPG:
		Weapon: GradRockets.UPG
		LocalOffset: 268,-100,440
		RequiresCondition: rocketpods-upgrade
	Armament@SECONDARYUPG:
		Weapon: GradRocketsWide.UPG
		LocalOffset: 268,100,440
		FireDelay: 6
		RequiresCondition: rocketpods-upgrade
	GrantConditionOnPrerequisite@RocketPods:
		Prerequisites: rocketpods.upgrade
		Condition: rocketpods-upgrade
	EncyclopediaExtras:
		AdditionalInfo: Requires Artillery Doctrine.

NUKC:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@ATOMICAMMO: ^AtomicAmmunition
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 243
		Prerequisites: stek, ~vehicles.soviet, ~arty.doctrine, ~nukc.upgrade, ~techlevel.high
		Description: Nuclear long-range artillery.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets
		Attributes: • Can crush concrete walls
	Valued:
		Cost: 2400
	Tooltip:
		Name: Nuke Cannon
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Health:
		HP: 60000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 6
		Speed: 46
		Locomotor: heavytracked
		Voice: Move
		ImmovableCondition: deployed
		RequireForceMoveCondition: !undeployed
		PauseOnCondition: being-captured || empdisable || being-warped || driver-dead || notmobile || deploying || undeploying
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: 280mmAtomic
		LocalOffset: 824,0,758
		MuzzleSequence: muzzle
		PauseOnCondition: has-atomic-ammo || atomic-reloading || !deployed || deploying
		RequiresCondition: deployed || undeployed
		ReloadingCondition: reloading
	Armament@ATOMICAMMO:
		Weapon: 280mmNeutron
		LocalOffset: 824,0,758
		MuzzleSequence: muzzle
		PauseOnCondition: !has-atomic-ammo || reloading || !deployed || deploying
		RequiresCondition: deployed || undeployed
		ReloadingCondition: atomic-reloading
	Armament@Deployer:
		Name: deployer
		Turret: deployer
		Weapon: NukeCannonDeployer
		LocalOffset: 824,0,758
		RequiresCondition: undeployed && !undeploying
	AttackTurreted:
		Armaments: primary, deployer
		Turrets: primary, deployer
		Voice: Attack
		RangeMargin: 0
		PauseOnCondition: empdisable || being-warped || blinded || deploying
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PersistentTargeting: false
		RequiresCondition: !undeploying
	GrantConditionOnDeployTurreted:
		DeployedCondition: deployed
		UndeployedCondition: undeployed
		UndeployOnMove: True
		Voice: Deploy
		DeploySounds: vnukde1a.aud
		UndeploySounds: vnukde2a.aud
		PauseOnCondition: being-warped
		SmartDeploy: True
		ValidFacings: 0, 256, 512, 768
	GrantTimedCondition@Deploying:
		Condition: deploying
		Duration: 25
		RequiresCondition: deployed
	GrantTimedCondition@Undeploying:
		Condition: undeploying
		Duration: 25
		RequiresCondition: undeployed
	WithEnabledAnimation@Deploying:
		RequiresCondition: deployed
		Sequence: make
		Body: deployed
	WithEnabledAnimation@Undeploying:
		RequiresCondition: undeployed
		Sequence: undeploy
		Body: body
	WithFacingSpriteBody:
		RequiresCondition: undeployed
	WithFacingSpriteBody@DeployedChassis:
		Name: deployed
		Sequence: deployed
		RequiresCondition: !undeployed
	WithFacingSpriteBody@Overlay:
		Name: overlay
		Sequence: overlay
	WaitsForTurretAlignmentOnUndeploy:
		AligningCondition: undeploying
	WithSpriteTurret:
		Sequence: turret
	Turreted:
		TurnSpeed: 6
		InitialFacing: 0
		RealignDelay: 3
		PauseOnCondition: !deployed
	Turreted@Deployer:
		Turret: deployer
	WithMuzzleOverlay:
	WithRestartableIdleOverlay@ATOMICAMMO:
		RequiresCondition: atomic-ammo && !has-atomic-ammo
	WithAmmoPipsDecoration@ATOMICAMMO:
		PipCount: 1
	AmmoPool@ATOMICAMMO:
		Ammo: 1
	ReloadAmmoPool@ATOMICAMMO:
		Count: 1
		RequiresCondition: atomic-ammo && !has-atomic-ammo
	ReloadAmmoPoolCA@ATOMICAMMODECAY:
		Count: -1
	FireWarheadsOnDeath:
		Weapon: UnitExplodeIraqTank
		LoadedChance: 100
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: NukcVoice
	SpawnActorOnDeath:
		Actor: NUKC.Husk
		RequiresCondition: !being-warped
	GrantConditionOnBotOwner@BotOwner:
		Condition: botowner
		Bots: brutal, vhard, hard, normal, easy, naval
	DeployOnAttack:
		RequiresCondition: undeployed && !moving && !deploying && !undeploying
		ArmamentNames: deployer
	AutoTarget:
		RequiresCondition: (botowner || deployed || attack-move || assault-move) && !undeploying && !deploying
	AttackMove:
		AttackMoveCondition: attack-move
	WithRangeCircle:
		Type: NukeCannon
		Range: 18c0
		Color: ffdd0050
		RequiresCondition: (!deployed || deploying) && !veiled && !gapveiled
	WithRangeCircle@Veiled:
		Type: NukeCannon
		Range: 14c410
		Color: ffdd0050
		RequiresCondition: (!deployed || deploying) && veiled && !gapveiled
	WithRangeCircle@GapVeiled:
		Type: NukeCannon
		Range: 10c819
		Color: ffdd0050
		RequiresCondition: (!deployed || deploying) && gapveiled
	WithRangeCircle@Deployed:
		Type: NukeCannon
		Range: 18c0
		Color: ff880090
		RequiresCondition: deployed && !deploying && !veiled && !gapveiled
	WithRangeCircle@DeployedVeiled:
		Type: NukeCannon
		Range: 14c410
		Color: ff880090
		RequiresCondition: deployed && !deploying && veiled && !gapveiled
	WithRangeCircle@DeployedGapVeiled:
		Type: NukeCannon
		Range: 10c819
		Color: ff880090
		RequiresCondition: deployed && !deploying && gapveiled
	GrantConditionOnMovement@Moving:
		Condition: moving
		ValidMovementTypes: Horizontal, Vertical
	RejectsOrders:
		Reject: AttackMove
		RequiresCondition: !undeployed
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		RenderPreviewActor: nukc.preview
		AdditionalInfo: Requires Artillery Doctrine.

NUKC.Preview:
	Inherits: ^PreviewDummy
	RenderSprites:
		Image: nukc
	WithFacingSpriteBody:
	WithSpriteTurret:
		Sequence: turret
	Turreted:

1TNK:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 120
		Prerequisites: ~vehicles.1tnk, ~techlevel.low
		Description: Fast tank, good for scouting and skirmishes.
	TooltipExtras:
		Strengths: • Strong vs Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
		Attributes: • Amphibious
	Valued:
		Cost: 650
	Tooltip:
		Name: Scout Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 27000
	Armor:
		Type: Heavy
	Mobile:
		Locomotor: Amphibious
		Speed: 113
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	Targetable@WATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater && (!parachute && !being-warped)
	WithFacingSpriteBody:
		RequiresCondition: !onwater
	WithFacingSpriteBody@WATER:
		Sequence: idle-water
		Name: body-water
		RequiresCondition: onwater
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 28
	Armament:
		Weapon: 25mm
		Recoil: 85
		RecoilRecovery: 25
		LocalOffset: 768,0,90
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded || parachute
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: 1TNK.Husk
		RequiresCondition: !onwater && !being-warped
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Encyclopedia:
		Category: Allies/Vehicles

2TNK:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlightlySlowedByCrushing
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 70
		Prerequisites: ~vehicles.allies, ~techlevel.low
		Description: Allied main battle tank.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 800
	Tooltip:
		Name: Medium Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 47000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 72
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 20
		Offset: 0,0,20
	QuantizeFacingsFromSequence:
		Sequence: idle
	WithFacingSpriteBody:
		Sequence: idle
	Armament:
		Weapon: 90mm
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 720,0,80
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: 2TNK.Husk
		RequiresCondition: !being-warped
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	Encyclopedia:
		Category: Allies/Vehicles

GTNK:
	Inherits: 2TNK
	Buildable:
		Queue: GrizzlyTank
		-Prerequisites:
		Description: Allied paradropped battle tank.
	Valued:
		Cost: 875
	Tooltip:
		Name: Grizzly Tank
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
	Mobile:
		Speed: 100
		Voice: Move
	Passenger:
		Voice: Move
	Health:
		HP: 41250
	Armament:
		Weapon: 105mm
		LocalOffset: 720,0,80
	Turreted:
		TurnSpeed: 20
		Offset: 80,0,0
	AttackTurreted:
		Voice: Attack
		PauseOnCondition: empdisable || being-warped || blinded || parachute
	AttackMove:
		Voice: Attack
	SpawnActorOnDeath:
		Actor: GTNK.Husk
	Voiced:
		VoiceSet: GrizzlyVoice
	EncyclopediaExtras:
		Subfaction: usa

GTNKR2:
	Inherits: GTNK
	RenderSprites:
		Image: gtnk
	ProducibleWithLevel:
		-Prerequisites:
		InitialLevels: 2
	-Encyclopedia:
	-EncyclopediaExtras:

GTNK.squad:
	AlwaysVisible:
	Interactable:
	ScriptTriggers:
	ProvidesPrerequisite@squadname:
	Tooltip:
		Name: Airdrop: Grizzly Tanks
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildAtProductionType: ParadropVehicle
		BuildPaletteOrder: 400
		Prerequisites: radaroraircraft, ~vehicles.usa, ~techlevel.medium
		Description: Prepare a pair of  Grizzly Tanks for airdrop.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 1750
	RenderSprites:
		Image: squad.airborne.tank
	ProduceActorPowerCA:
		Actors: powerproxy.airborne.tank
		Type: ParadropVehicle
		OneShot: true
		AutoFire: true
		AllowMultiple: true
	Armor:
		Type: Heavy
	ProductionTimeMultiplier:
		Multiplier: 75
		Prerequisites: airborne.upgrade

2TNK.Chrono:
	Inherits: 2TNK
	Inherits@TEMPINC: ^TemporalReinforcement
	RenderSprites:
		Image: 2tnk
		FactionImages:
			england: rtnk
			germany: tnkd
			russia: 3tnk
			ukraine: 3tnk
			iraq: 3tnk
			yuri: 3tnk
			blackh: mtnk.nod.chrono
			marked: mtnk.nod.chrono
			legion: mtnk.nod.chrono
			shadow: mtnk.nod.chrono
			eagle: mtnk.gdi.chrono
			zocom: mtnk.gdi.chrono
			talon: mtnk.gdi.chrono
			arc: mtnk.gdi.chrono
			reaper: devo
			traveler: devo
			harbinger: devo
	-Buildable:
	FireWarheadsOnDeath:
		RequiresCondition: !warpout
	GrantConditionOnFaction@France:
		Factions: france
		Condition: france
	GrantConditionOnFaction@England:
		Factions: england
		Condition: england
	GrantConditionOnFaction@Germany:
		Factions: germany
		Condition: germany
	GrantConditionOnFaction@USA:
		Factions: usa
		Condition: usa
	GrantConditionOnFaction@GDI:
		Factions: gdi, talon, zocom, eagle, arc
		Condition: gdi
	GrantConditionOnFaction@NOD:
		Factions: nod, blackh, marked, legion, shadow
		Condition: nod
	GrantConditionOnFaction@SOVIET:
		Factions: soviet, russia, ukraine, iraq, yuri
		Condition: soviet
	GrantConditionOnFaction@SCRIN:
		Factions: scrin, reaper, traveler, harbinger, collector
		Condition: scrin
	SpeedMultiplier@SCRIN:
		Modifier: 140
		RequiresCondition: scrin
	DamageMultiplier@SCRIN:
		Modifier: 120
		RequiresCondition: scrin
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead && scrin
	Armament:
		RequiresCondition: france || usa
	Armament@Germany:
		Weapon: 183mm
		Recoil: 85
		RecoilRecovery: 25
		LocalOffset: 868,0,0
		MuzzleSequence: muzzle
		RequiresCondition: germany
	Armament@England:
		Weapon: 120mmHEAT
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 720,0,80
		MuzzleSequence: muzzle
		RequiresCondition: england
	Armament@TD:
		Weapon: 120mm
		Recoil: 128
		RecoilRecovery: 26
		LocalOffset: 768,0,90
		MuzzleSequence: muzzle
		RequiresCondition: gdi || nod
	Armament@SOVIET:
		Weapon: 125mm
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 768,85,90, 768,-85,90
		MuzzleSequence: muzzle
		RequiresCondition: soviet
	Armament@SCRIN:
		Weapon: DevourerLaser
		LocalOffset: 850,0,208
		Turret: secondary
		RequiresCondition: scrin
	Tooltip:
		RequiresCondition: france || usa
	Tooltip@Germany:
		Name: Tank Destroyer
		GenericName: Tank
		RequiresCondition: germany
	Tooltip@Mirage:
		Name: Mirage Tank
		GenericName: Tank
		RequiresCondition: england
	Tooltip@TD:
		Name: Battle Tank
		GenericName: Tank
		RequiresCondition: nod || gdi
	Tooltip@SOVIET:
		Name: Heavy Tank
		GenericName: Tank
		RequiresCondition: soviet
	Tooltip@SCRIN:
		Name: Devourer
		GenericName: Tank
		RequiresCondition: scrin
	Turreted:
		RequiresCondition: !scrin
	Turreted@SCRIN:
		Turret: secondary
		TurnSpeed: 16
		Offset: 0,0,0
		RequiresCondition: scrin
	Turreted@TD:
		Turret: tertiary
		TurnSpeed: 20
		Offset: 0,0,30
		RequiresCondition: gdi || nod
	AttackTurreted:
		Turrets: primary, secondary, tertiary
		TargetFrozenActors: True
		Armaments: primary
	Mirage:
		RequiresCondition: england && !empdisable && !being-warped && !driver-dead
		RevealOn: Attack, Move
		MirageCondition: tree
		DefaultTargetTypes: v08, v09, v10, v11, v12, v13, v14, v15, v16, v17, v18
		InitialDelay: 99
		RevealDelay: 100
	WithMirageSpriteBody:
		RequiresCondition: tree && england
		Name: tree
		IsPlayerPalette: false
	WithFacingSpriteBody:
		RequiresCondition: !tree
	WithSpriteTurret:
		RequiresCondition: !scrin && !(gdi || nod) && !tree
	WithSpriteTurret@TD:
		RequiresCondition: gdi || nod
		Turret: tertiary
		IsPlayerPalette: true
		Palette: playertd
	WithSpriteTurret@SCRIN:
		Turret: secondary
		RequiresCondition: scrin
	WithMuzzleOverlay:
		RequiresCondition: !scrin
	Cloak@NORMAL:
		InitialDelay: 99
		CloakDelay: 100
		CloakSound: vmirat2a.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Move, Damage, Heal
		RequiresCondition: england && !cloak-force-disabled && !being-warped && !empdisable && !driver-dead
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	SpawnActorOnDeath:
		RequiresCondition: !(being-warped || warpout) && warpin && (france || usa)
	SpawnActorOnDeath@England:
		Actor: RTNK.Husk
		RequiresCondition: warpin && england && !(being-warped || warpout)
	SpawnActorOnDeath@Germany:
		Actor: TNKD.Husk
		RequiresCondition: warpin && germany && !(being-warped || warpout)
	SpawnActorOnDeath@TD:
		Actor: MTNK.Husk
		RequiresCondition: warpin && (nod || gdi) && !(being-warped || warpout)
	SpawnActorOnDeath@SOVIET:
		Actor: 3TNK.Husk
		RequiresCondition: warpin && soviet && !(being-warped || warpout)
	AutoTargetPriority@TankDestroyer:
		RequiresCondition: germany
		ValidTargets: Vehicle
		InvalidTargets: NoAutoTarget
		Priority: 10
	-MapEditorData:
	-Encyclopedia:

2TNK.TEMP:
	Inherits: 2TNK
	Inherits@TEMPINC: ^TemporalReinforcement
	RenderSprites:
		Image: 2tnk
	FireWarheadsOnDeath:
		RequiresCondition: !warpout
	-ActorLostNotification:
	-SpawnActorOnDeath:
	-Buildable:
	-ChronoshiftableCA:
	-HealthCapDamageMultiplier@CHRONO:
	-MapEditorData:
	-Encyclopedia:

3TNK:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlightlySlowedByCrushing
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@ATOMICAMMO: ^AtomicAmmunition
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 80
		Prerequisites: ~!atomicengines.upgrade, ~!lasher.upgrade, ~!armor.doctrine, ~vehicles.soviet, ~techlevel.low
		Description: Soviet main battle tank with dual cannons.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 1150
	Tooltip:
		Name: Heavy Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 62000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 60
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 20
		Offset: 0,0,60
	Armament:
		Weapon: 125mm
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 768,85,90, 768,-85,90
		MuzzleSequence: muzzle
		PauseOnCondition: has-atomic-ammo || atomic-reloading
		ReloadingCondition: reloading
	Armament@ATOMICAMMO:
		Weapon: 125mmAtomic
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 768,85,90, 768,-85,90
		MuzzleSequence: muzzle
		PauseOnCondition: !has-atomic-ammo || reloading
		ReloadingCondition: atomic-reloading
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded || parachute
	WithMuzzleOverlay:
	WithSpriteTurret:
	WithFacingSpriteBody:
	SpawnActorOnDeath:
		Actor: 3TNK.Husk
		RequiresCondition: !being-warped
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	ReplacedInQueue:
		Actors: 3tnk.atomic, 3tnk.yuri, 3tnk.rhino
	Upgradeable@ATOMIC:
		Type: atomicengines.upgrade
		UpgradingCondition: upgrading
		Actor: 3tnk.atomic
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@LASHER:
		Type: lasher.upgrade
		UpgradingCondition: upgrading
		Actor: 3tnk.yuri
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@RHINO:
		Type: armor.doctrine
		UpgradingCondition: upgrading
		Actor: 3tnk.rhino
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Soviets/Vehicles

3TNK.ATOMIC:
	Inherits: 3TNK
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	RenderSprites:
		Image: 3tnki
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 81
		Prerequisites: ~atomicengines.upgrade, ~!lasher.upgrade, ~!armor.doctrine, ~vehicles.soviet, ~techlevel.low
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
		Attributes: • Explodes and leaves radiation on death
	Valued:
		Cost: 1150
	Tooltip:
		Name: Atomic Heavy Tank
		GenericName: Tank
	Turreted:
		Offset: 0,0,20
	Selectable:
		Class: 3tnk
	SpeedMultiplier:
		Modifier: 125
	-SpawnActorOnDeath:
	FireWarheadsOnDeath@ATOMICENGINES:
		Weapon: UnitExplodeIraqTank
		EmptyWeapon: UnitExplodeIraqTank
		RequiresCondition: !being-warped
	ReplacedInQueue:
		Actors: 3tnk.atomicyuri, 3tnk.rhino.atomic
	Upgradeable@LASHER:
		Actor: 3tnk.atomicyuri
	Upgradeable@RHINO:
		Actor: 3tnk.rhino.atomic
	-Upgradeable@ATOMIC:

3TNK.YURI:
	Inherits: 3TNK
	RenderSprites:
		Image: 3tnky
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 84
		Prerequisites: ~lasher.upgrade, ~!atomicengines.upgrade, ~!armor.doctrine, ~vehicles.soviet, ~techlevel.low
		Description: Heavy tank with infantry crushing improvements.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Defenses\n• Cannot attack Aircraft
		Attributes: • Can crush concrete walls
	Tooltip:
		Name: Lasher Tank
		GenericName: Tank
	Health:
		HP: 57000
	Selectable:
		Class: 3tnk
	Armament:
		Weapon: 125mmLasher
		LocalOffset: 768,0,90
	Armament@ATOMICAMMO:
		Weapon: 125mmLasherAtomic
		LocalOffset: 768,0,90
	SpeedMultiplier:
		Modifier: 125
	GrantConditionOnMovement@LASHER:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: lasher-move
	PeriodicExplosion@LASHER:
		RequiresCondition: lasher-move
		LocalOffset: 896, 0, 0
		Weapon: Lasher
	AttackTurreted:
		Voice: Attack
	Mobile:
		Voice: Move
		Locomotor: heavytracked
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: LasherTankVoice
	SpawnActorOnDeath:
		Actor: 3TNK.YURI.Husk
	WithRestartableIdleOverlay@ATOMICAMMO:
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	AmmoPool@ATOMICAMMO:
		Ammo: 6
	ReloadAmmoPool@ATOMICAMMO:
		Count: 6
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	-ExternalCondition@CRUSHATTEMPTSLOW:
	-ExternalCondition@CRUSHSLOW:
	-SpeedMultiplier@CRUSHATTEMPTSLOW:
	-SpeedMultiplier@CRUSHSLOW:
	ReplacedInQueue:
		Actors: 3tnk.atomicyuri, 3tnk.rhino.yuri
	Upgradeable@ATOMIC:
		Actor: 3tnk.atomicyuri
	Upgradeable@RHINO:
		Actor: 3tnk.rhino.yuri
	-Upgradeable@LASHER:
	EncyclopediaExtras:
		Subfaction: yuri

3TNK.ATOMICYURI:
	Inherits: 3TNK.ATOMIC
	RenderSprites:
		Image: 3tnkay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 85
		Prerequisites: ~lasher.upgrade, ~atomicengines.upgrade, ~!armor.doctrine, ~vehicles.soviet, ~techlevel.low
		Description: Heavy tank with infantry crushing improvements.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Defenses\n• Cannot attack Aircraft
		Attributes: • Can crush concrete walls\n• Explodes and leaves radiation on death
	Tooltip:
		Name: Atomic Lasher Tank
		GenericName: Tank
	Health:
		HP: 57000
	Selectable:
		Class: 3tnk
	Armament:
		Weapon: 125mmLasher
		LocalOffset: 768,0,90
	Armament@ATOMICAMMO:
		Weapon: 125mmLasherAtomic
		LocalOffset: 768,0,90
	SpeedMultiplier:
		Modifier: 135
	GrantConditionOnMovement@LASHER:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: lasher-move
	PeriodicExplosion@LASHER:
		RequiresCondition: lasher-move
		LocalOffset: 896, 0, 0
		Weapon: Lasher
	AttackTurreted:
		Voice: Attack
	Mobile:
		Voice: Move
		Locomotor: heavytracked
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: LasherTankVoice
	WithFacingSpriteBody:
		Sequence: idle2
	WithRestartableIdleOverlay@ATOMICAMMO:
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	AmmoPool@ATOMICAMMO:
		Ammo: 6
	ReloadAmmoPool@ATOMICAMMO:
		Count: 6
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	-ExternalCondition@CRUSHATTEMPTSLOW:
	-ExternalCondition@CRUSHSLOW:
	-SpeedMultiplier@CRUSHATTEMPTSLOW:
	-SpeedMultiplier@CRUSHSLOW:
	ReplacedInQueue:
		Actors: 3tnk.rhino.atomicyuri
	Upgradeable@RHINO:
		Actor: 3tnk.rhino.atomicyuri
	-Upgradeable@LASHER:
	EncyclopediaExtras:
		Subfaction: yuri

3TNK.RHINO:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlightlySlowedByCrushing
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@ATOMICAMMO: ^AtomicAmmunition
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 82
		Prerequisites: ~vehicles.soviet, ~!atomicengines.upgrade, ~!lasher.upgrade, ~armor.doctrine, ~techlevel.low
		Description: Soviet main battle tank with a single powerful cannon.
	RenderSprites:
		Image: rhin
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 1150
	Tooltip:
		Name: Rhino Heavy Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 68000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 60
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 20
		Offset: 0,0,0
	Armament:
		Weapon: 125mmRhino
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 768,0,90
		MuzzleSequence: muzzle
		PauseOnCondition: has-atomic-ammo || atomic-reloading
		ReloadingCondition: reloading
	Armament@ATOMICAMMO:
		Weapon: 125mmRhinoAtomic
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 768,0,90
		MuzzleSequence: muzzle
		PauseOnCondition: !has-atomic-ammo || reloading
		ReloadingCondition: atomic-reloading
	AttackTurreted:
		Voice: Attack
		PauseOnCondition: empdisable || being-warped || blinded || parachute
	AttackMove:
		Voice: Attack
	Voiced:
		VoiceSet: RhinoTankVoice
	WithMuzzleOverlay:
	WithSpriteTurret:
		RequiresCondition: !reactive-upgrade
	WithSpriteTurret@ReactiveArmor:
		Sequence: turret-upg
		RequiresCondition: reactive-upgrade
	WithFacingSpriteBody:
	SpawnActorOnDeath:
		Actor: 3TNK.RHIN.Husk
		RequiresCondition: !being-warped
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
		Class: 3tnk
	GrantConditionOnPrerequisite@ReactiveArmor:
		Prerequisites: reactive.upgrade
		Condition: reactive-upgrade
	DamageMultiplier@ReactiveArmor:
		Modifier: 80
		RequiresCondition: reactive-upgrade
	ReplacedInQueue:
		Actors: 3tnk.rhino.atomic, 3tnk.rhino.yuri
	Upgradeable@ATOMIC:
		Type: atomicengines.upgrade
		UpgradingCondition: upgrading
		Actor: 3tnk.rhino.atomic
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@LASHER:
		Type: lasher.upgrade
		UpgradingCondition: upgrading
		Actor: 3tnk.rhino.yuri
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Armor Doctrine.

3TNK.RHINO.ATOMIC:
	Inherits: 3TNK.RHINO
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	RenderSprites:
		Image: rhini
	Buildable:
		BuildPaletteOrder: 83
		Prerequisites: ~atomicengines.upgrade, ~!lasher.upgrade, ~armor.doctrine, ~techlevel.low
	TooltipExtras:
		Attributes: • Explodes and leaves radiation on death
	Tooltip:
		Name: Atomic Rhino Heavy Tank
	Turreted:
		Offset: 0,0,20
	Selectable:
		Class: 3tnk
	SpeedMultiplier:
		Modifier: 125
	-SpawnActorOnDeath:
	FireWarheadsOnDeath@ATOMICENGINES:
		Weapon: UnitExplodeIraqTank
		EmptyWeapon: UnitExplodeIraqTank
		RequiresCondition: !being-warped
	ReplacedInQueue:
		Actors: 3tnk.rhino.atomicyuri
	Upgradeable@LASHER:
		Actor: 3tnk.rhino.atomicyuri
	-Upgradeable@ATOMIC:

3TNK.RHINO.YURI:
	Inherits: 3TNK.RHINO
	RenderSprites:
		Image: rhiny
	Buildable:
		BuildPaletteOrder: 86
		Prerequisites: ~!atomicengines.upgrade, ~lasher.upgrade, ~armor.doctrine, ~techlevel.low
		Description: Soviet main battle tank with infantry crushing improvements.
	Tooltip:
		Name: Thrasher Tank
	TooltipExtras:
		Attributes: • Can crush concrete walls
	Health:
		HP: 63000
	Mobile:
		Locomotor: heavytracked
	FirepowerMultiplier@Lasher:
		Modifier: 95
	GrantConditionOnMovement@LASHER:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: lasher-move
	PeriodicExplosion@LASHER:
		RequiresCondition: lasher-move
		LocalOffset: 896, 0, 0
		Weapon: Lasher
	SpeedMultiplier:
		Modifier: 125
	ReplacedInQueue:
		Actors: 3tnk.rhino.atomicyuri
	Upgradeable@ATOMIC:
		Actor: 3tnk.rhino.atomicyuri
	-ExternalCondition@CRUSHATTEMPTSLOW:
	-ExternalCondition@CRUSHSLOW:
	-SpeedMultiplier@CRUSHATTEMPTSLOW:
	-SpeedMultiplier@CRUSHSLOW:
	-Upgradeable@LASHER:
	EncyclopediaExtras:
		Subfaction: yuri

3TNK.RHINO.ATOMICYURI:
	Inherits: 3TNK.RHINO.ATOMIC
	RenderSprites:
		Image: rhinay
	Buildable:
		BuildPaletteOrder: 87
		Prerequisites: ~atomicengines.upgrade, ~lasher.upgrade, ~armor.doctrine, ~techlevel.low
		Description: Soviet main battle tank with infantry crushing improvements.
	Tooltip:
		Name: Atomic Thrasher Tank
	TooltipExtras:
		Attributes: • Can crush concrete walls
	Health:
		HP: 65000
	Mobile:
		Locomotor: heavytracked
	FirepowerMultiplier@Lasher:
		Modifier: 95
	GrantConditionOnMovement@LASHER:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: lasher-move
	PeriodicExplosion@LASHER:
		RequiresCondition: lasher-move
		LocalOffset: 896, 0, 0
		Weapon: Lasher
	SpeedMultiplier:
		Modifier: 135
	-ExternalCondition@CRUSHATTEMPTSLOW:
	-ExternalCondition@CRUSHSLOW:
	-SpeedMultiplier@CRUSHATTEMPTSLOW:
	-SpeedMultiplier@CRUSHSLOW:
	-ReplacedInQueue:
	-Upgradeable@LASHER:
	-WithDecoration@UpgradeOverlay:
	EncyclopediaExtras:
		Subfaction: yuri

4TNK:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeGround
	Inherits@BERSERK: ^Berserk
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@ATOMICAMMO: ^AtomicAmmunition
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 231
		Prerequisites: stek, ~vehicles.soviet, ~!erad.upgrade, ~!atomicengines.upgrade, ~!ovld.upgrade, ~!apoc.upgrade, ~techlevel.high
		Description: Big and slow tank with anti-air capability.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Attributes: • Can crush concrete walls\n• Self repairs to 50% out of combat
	Valued:
		Cost: 1700
	Tooltip:
		Name: Mammoth Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 90000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 44
		Locomotor: heavytracked
		Voice: Move
	Passenger:
		Weight: 2
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 12
	Armament@PRIMARY:
		Weapon: 130mm
		Recoil: 171
		RecoilRecovery: 30
		LocalOffset: 900,180,340, 900,-180,340
		MuzzleSequence: muzzle
		PauseOnCondition: has-atomic-ammo || atomic-reloading
		ReloadingCondition: reloading
	Armament@ATOMICAMMO:
		Weapon: 130mmAtomic
		Recoil: 171
		RecoilRecovery: 30
		LocalOffset: 900,180,340, 900,-180,340
		MuzzleSequence: muzzle
		PauseOnCondition: !has-atomic-ammo || reloading
		ReloadingCondition: atomic-reloading
	Armament@SECONDARY:
		Name: secondary
		Weapon: MammothTusk
		LocalOffset: -85,384,340, -85,-384,340
		LocalYaw: -100,100
		Recoil: 43
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	Voiced:
		VoiceSet: MammothRUVoice
	WithMuzzleOverlay:
	WithFacingSpriteBody:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: 4TNK.Husk
		RequiresCondition: !being-warped
	ChangesHealth:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 50
		DamageCooldown: 150
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Carryable:
		LocalOffset: 0,0,500
	-Crushable:
	WithIdleOverlay@MINDCONTROL:
		Offset: 0,0,512
	WithRestartableIdleOverlay@ATOMICAMMO:
		RequiresCondition: atomic-ammo && has-atomic-ammo < 10
	WithAmmoPipsDecoration@ATOMICAMMO:
		PipCount: 5
	AmmoPool@ATOMICAMMO:
		Ammo: 10
	ReloadAmmoPool@ATOMICAMMO:
		Count: 10
		RequiresCondition: atomic-ammo && has-atomic-ammo < 10
	ReplacedInQueue:
		Actors: 4tnk.atomic, 4tnk.erad, ovld, apoc
	Upgradeable@ATOMIC:
		Type: atomicengines.upgrade
		UpgradingCondition: upgrading
		Actor: 4tnk.atomic
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@ERAD:
		Type: erad.upgrade
		UpgradingCondition: upgrading
		Actor: 4tnk.erad
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@OVLD:
		Type: ovld.upgrade
		UpgradingCondition: upgrading
		Actor: ovld
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@APOC:
		Type: apoc.upgrade
		UpgradingCondition: upgrading
		Actor: apoc
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Soviets/Vehicles

4TNK.ATOMIC:
	Inherits: 4TNK
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	RenderSprites:
		Image: 4tnki
	Buildable:
		BuildPaletteOrder: 232
		Prerequisites: stek, ~vehicles.soviet, ~atomicengines.upgrade, ~!erad.upgrade, ~!ovld.upgrade, ~!apoc.upgrade, ~techlevel.high
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Attributes: • Can crush concrete walls\n• Self repairs to 50% out of combat\n• Explodes and leaves radiation on death
	Tooltip:
		Name: Atomic Mammoth Tank
	-SpawnActorOnDeath:
	FireWarheadsOnDeath@ATOMICUPGRADE:
		Weapon: UnitExplodeIraqTank
		EmptyWeapon: UnitExplodeIraqTank
		RequiresCondition: !being-warped
	Selectable:
		Class: 4tnk
	SpeedMultiplier:
		Modifier: 125
	ReplacedInQueue:
		Actors: 4tnk.erad.atomic, ovld.atomic, apoc.atomic
	Upgradeable@ERAD:
		Actor: 4tnk.erad.atomic
	Upgradeable@OVLD:
		Actor: ovld.atomic
	Upgradeable@APOC:
		Actor: apoc.atomic
	-Upgradeable@ATOMIC:

4TNK.ERAD:
	Inherits: 4TNK
	RenderSprites:
		Image: 4tnkerad
	Tooltip:
		Name: Eradicator
	TooltipExtras:
		Weaknesses: • Cannot attack Aircraft
	Buildable:
		Prerequisites: stek, ~vehicles.soviet, ~!atomicengines.upgrade, ~erad.upgrade, ~!ovld.upgrade, ~!apoc.upgrade, ~techlevel.high
		Description: Big and slow tank armed with a radiation cannon.
		BuildPaletteOrder: 233
	Armament@PRIMARY:
		Weapon: RadBeamWeaponHeavy
		MuzzlePalette: caneon
		LocalOffset: 400,0,340
		-PauseOnCondition:
		-ReloadingCondition:
	-Armament@ATOMICAMMO:
	-Armament@SECONDARY:
	SpawnActorOnDeath:
		Actor: 4TNK.ERAD.Husk
		RequiresCondition: !being-warped
	-ExternalCondition@ATOMICAMMO:
	-WithRestartableIdleOverlay@ATOMICAMMO:
	-FirepowerMultiplier@ATOMICAMMO:
	-Targetable@ATOMICAMMO:
	-AmmoPool@ATOMICAMMO:
	-WithAmmoPipsDecoration@ATOMICAMMO:
	-ReloadAmmoPool@ATOMICAMMO:
	-ReloadAmmoPoolCA@ATOMICAMMODECAY:
	-DamagedByTintedCells@RADSTRONG:
	-DamagedByTintedCells@RADMED:
	ReplacedInQueue:
		Actors: 4tnk.erad.atomic, apoc.erad, ovld.erad
	Upgradeable@ATOMIC:
		Actor: 4tnk.erad.atomic
	Upgradeable@APOC:
		Actor: apoc.erad
	Upgradeable@OVLD:
		Actor: ovld.erad
	-Upgradeable@ERAD:
	-AutoTargetPriority@DeprioritizedAir:
	Voiced:
		VoiceSet: EradVoice

4TNK.ERAD.ATOMIC:
	Inherits: 4TNK.ATOMIC
	RenderSprites:
		Image: 4tnkeradi
	Tooltip:
		Name: Atomic Eradicator
	TooltipExtras:
		Weaknesses: • Cannot attack Aircraft
	Buildable:
		Prerequisites: stek, ~vehicles.soviet, ~atomicengines.upgrade, ~erad.upgrade, ~!ovld.upgrade, ~!apoc.upgrade, ~techlevel.high
		Description: Big and slow tank armed with a radiation cannon.
		BuildPaletteOrder: 234
	Selectable:
		Class: 4tnk.erad
	Armament@PRIMARY:
		Weapon: RadBeamWeaponHeavy
		MuzzlePalette: caneon
		LocalOffset: 400,0,340
		-PauseOnCondition:
		-ReloadingCondition:
	-ExternalCondition@ATOMICAMMO:
	-WithRestartableIdleOverlay@ATOMICAMMO:
	-FirepowerMultiplier@ATOMICAMMO:
	-Targetable@ATOMICAMMO:
	-AmmoPool@ATOMICAMMO:
	-WithAmmoPipsDecoration@ATOMICAMMO:
	-ReloadAmmoPool@ATOMICAMMO:
	-ReloadAmmoPoolCA@ATOMICAMMODECAY:
	-Armament@ATOMICAMMO:
	-Armament@SECONDARY:
	Voiced:
		VoiceSet: EradVoice
	ReplacedInQueue:
		Actors: apoc.erad.atomic, ovld.erad.atomic
	Upgradeable@APOC:
		Actor: apoc.erad.atomic
	Upgradeable@OVLD:
		Actor: ovld.erad.atomic
	-Upgradeable@ERAD:

ARTY:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 122
		Prerequisites: ~vehicles.allies, ~techlevel.low
		Description: Long-range artillery.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets
	Valued:
		Cost: 650
	Tooltip:
		Name: Artillery
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 10000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 12
		Speed: 46
		Locomotor: lighttracked
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: 155mm
		LocalOffset: 624,0,208
		MuzzleSequence: muzzle
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	WithMuzzleOverlay:
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
		LoadedChance: 75
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Encyclopedia:
		Category: Allies/Vehicles

ARTY.nod:
	Inherits: ARTY
	Inherits@TDPAL: ^TDPalette
	WithDamageOverlay:
		Image: smoke_mtd
	RenderSprites:
		Image: artynod
	Buildable:
		BuildPaletteOrder: 151
		IconPalette: chrometd
		Prerequisites: ~vehicles.nod, ~!howi.upgrade, ~techlevel.low
	Armament:
		Weapon: 155mmTD
	Selectable:
		Class: arty
	ReplacedInQueue:
		Actors: howi
	Upgradeable@HOWI:
		Type: howi.upgrade
		Actor: howi
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Nod/Vehicles

HOWI:
	Inherits: ARTY.nod
	RenderSprites:
		Image: howi
	Tooltip:
		Name: Howitzer
	Buildable:
		BuildPaletteOrder: 152
		Prerequisites: ~vehicles.nod, ~howi.upgrade, ~techlevel.medium
	-AttackFrontal:
	AttackTurreted:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
	Turreted:
		TurnSpeed: 18
	Armament:
		Weapon: 155mmTDM
	Mobile:
		Speed: 64
		TurnSpeed: 18
	WithSpriteTurret:
	-ReplacedInQueue:
	-Upgradeable@HOWI:
	Selectable:
		-Class:
	GrantConditionOnPrerequisite@ZealBonus:
		Condition: zeal-covenant
		Prerequisites: zeal.covenant
	SpeedMultiplier@ZealBonus:
		Modifier: 115
		RequiresCondition: zeal-covenant

ARTY.Chrono:
	Inherits: ARTY
	Inherits@TEMPINC: ^TemporalReinforcement
	RenderSprites:
		Image: arty
		FactionImages:
			russia: katy
			ukraine: katy
			iraq: katy
			yuri: katy
			reaper: gunw
			traveler: gunw
			harbinger: gunw
	GrantConditionOnFaction@SOVIET:
		Factions: soviet, russia, ukraine, iraq, yuri
		Condition: soviet
	GrantConditionOnFaction@SCRIN:
		Factions: scrin, reaper, traveler, harbinger, collector
		Condition: scrin
	GrantConditionOnFaction@TD:
		Factions: gdi, talon, zocom, eagle, arc, nod, blackh, marked, legion, shadow
		Condition: td
	-Buildable:
	FireWarheadsOnDeath:
		RequiresCondition: !warpout
	Armament:
		RequiresCondition: !scrin && !soviet && !td
	Armament@TD:
		Weapon: 155mmTD
		LocalOffset: 624,0,208
		MuzzleSequence: muzzle
		RequiresCondition: td
	Armament@SOVIET:
		Weapon: KatyushaRockets
		LocalOffset: 268,0,440
		RequiresCondition: soviet
	Armament@PRIMARY:
		Weapon: GunWalkerZap
		LocalOffset: 750,92,25, 750,-92,25
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		RequiresCondition: scrin
	Armament@SECONDARY:
		Name: secondary
		Weapon: GunWalkerZapAA
		LocalOffset: 750,92,25, 750,-92,25
		MuzzleSequence: muzzle
		MuzzlePalette: scrin
		RequiresCondition: scrin
	Tooltip:
		RequiresCondition: !scrin && !soviet
	Tooltip@KATY:
		Name: Katyusha
		RequiresCondition: soviet
	Tooltip@SCRIN:
		Name: Gun Walker
		RequiresCondition: scrin
	SpeedMultiplier@SCRIN:
		RequiresCondition: scrin
		Modifier: 200
	WithMoveAnimation:
		ValidMovementTypes: Horizontal, Vertical, Turn
		RequiresCondition: scrin
	-MapEditorData:
	-Encyclopedia:

ARTY.TEMP:
	Inherits: ARTY
	Inherits@TEMPINC: ^TemporalReinforcement
	RenderSprites:
		Image: arty
	FireWarheadsOnDeath:
		RequiresCondition: !warpout
	-ActorLostNotification:
	-Buildable:
	-ChronoshiftableCA:
	-HealthCapDamageMultiplier@CHRONO:
	-MapEditorData:
	-Encyclopedia:

ISU:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@ATOMICAMMO: ^AtomicAmmunition
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 183
		Prerequisites: anyradar, ~vehicles.ukraine, ~techlevel.medium
		Description: Powerful medium-range artillery.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets
		Attributes: • Concussion shells slow enemy movement and rate of fire\n• Can crush concrete walls
	Valued:
		Cost: 1500
	Tooltip:
		Name: Siege Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 60000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 8
		Speed: 46
		Locomotor: heavytracked
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: 380mm
		LocalOffset: 824,0,208
		MuzzleSequence: muzzle
		PauseOnCondition: has-atomic-ammo || atomic-reloading
		ReloadingCondition: reloading
	Armament@ATOMICAMMO:
		Weapon: 380mmAtomic
		LocalOffset: 824,0,208
		MuzzleSequence: muzzle
		PauseOnCondition: !has-atomic-ammo || reloading
		ReloadingCondition: atomic-reloading
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
		FacingTolerance: 0
	WithMuzzleOverlay:
	WithRestartableIdleOverlay@ATOMICAMMO:
		RequiresCondition: atomic-ammo && has-atomic-ammo < 3
	WithAmmoPipsDecoration@ATOMICAMMO:
		PipCount: 3
	AmmoPool@ATOMICAMMO:
		Ammo: 3
	ReloadAmmoPool@ATOMICAMMO:
		Count: 3
		RequiresCondition: atomic-ammo && has-atomic-ammo < 3
	ReloadAmmoPoolCA@ATOMICAMMODECAY:
		Count: -3
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
		LoadedChance: 75
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: SiegeTankVoice
	SpawnActorOnDeath:
		Actor: ISU.Husk
		RequiresCondition: !being-warped
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		Subfaction: ukraine

SPEC:
	Inherits: ^VehicleTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 271
		IconPalette: chrometd
		Prerequisites: tmpl, ~vehicles.shadow, ~techlevel.high
		Description: Long-range stealth artillery.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft\n• Has difficulty hitting moving targets
	Valued:
		Cost: 1350
	Tooltip:
		Name: Spectre
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 11000
	Armor:
		Type: Light
	Mobile:
		Speed: 92
		Locomotor: lighttracked
		Voice: Move
	AttackMove:
		Voice: Move
	Guard:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: 155mmSpec
		LocalOffset: 624,0,408
	AttackFrontalCharged:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 32
		ChargeLevel: 25
		DischargeRate: 5
		ShotsPerCharge: 3
		ShowSelectionBar: true
		SelectionBarColor: 666666
		Voice: Attack
	WithMuzzleOverlay:
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
		LoadedChance: 75
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Cloak@NORMAL:
		InitialDelay: 89
		CloakDelay: 90
		CloakSound: trans1.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: !cloak-force-disabled && !being-warped && !empdisable && !driver-dead
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	Voiced:
		VoiceSet: SpectreVoice
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		Subfaction: shadow

SAPC:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 272
		Prerequisites: tmpl, ~vehicles.nod, ~techlevel.high
		Description: Stealth infantry transport.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 1100
	Tooltip:
		Name: Stealth APC
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 20000
	Armor:
		Type: Light
	Mobile:
		Speed: 100
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: StealthAPCLaser
		LocalOffset: 85,0,171
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 5
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e7: loaded-cmdo
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			mast: loaded-cmdo
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	Cloak@NORMAL:
		InitialDelay: 89
		CloakDelay: 90
		CloakSound: trans1.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: !cloak-force-disabled && !being-warped && !empdisable && !driver-dead
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	GrantTimedConditionOnCargoAction:
		Condition: cloak-force-disabled
		Actions: Load
		Duration: 30
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	Encyclopedia:
		Category: Nod/Vehicles

SAPC.AI:
	Inherits: SAPC
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: sapc
	Buildable:
		Prerequisites: ~botplayer, ~!tmpp, tmpl, ~vehicles.nod, ~techlevel.high
	Cargo:
		InitialUnits: N1,N1,N4,N3,N3
	WithCargoSounds:
		-EnterSounds:
	-Encyclopedia:

SAPC.AI2:
	Inherits: SAPC
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: sapc
	Buildable:
		Prerequisites: ~botplayer, ~tmpp, tmpl, ~vehicles.nod, ~techlevel.high
	Cargo:
		InitialUnits: N1C,N1C,N5,N3C,N3C
	-Encyclopedia:

HARV:
	Inherits: ^Vehicle-NOUPG
	Inherits@HARVBALANCE: ^HarvesterBalancer
	Inherits@HARVDMGNOTIFY: ^HarvesterDamageNotification
	Inherits@SELECTION: ^SelectableEconomicUnit
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 10
		Prerequisites: anyrefinery, ~vehicles.ra, ~!charv.upgrade, ~techlevel.low
		Description: Collects Ore, Gems and Tiberium for processing.
	TooltipExtras:
		Weaknesses: • Unarmed\n• Resistant to mind control
	Valued:
		Cost: 1400
	Tooltip:
		Name: Ore Truck
		GenericName: Harvester
	Selectable:
		Priority: 7
		DecorationBounds: 1792, 1792
	Harvester:
		Resources: Tiberium, BlueTiberium, BlackTiberium, Ore, Gems
		BaleUnloadDelay: 1
		BaleLoadDelay: 4
		SearchFromProcRadius: 15
		SearchFromHarvesterRadius: 8
		HarvestFacings: 8
		EmptyCondition: no-ore
	DockClientManager:
	WithStoresResourcesPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 5
		ResourceSequences:
			Ore: pip-yellow
			Gems: pip-red
			Tiberium: pip-green
			BlueTiberium: pip-blue
			BlackTiberium: pip-green
	Health:
		HP: 75000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 72
		TurnSpeed: 24
		Locomotor: heavywheeled
	RevealsShroud:
		Range: 4c0
	WithHarvestAnimationCA:
	WithDockingAnimationCA:
		RefineryTypes: proc
	SpawnActorOnDeath:
		Actor: HARV.Husk
		RequiresCondition: !being-warped && !charv-upgrade
	GrantConditionOnTerrain@Tiberium:
		Condition: ontib
		TerrainTypes: Tiberium
	GrantConditionOnTerrain@Ore:
		Condition: onore
		TerrainTypes: Ore
	FireWarheadsOnDeath@Ore:
		RequiresCondition: !no-ore && onore && !being-warped
		Weapon: OreExplosion
	FireWarheadsOnDeath@Tib:
		RequiresCondition: !no-ore && ontib && !being-warped
		Weapon: TibExplosion
	-Crushable:
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
	GrantConditionOnPrerequisite@OREPUPGRADE:
		Prerequisites: charv.upgrade
		Condition: charv-upgrade
	PeriodicExplosion@WARPOUT:
		Weapon: HarvSwap
		RequiresCondition: charv-upgrade
	SpawnRandomActorOnDeath:
		Actors: c1,c7,c10
		Probability: 5
		RequiresCondition: !being-warped
	GrantConditionOnPrerequisite@GSHIELD:
		Condition: pointdef-upgrade
		Prerequisites: pointdef.upgrade
	TimedDamageMultiplier@GSHIELD:
		ActiveCondition: gshield
		ChargingColor: 008888
		DrainingColor: 00ffff
		Modifier: 25
		Duration: 75
		ChargeTime: 375
		ActivateSound: gshieldup.aud
		DeactivateSound: gshielddown.aud
		RequiresCondition: pointdef-upgrade
	WithIdleOverlay@NSHIELD:
		RequiresCondition: (nshield || gshield) && !(empdisable || invulnerability || invisibility)
	ChangesHealth@MINIDRONE:
		Step: 2250
	DamageTypeDamageMultiplier@A2GPROTECTION:
		Modifier: 60
	TransformOnCondition:
		RequiresCondition: charv-upgrade
		IntoActor: harv.chrono
	RejectsOrders@Transforming:
		RequiresCondition: charv-upgrade
	TransferResourcesOnTransform:
	UpdatesKillCount@BuildingsOrHarvesters:
		Type: BuildingsOrHarvesters
	ReplacedInQueue:
		Actors: harv.chrono
	StoresResources:
		Capacity: 26
		Resources: Tiberium, BlueTiberium, BlackTiberium, Ore, Gems
	Encyclopedia:
		Category: Allies/Vehicles; Soviets/Vehicles

HARV.Chrono:
	Inherits: HARV
	Buildable:
		BuildPaletteOrder: 11
		Prerequisites: anyrefinery, ~charv.upgrade, ~vehicles.allies, ~techlevel.high
	TooltipExtras:
		Attributes: • Teleports back to refinery
	RenderSprites:
		Image: charv
	Tooltip:
		Name: Chrono Miner
	Health:
		HP: 80000
	Mobile:
		Voice: Move
	Harvester:
		HarvestVoice: Harvest
	DockClientManager:
		Voice: Deliver
	ChronoResourceDelivery:
		Image: chrono
		Palette: ra2effect-ignore-lighting-alpha75
		WarpInSequence: warpout
		WarpOutSequence: warpout
		WarpInSound: vchrtele.aud
		WarpOutSound: ichrmova.aud
		AudibleThroughFog: false
	Voiced:
		VoiceSet: ChronoMinerVoice
	SpawnActorOnDeath:
		Actor: HARV.Chrono.Husk
		RequiresCondition: !being-warped
	-GrantConditionOnPrerequisite@OREPUPGRADE:
	-PeriodicExplosion@WARPOUT:
	-TransformOnCondition:
	-TransferResourcesOnTransform:
	-RejectsOrders@Transforming:
	StoresResources:
		Capacity: 18
	Encyclopedia:
		Category: Allies/Vehicles

MCV:
	Inherits: ^Vehicle-NOUPG
	Inherits@SELECTION: ^SelectableSupportUnit
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 600
		Prerequisites: vehicles.mcv, ~vehicles.ra, ~techlevel.low
		Description: Deploys into another Construction Yard.
		BuildDurationModifier: 50
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Special Ability: Deploy into Construction Yard
	Valued:
		Cost: 3000
	Tooltip:
		Name: Mobile Construction Vehicle
	Selectable:
		Priority: 4
		DecorationBounds: 1792, 1792
	Health:
		HP: 75000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 54
		Locomotor: heavywheeled
	RevealsShroud:
		Range: 4c0
	Transforms:
		IntoActor: fact
		Offset: -1,-1
		Facing: 384
		TransformSounds: placbldg.aud, build5.aud
		NoTransformNotification: BuildingCannotPlaceAudio
		NoTransformTextNotification: Cannot deploy here.
		PauseOnCondition: empdisable || being-warped
	MustBeDestroyed:
		RequiredForShortGame: true
	BaseBuilding:
	ProvidesPrerequisite@anymcv:
		Prerequisite: anymcv
	SpawnActorOnDeath:
		Actor: MCV.Husk
		RequiresCondition: !being-warped
	TransferTimedExternalConditionOnTransform:
		Condition: invulnerability
	TransferTimedExternalConditionOnTransform@INVIS:
		Condition: invisibility
	-Crushable:
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	DamageTypeDamageMultiplier@A2GPROTECTION:
		Modifier: 60
	UpdatesKillCount@BuildingsOrHarvesters:
		Type: BuildingsOrHarvesters
	Encyclopedia:
		Category: Allies/Vehicles; Soviets/Vehicles

JEEP:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 20
		Prerequisites: ~vehicles.allies, ~techlevel.low
		Description: Fast scout & anti-infantry vehicle.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
		Attributes: • Can detect spies and cloaked units.
	Valued:
		Cost: 400
	Tooltip:
		Name: Ranger
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 15000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 40
		Speed: 144
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
		RequiresCondition: !optics-active
	RevealsShroud@GAPGEN:
		Range: 6c0
		RequiresCondition: !optics-active
	Turreted:
		TurnSpeed: 40
		Offset: 0,0,128
	Armament:
		Weapon: M60mg
		MuzzleSequence: muzzle
		LocalOffset: 128,0,43
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-125,200
		CasingTargetOffset: 0, -500, 0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	IgnoresDisguise:
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: !(empdisable || being-warped) && !optics-active
	DetectCloaked@Optics:
		Range: 13c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: !(empdisable || being-warped) && optics-active
	RevealsShroud@Optics:
		Range: 13c0
		RequiresCondition: optics-active
	GrantTimedConditionOnDeploy@Optics:
		DeployedCondition: optics-active
		RequiresCondition: optics-upgrade
		ShowSelectionBar: true
		StartsFullyCharged: true
		DeployedTicks: 375
		CooldownTicks: 1125
		ShowSelectionBarWhenFull: false
		ChargingColor: 808080
		DischargingColor: ffffff
		DeploySound: optics-enable.aud
		UndeploySound: optics-disable.aud
		Instant: true
	GrantConditionOnPrerequisite@Optics:
		Condition: optics-upgrade
		Prerequisites: optics.upgrade
	WithDecoration@Optics:
		Image: opticsactive
		Sequence: idle
		Palette: effect
		Position: Top
		ValidRelationships: Ally
		Margin: 0, 8
		RequiresCondition: optics-active
	WithRadiatingCircle:
		EndRadius: 13c0
		Color: 00bbff40
		MaxRadiusColor: 00bbff66
		MaxRadiusFlashColor: 00bbff66
		Interval: 75
		Duration: 50
		ValidRelationships: Ally
		AlwaysShowMaxRange: true
		RequiresCondition: optics-active
	Encyclopedia:
		Category: Allies/Vehicles

JEEP.TEMP:
	Inherits: JEEP
	Inherits@TEMPINC: ^TemporalReinforcement
	RenderSprites:
		Image: jeep
	FireWarheadsOnDeath:
		RequiresCondition: !warpout
	-ActorLostNotification:
	-Buildable:
	-ChronoshiftableCA:
	-HealthCapDamageMultiplier@CHRONO:
	-MapEditorData:
	-Encyclopedia:

APC:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 121
		Prerequisites: ~vehicles.allies, ~!rapc.upgrade, ~techlevel.low
		Description: Tough infantry transport.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 600
	Tooltip:
		Name: Armored Personnel Carrier
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 30000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 126
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: M60mg
		LocalOffset: 85,0,171
		MuzzleSequence: muzzle
	WithMuzzleOverlay:
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 5
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e7: loaded-cmdo
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			mast: loaded-cmdo
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	ReplacedInQueue:
		Actors: rapc
	Upgradeable@RAIDER:
		Type: rapc.upgrade
		UpgradingCondition: upgrading
		Actor: rapc
		UpgradeAtActors: fix, rep, srep
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Allies/Vehicles

RAPC:
	Inherits: APC
	Buildable:
		Prerequisites: ~vehicles.allies, ~rapc.upgrade, ~techlevel.medium
		Description: Tough infantry transport that fires high explosive shells.
	Valued:
		Cost: 800
	Mobile:
		Speed: 82
	RevealsShroud:
		Range: 6c0
	Tooltip:
		Name: Armored Personnel Carrier (Raider)
	TooltipExtras:
		Description: Tough infantry transport that fires high explosive shells.
		Strengths: • Strong vs Light Armor, Buildings, Defenses, Infantry
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
	-AttackFrontal:
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	Turreted:
		TurnSpeed: 28
	WithSpriteTurret:
	Armament:
		Weapon: 76mm
		LocalOffset: 500,0,250
		MuzzleSequence: muzzle2
	-ReplacedInQueue:
	-Upgradeable@RAIDER:
	-WithDecoration@UpgradeOverlay:

APC.AI:
	Inherits: APC
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: APC
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.allies, ~!rapc.upgrade, ~techlevel.low
	Cargo:
		InitialUnits: E1,E1,E1,E3,E3
	WithCargoSounds:
		-EnterSounds:
	-ReplacedInQueue:
	-Upgradeable@RAIDER:
	-WithDecoration@UpgradeOverlay:
	-Encyclopedia:

RAPC.AI:
	Inherits: RAPC
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: RAPC
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.allies, ~rapc.upgrade, ~techlevel.medium
	Cargo:
		InitialUnits: E1,E1,E1,E3,E3
	WithCargoSounds:
		-EnterSounds:
	-Encyclopedia:

MNLY:
	Inherits: ^Tank
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 340
		Prerequisites: repair, ~techlevel.low, ~vehicles.human
		Description: Lays mines to destroy unwary enemy units.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Can detect and disarm mines.
	Valued:
		Cost: 800
	Tooltip:
		Name: Minelayer
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Selectable:
		Priority: 5
	Health:
		HP: 30000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 126
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Minelayer:
		Mine: MINV
		TileUnknownName: build-valid
	MineImmune:
	AmmoPool:
		Ammo: 5
		RearmSound: minelay1.aud
		Armaments: none
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	DetectCloaked:
		Range: 5c0
		DetectionTypes: Mine
		RequiresCondition: !(empdisable || being-warped)
	RenderDetectionCircle:
	FireWarheadsOnDeath:
		Weapon: ATMine
	RenderSprites:
		Image: MNLY
	Rearmable:
		RearmActors: fix, rep
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Armament@PRIMARY:
		Weapon: MineDefuser
		Cursor: goldwrench
		OutsideRangeCursor: goldwrench
		Name: primary
		TargetRelationships: Enemy, Neutral
	AutoTarget:
	AutoTargetPriority@MINEDEFUSER:
		ValidTargets: Mine
		InvalidTargets: NoAutoTarget
	AttackFrontalCharged:
		FacingTolerance: 0
		ChargeLevel: 50
		DischargeRate: 50
		PauseOnCondition: empdisable || being-warped || blinded
		ShowSelectionBar: true
		SelectionBarColor: ffff00
	-WithDecoration@BOMBARD:
	-WithDecoration@BOMBARD2:
	-WithDecoration@BOMBARD3:
	Encyclopedia:
		Category: Allies/Vehicles; Soviets/Vehicles; GDI/Vehicles; Nod/Vehicles

TRUK:
	Inherits: ^Vehicle
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 350
		Prerequisites: ~techlevel.low, ~vehicles.human
		Description: Transports cash to other players.
		BuildDurationModifier: 30
	TooltipExtras:
		Weaknesses: • Unarmed
	Valued:
		Cost: 500
	Tooltip:
		Name: Supply Truck
	Selectable:
		Priority: 6
	Health:
		HP: 11000
	Armor:
		Type: Light
	Mobile:
		Speed: 126
	RevealsShroud:
		Range: 4c0
	DeliversCash:
		Payload: 500
	SpawnActorOnDeath:
		Actor: moneycrate
		RequiresCondition: !being-warped
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	-ProductionCostMultiplier@IndustrialPlantBoost:
	-ProductionCostMultiplier@OilRef:
	-WithDecoration@BOMBARD:
	-WithDecoration@BOMBARD2:
	-WithDecoration@BOMBARD3:
	Encyclopedia:
		Category: Allies/Vehicles; Soviets/Vehicles; GDI/Vehicles; Nod/Vehicles

TRUK.DROP:
	Inherits: TRUK
	Buildable:
		Queue: Dropzone
	RenderSprites:
		Image: truk
	-DeliversCash:
	CashTrickler:
		Interval: 50
		Amount: 1250
	KillsSelf:
		Delay: 50
		RemoveInstead: true
	-Selectable:
	SelectionDecorations:
	RejectsOrders:
	Interactable:
	-Encyclopedia:

MGG:
	Inherits: ^Vehicle
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 301
		Prerequisites: atek, ~vehicles.allies, ~techlevel.high
		Description: Regenerates the shroud nearby, obscuring enemy vision. Can channel the effect to reduce enemy weapon range and vision.
	TooltipExtras:
		Attributes: • Special Ability: Toggle Gap Generator
	Valued:
		Cost: 1000
	Tooltip:
		Name: Mobile Gap Generator
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 25000
	Armor:
		Type: Light
	Mobile:
		Speed: 72
	Carryable:
	WithIdleOverlay@SPINNER:
		Offset: -299,0,171
		Sequence: spinner
		PauseOnCondition: empdisable || being-warped || mgg-disabled
		RequiresCondition: !passenger
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	CreatesShroud:
		Range: 6c0
		RequiresCondition: !empdisable && !being-warped && !mgg-disabled
	RenderShroudCircleCA:
		Color: 999999AA
		RequiresCondition: !empdisable && !being-warped && !mgg-disabled
	SpawnActorOnDeath:
		Actor: MGG.Husk
		RequiresCondition: !being-warped
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	GrantConditionOnDeploy:
		DeployedCondition: mgg-disabled
	Armament@PRIMARY:
		Weapon: MobileGapBeam
		LocalOffset: 0, 0, 200
		MuzzleSequence: muzzle
	WithMuzzleOverlay:
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped
	Turreted:
		Offset: -299,0,171
	Encyclopedia:
		Category: Allies/Vehicles

MRJ:
	Inherits: ^Vehicle
	Inherits@AUTOTARGET: ^AutoTargetGround
	AutoTargetPriority@DEFAULT:
		ValidTargets: Vehicle, Ship, Defense
	Valued:
		Cost: 1000
	Tooltip:
		Name: Mobile Radar Jammer
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 300
		Prerequisites: atek, ~vehicles.allies, ~techlevel.high
		Description: Support vehicle that can disrupt enemy targeting systems.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Jamming fields reduce rate of fire and accuracy of enemy vehicles/defenses
	Health:
		HP: 25000
	Armor:
		Type: Light
	Mobile:
		Speed: 72
	RevealsShroud:
		Range: 7c0
	WithIdleOverlay@SPINNER:
		Sequence: spinner
		Offset: -256,0,256
		PauseOnCondition: empdisable || being-warped
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Armament:
		Weapon: RadarJammer
		LocalOffset: 0,0,0
	AttackFrontalCharged:
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 32
		ChargeLevel: 75
		DischargeRate: 5
		ShotsPerCharge: 1
		ShowSelectionBar: true
		SelectionBarColor: ffffff
		ChargingCondition: charging
	AmbientSoundCA:
		RequiresCondition: charging
		SoundFiles: jammer.aud
	WithIdleOverlay@JAMSIGNAL:
		Sequence: jamsignal
		RequiresCondition: charging
		Offset: -256,0,256
		IsDecoration: True
	Encyclopedia:
		Category: Allies/Vehicles

MSG:
	Inherits: ^Vehicle
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 330
		Prerequisites: anyradar, ~vehicles.shadow, ~techlevel.medium
		Description: Support unit that projects a cloaking field.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Special Ability: Deploys to cloak nearby units and structures
	Valued:
		Cost: 1250
	Tooltip:
		Name: Mobile Stealth Generator
		RequiresCondition: !deployed
	Tooltip@DEPLOYED:
		Name: Mobile Stealth Generator (deployed)
		RequiresCondition: deployed
	Selectable:
		DecorationBounds: 1621, 1536, 0, -42
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 18000
	Armor:
		Type: Light
	Mobile:
		Speed: 60
		ImmovableCondition: deployed
		RequireForceMoveCondition: !undeployed
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	SpawnActorOnDeath:
		Actor: MSG.Husk
		RequiresCondition: !being-warped
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	GrantCondition@PREVIEWWORKAROUND:
		Condition: real-actor
	WithMakeAnimation:
		BodyNames: deployedbody
	Carryable:
		LocalOffset: 0,0,150
	WithSpriteBody@deployed:
		Sequence: idle-deployed
		RequiresCondition: !undeployed && real-actor
		Name: deployedbody
	WithFacingSpriteBody:
		RequiresCondition: undeployed || !real-actor
	GrantConditionOnDeploy:
		PauseOnCondition: empdisable || being-warped || being-captured
		DeployedCondition: deployed
		UndeployedCondition: undeployed
		Facing: 368
		AllowedTerrainTypes: Clear, Road, Rough, Ore, Gems, Tiberium, BlueTiberium
		DeploySounds: placbldg.aud
		UndeploySounds: clicky1.aud
		UndeployOnMove: true
		UndeployOnPickup: true
	WithRangeCircle@MSG:
		Type: StealthGenerator
		RequiresCondition: deployed
		Range: 5c512
		Color: 00aa00
	ProximityExternalCondition@MSG:
		Range: 5c512
		Condition: sgencloak
		RequiresCondition: deployed && !cloak-force-disabled
		MaximumVerticalOffset: 512
		AffectsParent: true
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		Subfaction: shadow

TTRA:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 230
		Prerequisites: stek, ~vehicles.soviet, ~techlevel.high
		Description: Half-track with mounted Tesla Coil.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Jams nearby enemy radar
	Valued:
		Cost: 1350
	Tooltip:
		Name: Tesla Track
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 18000
	Armor:
		Type: Light
	Mobile:
		Speed: 60
		Voice: Move
		TurnSpeed: 16
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: TTrackZap
		LocalOffset: 0,0,213
		RequiresCondition: !tarc-upgrade
	Armament@PRIMARYUPG:
		Weapon: TTrackZap.UPG
		LocalOffset: 0,0,213
		RequiresCondition: tarc-upgrade
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
		TargetFrozenActors: True
	Turreted:
	WithIdleOverlay@SPINNER:
		Sequence: spinner
	ProximityExternalCondition@JAMMER:
		Range: 1c512
		ValidRelationships: Enemy, Neutral
		Condition: jammed
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1280, 1280
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: TeslaTankVoice
	SpawnActorOnDeath:
		Actor: TTRA.Husk
		RequiresCondition: !being-warped
	GrantConditionOnPrerequisite@TARC:
		Condition: tarc-upgrade
		Prerequisites: tarc.upgrade
	Encyclopedia:
		Category: Soviets/Vehicles

TTNK:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 182
		Prerequisites: tslaorstek, anyradar, ~vehicles.russia, ~techlevel.medium
		Description: Tank with twin mounted Tesla Coils.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft
	Valued:
		Cost: 1150
	Tooltip:
		Name: Tesla Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 60
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: TTankZap
		LocalOffset: 511,200,113, 511,-200,113
		RequiresCondition: !tarc-upgrade
		MuzzleSequence: muzzle
	Armament@PRIMARYUPG:
		Weapon: TTankZap.UPG
		LocalOffset: 511,200,113, 511,-200,113
		RequiresCondition: tarc-upgrade
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	Turreted:
		TurnSpeed: 28
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1280, 1280
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: TeslaTankRA2Voice
	SpawnActorOnDeath:
		Actor: TTNK.Husk
		RequiresCondition: !being-warped
	GrantConditionOnPrerequisite@TARC:
		Condition: tarc-upgrade
		Prerequisites: tarc.upgrade
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		Subfaction: russia

DTRK:
	Inherits: ^Vehicle
	Inherits@BERSERK: ^Berserk
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 312
		Prerequisites: stek, ~vehicles.soviet, ~techlevel.high
		Description: Truck carrying nuclear explosives.
	TooltipExtras:
		Weaknesses: • Very weak armor
		Attributes: • Special Ability: Detonate
	Valued:
		Cost: 2000
	Tooltip:
		Name: Demolition Truck
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 5000
	Armor:
		Type: Light
	Mobile:
		Speed: 72
		Voice: Move
	RevealsShroud:
		Range: 4c0
	FireWarheadsOnDeath:
		Weapon: MiniNuke
		EmptyWeapon: MiniNuke
		DamageSource: Killer
	AttackFrontal:
		FacingTolerance: 512
		Voice: Attack
	Armament@PRIMARY:
		Weapon: DemoTruckTargeting
	GrantConditionOnAttack:
		Condition: triggered
	GrantConditionOnDeploy:
		DeployedCondition: triggered
	-DamageMultiplier@IRONCURTAIN:
	KillsSelf:
		RequiresCondition: invulnerability || tibstealth || triggered || berserk
	ChronoshiftableCA:
		ExplodeInstead: true
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: TTruckVoice
	ExternalCondition@PRODUCED:
		Condition: produced
	VoiceAnnouncement:
		RequiresCondition: produced
		Voice: Build
		ValidRelationships: Neutral, Ally
	ProductionCostMultiplier@IRAQBONUS:
		Multiplier: 90
		Prerequisites: player.iraq
	Encyclopedia:
		Category: Soviets/Vehicles

CTNK:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeGround
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 220
		Prerequisites: atek, ~vehicles.allies, ~techlevel.high
		Description: Teleporting tank armed with twin missile launchers.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses, Buildings
		Weaknesses: • Weak vs Infantry
		Attributes: • Special Ability: Teleport\n• Teleporting longer distances requires charge up
	Valued:
		Cost: 1500
	Tooltip:
		Name: Chrono Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 32000
	Armor:
		Type: Light
	Mobile:
		Speed: 92
		TurnSpeed: 30
		Locomotor: heavywheeled
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: APTusk
		LocalOffset: -160,-276,232, -160,276,232
		LocalYaw: 60, -60
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	PortableChronoCA:
		Charges: 1
		ChargeDelay: 375
		Cooldown: 75
		HasDistanceLimit: true
		MaxDistance: 48
		MaxInstantDistance: 12
		PreChargeTicksPerCell: 7
		MaxPreChargeTicks: 250
		ResetRechargeOnUse: false
		RequiresCondition: !empdisable && !being-warped && !chronoshifted
		ShowSelectionBarWhenFull: false
		ShowCooldownSelectionBar: true
		RequireEmptyDestination: true
		PreChargeCondition: chronoprep
		MaxInstantCircleColor: EEEEEE44
		MaxInstantCircleBorderColor: 00000044
	WithColoredOverlay@PreCharge:
		RequiresCondition: chronoprep
		Color: 00eeff22
	WithIdleOverlay@PreCharge:
		Sequence: idle
		Image: chronoprep-overlay
		Palette: effect
		RequiresCondition: chronoprep
		IsDecoration: True
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1280, 1280
	ProductionCostMultiplier@GermanyBonus:
		Multiplier: 90
		Prerequisites: player.germany
	PortableChronoModifier@TEMPORALFLUX:
		ExtraCharges: 1
		RequiresCondition: tflx-upgrade
	GrantConditionOnPrerequisite@TEMPORALFLUX:
		Condition: tflx-upgrade
		Prerequisites: tflx.upgrade
	WithChronoshiftChargePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresCondition: tflx-upgrade
	AmbientSoundCA@Prep:
		SoundFiles: chronoprep.aud
		RequiresCondition: chronoprep
		Interval: 1000
	Encyclopedia:
		Category: Allies/Vehicles

QTNK:
	Inherits: ^Tank
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 310
		Prerequisites: stek, ~vehicles.qtnk, ~techlevel.high
		Description: Deals seismic damage to nearby vehicles and structures.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses, Buildings
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
		Attributes: • Special Ability: Seismic Shockwave
	Valued:
		Cost: 1700
	Tooltip:
		Name: MAD Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 120000
	Armor:
		Type: Heavy
	Mobile:
		PauseOnCondition: deployed || being-captured || empdisable || being-warped || driver-dead || notmobile
		Speed: 46
	ChronoshiftableCA:
		RequiresCondition: !deployed && !being-captured && !being-warped
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	MadTankCA:
		DeployedCondition: deployed
		KillsSelf: false
		DriverActor: e1
		ChargeDelay: 60
		DetonationDelay: 40
		DetonationSequence: piston
		DetonationWeapon: MADTankDetonate
		FirstDetonationImmediate: true
		PauseOnCondition: empdisable || being-warped
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	WithRangeCircle:
		Type: MADTank
		Color: FFFF0080
		Range: 7c0
	-Crushable:
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@MADTank:
		RequiresCondition: !being-warped
		TargetTypes: MADTank
	FirepowerMultiplier@MADIC:
		Modifier: 40
		RequiresCondition: invulnerability
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: deployed || driver-dead
	Targetable@DriverKillImmune:
		TargetTypes: DriverKillImmune
		RequiresCondition: deployed
	Encyclopedia:
		Category: Soviets/Vehicles

AMCV:
	Inherits: ^VehicleTD-NOUPG
	Inherits@SELECTION: ^SelectableSupportUnit
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 601
		Prerequisites: vehicles.mcv, ~vehicles.td, ~techlevel.low
		IconPalette: chrometd
		Description: Deploys into another Construction Yard.
		BuildDurationModifier: 50
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Special Ability: Deploy into Construction Yard
	Valued:
		Cost: 3000
	Tooltip:
		Name: Mobile Construction Vehicle
	Selectable:
		Priority: 4
		Bounds: 1792, 1792
	Health:
		HP: 75000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 54
		Locomotor: heavywheeled
	RevealsShroud:
		Range: 4c0
	Transforms:
		IntoActor: afac
		Offset: -1,-1
		Facing: 384
		TransformSounds: placbldg.aud, build5.aud
		NoTransformNotification: BuildingCannotPlaceAudio
		NoTransformTextNotification: Cannot deploy here.
		PauseOnCondition: empdisable || being-warped
	MustBeDestroyed:
		RequiredForShortGame: true
	BaseBuilding:
	ProvidesPrerequisite@anymcv:
		Prerequisite: anymcv
	SpawnActorOnDeath:
		Actor: AMCV.Husk
		RequiresCondition: !being-warped
	TransferTimedExternalConditionOnTransform:
		Condition: invulnerability
	TransferTimedExternalConditionOnTransform@INVIS:
		Condition: invisibility
	-Crushable:
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	DamageTypeDamageMultiplier@A2GPROTECTION:
		Modifier: 60
	UpdatesKillCount@BuildingsOrHarvesters:
		Type: BuildingsOrHarvesters
	Encyclopedia:
		Category: GDI/Vehicles; Nod/Vehicles

HMMV:
	Inherits: ^VehicleTD
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	RenderSprites:
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 40
		IconPalette: chrometd
		Prerequisites: ~vehicles.hmmv, ~!tow.upgrade, ~techlevel.low
		Description: Fast scout & anti-infantry vehicle.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
		Attributes: • Can detect spies and cloaked units.
	Valued:
		Cost: 400
	Tooltip:
		Name: Hum-Vee
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 15000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 40
		Speed: 144
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Turreted:
		TurnSpeed: 40
		Offset: -85,0,128
	Armament:
		Weapon: M60mgTD
		LocalOffset: 171,0,85
		MuzzleSequence: muzzle
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-125,200
		CasingTargetOffset: 0, -500, 0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret:
	IgnoresDisguise:
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: !(empdisable || being-warped)
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnPrerequisite@GSHIELD:
		Condition: pointdef-upgrade
		Prerequisites: pointdef.upgrade
	TimedDamageMultiplier@GSHIELD:
		ActiveCondition: gshield
		ChargingColor: 008888
		DrainingColor: 00ffff
		Modifier: 33
		Duration: 100
		ChargeTime: 625
		ActivateSound: gshieldup.aud
		DeactivateSound: gshielddown.aud
		RequiresCondition: pointdef-upgrade
	WithIdleOverlay@NSHIELD:
		Image: nshield-overlay-sm
		RequiresCondition: (nshield || gshield) && !(empdisable || invulnerability || invisibility)
	ReplacedInQueue:
		Actors: hmmv.tow
	Upgradeable@TOW:
		Type: tow.upgrade
		UpgradingCondition: upgrading
		Actor: hmmv.tow
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 200
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: GDI/Vehicles

HMMV.TOW:
	Inherits: HMMV
	RenderSprites:
		Image: hmmv
	Buildable:
		Description: Fast scout & anti-infantry vehicle with anti-tank missile launcher.
		Prerequisites: ~vehicles.hmmv, ~tow.upgrade, ~techlevel.high
	Valued:
		Cost: 575
	Mobile:
		Speed: 130
	Tooltip:
		Name: TOW Hum-Vee
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Heavy Armor
		Weaknesses: • Weak vs Defenses\n• Cannot attack Aircraft
	Armament@SECONDARY:
		Name: secondary
		Weapon: TOW
		LocalOffset: 250,100,150
		PauseOnCondition: !ammo
	WithSpriteTurret:
		Sequence: turrettow
	AmmoPool:
		Armaments: secondary
		Ammo: 1
		AmmoCondition: ammo
	ReloadAmmoPoolCA:
		Delay: 200 # matches reload time of weapon
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: aaaaaa
	-ReplacedInQueue:
	-Upgradeable@TOW:
	-WithDecoration@UpgradeOverlay:

GDRN:
	Inherits: ^Vehicle
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HACKABLE: ^Hackable
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	RenderSprites:
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 41
		IconPalette: chrome
		Prerequisites: ~vehicles.arc, ~!tow.upgrade, ~techlevel.low
		Description: Fast scout & anti-tank drone.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Buildings
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
		Attributes: • Can detect spies and cloaked units\n• Immune to mind control
	Valued:
		Cost: 450
	Tooltip:
		Name: Guardian Drone
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 17000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 40
		Speed: 126
		Voice: Move
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 40
		Offset: 0,0,0
	Armament:
		Weapon: GuardianDroneGun
		LocalOffset: 500,0,200
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: gshield || empdisable || being-warped || blinded
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	Voiced:
		VoiceSet: RobotTankVoice
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@DriverKillImmune:
		TargetTypes: DriverKillImmune
	IgnoresDisguise:
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: !(empdisable || being-warped)
	GrantConditionOnPrerequisite@GSHIELD:
		Condition: pointdef-upgrade
		Prerequisites: pointdef.upgrade
	TimedDamageMultiplier@GSHIELD:
		ActiveCondition: gshield
		ChargingColor: 008888
		DrainingColor: 00FFFF61
		Modifier: 33
		Duration: 100
		ChargeTime: 625
		ActivateSound: gshieldup.aud
		DeactivateSound: gshielddown.aud
		RequiresCondition: pointdef-upgrade
	WithIdleOverlay@NSHIELD:
		Image: nshield-overlay-sm
		RequiresCondition: (nshield || gshield) && !(empdisable || invulnerability || invisibility)
	ReplacedInQueue:
		Actors: gdrn.tow
	Upgradeable@TOW:
		Type: tow.upgrade
		UpgradingCondition: upgrading
		Actor: gdrn.tow
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 200
		RequiresCondition: !mindcontrolled
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-warped
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: arc

GDRN.TOW:
	Inherits: GDRN
	RenderSprites:
		Image: gdrn
	Buildable:
		Prerequisites: ~vehicles.arc, ~tow.upgrade, ~techlevel.high
	Valued:
		Cost: 575
	Tooltip:
		Name: TOW Guardian Drone
	Mobile:
		Speed: 118
	Armament@SECONDARY:
		Name: secondary
		Weapon: TOW
		LocalOffset: 200,-150,250
		PauseOnCondition: !ammo
	WithSpriteTurret:
		Sequence: turrettow
	AmmoPool:
		Armaments: secondary
		Ammo: 1
		AmmoCondition: ammo
	ReloadAmmoPoolCA:
		Delay: 200 # matches reload time of weapon
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: aaaaaa
	-ReplacedInQueue:
	-Upgradeable@TOW:
	-WithDecoration@UpgradeOverlay:

BGGY:
	Inherits: ^VehicleTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	RenderSprites:
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 50
		IconPalette: chrometd
		Prerequisites: ~vehicles.nod, ~techlevel.low
		Description: Fast scout & anti-infantry vehicle.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
		Attributes: • Can detect spies and cloaked units.
	Valued:
		Cost: 350
	Tooltip:
		Name: Buggy
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 13250
	Armor:
		Type: Light
	Mobile:
		Speed: 144
		TurnSpeed: 40
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Turreted:
		TurnSpeed: 40
		Offset: -43,0,128
	Armament:
		Weapon: M60mgTD
		LocalOffset: 171,0,43
		MuzzleSequence: muzzle
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-125,200
		CasingTargetOffset: 0, -500, 0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret:
	IgnoresDisguise:
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: !(empdisable || being-warped)
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnPrerequisite@ZealBonus:
		Condition: zeal-covenant
		Prerequisites: zeal.covenant
	SpeedMultiplier@ZealBonus:
		Modifier: 115
		RequiresCondition: zeal-covenant
	GrantConditionOnPrerequisite@BlackHandPlayer:
		Condition: player-blackh
		Prerequisites: player.blackh
	GrantConditionOnPrerequisite@Decoy:
		Condition: decoy-upgrade
		Prerequisites: decoy.upgrade
	AmmoPool@Decoy:
		Name: decoyspawner
		Armaments: decoyspawner
		AmmoCondition: ammo
	WithAmmoPipsDecoration@Decoy:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		RequiresCondition: decoy-upgrade
	ReloadAmmoPoolCA@Decoy:
		AmmoPool: decoyspawner
		Delay: 1500
		ShowSelectionBar: true
		SelectionBarColor: aaaaaa
	SpawnActorAbility@Decoy:
		Type: Decoy
		Actors: ftnk.decoy, ftnk.decoy
		SpawnSounds: decoyspawn.aud
		TargetModifiedCursor: ability2
		Behavior: SpawnAtSelfAndMoveToTarget
		RequiresCondition: !player-blackh && decoy-upgrade && ammo && !(empdisable || being-warped)
		AmmoPool: decoyspawner
		FaceTarget: true
	SpawnActorAbility@BlackHandDecoy:
		Type: BlackHandDecoy
		Actors: hftk.decoy, hftk.decoy
		SpawnSounds: decoyspawn.aud
		TargetModifiedCursor: ability2
		Behavior: SpawnAtSelfAndMoveToTarget
		RequiresCondition: player-blackh && decoy-upgrade && ammo && !(empdisable || being-warped)
		AmmoPool: decoyspawner
		FaceTarget: true
	Encyclopedia:
		Category: Nod/Vehicles

^DecoyUnit:
	GrantTimedCondition@Decoy:
		Condition: active
		Duration: 750
	WithFlashEffect@Flash:
		Color: ffffff
		Interval: 748
	KillsSelf:
		RequiresCondition: !active
	WithTextDecoration@Decoy:
		Text: Decoy
		ValidRelationships: Ally
		Font: TinyBold
		Position: Top
		Margin: 0, 7
	TimedConditionBar@Decoy:
		Condition: active
		Color: ffffff
	WithPalettedOverlay@Decoy:
		Palette: decoy
		ValidRelationships: Ally
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@DriverKillImmune:
		TargetTypes: DriverKillImmune
	Targetable@IronCurtainImmune:
		TargetTypes: IronCurtainImmune
	Targetable@ChronoshiftImmune:
		TargetTypes: ChronoshiftImmune
	SpawnActorOnDeath:
		Actor: camera.decoy

FTNK.Decoy:
	Inherits: FTNK
	Inherits@Decoy: ^DecoyUnit
	RenderSprites:
		Image: ftnk
	Health:
		HP: 50
	Mobile:
		Locomotor: wheeled
	Armament@PRIMARY:
		Weapon: BigFlamerTD.Decoy
	-Armament@FF:
	FireWarheadsOnDeath:
		Weapon: DecoyDespawn
		EmptyWeapon: DecoyDespawn
		-RequiresCondition:
	-ActorLostNotification:
	-Buildable:
	-ProducibleWithLevel:
	-WithProductionIconOverlay:
	-RevealsShroud:
	-RevealsShroud@GAPGEN:
	-Repairable:
	-Passenger:
	-Berserkable@BERSERK:
	-RejectsOrders@BERSERK:
	-GivesExperienceCA:
	-GrantConditionOnPrerequisite@GLOBALBOUNTY:
	-GivesBountyCA:
	-Targetable@REPAIR:
	-GrantConditionOnHealingReceived@REPAIRCOOLDOWN:
	-UpdatesPlayerStatistics:
	-RevealOnDeath:
	-Sellable:
	-ExternalCondition@UNITSELL:
	-MapEditorData:
	-Encyclopedia:

HFTK.Decoy:
	Inherits: HFTK
	Inherits@Decoy: ^DecoyUnit
	RenderSprites:
		Image: hftk
	Armor:
		Type: Light
	Health:
		HP: 50
	Mobile:
		Locomotor: wheeled
	Armament@PRIMARY:
		Weapon: HeavyFlameTankFlamer.Decoy
	Armament@PRIMARYUPG:
		Weapon: HeavyFlameTankFlamer.UPG.Decoy
	-Armament@FF:
	-Armament@FFUPG:
	FireWarheadsOnDeath:
		Weapon: DecoyDespawn
		EmptyWeapon: DecoyDespawn
		-RequiresCondition:
	-ActorLostNotification:
	-Buildable:
	-ProducibleWithLevel:
	-WithProductionIconOverlay:
	-RevealsShroud:
	-RevealsShroud@GAPGEN:
	-Repairable:
	-Passenger:
	-Berserkable@BERSERK:
	-RejectsOrders@BERSERK:
	-GivesExperienceCA:
	-GrantConditionOnPrerequisite@GLOBALBOUNTY:
	-GivesBountyCA:
	-Targetable@REPAIR:
	-GrantConditionOnHealingReceived@REPAIRCOOLDOWN:
	-UpdatesPlayerStatistics:
	-RevealOnDeath:
	-Sellable:
	-ExternalCondition@UNITSELL:
	-MapEditorData:
	-Encyclopedia:

APC2:
	Inherits: ^TankTD
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Inherits@COMMANDOSKULL: ^CommandoSkull
	RenderSprites:
		FactionImages:
			nod: apc2.nod
			blackh: apc2.nod
			legion: apc2.nod
			marked: apc2.nod
			shadow: apc2.nod
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 140
		Prerequisites: ~vehicles.td, ~!vulcan.upgrade, ~vehicles.apc2, ~techlevel.low
		IconPalette: chrometd
		Description: Tough infantry transport.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 600
	Tooltip:
		Name: Armored Personnel Carrier
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 30000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 126
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: M60mgTD
		LocalOffset: 85,0,171
		MuzzleSequence: muzzle
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	WithMuzzleOverlay:
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 5
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e7: loaded-cmdo
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			mast: loaded-cmdo
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	Selectable:
		Class: apc
	ReplacedInQueue:
		Actors: vulc
	Upgradeable@VULCAN:
		Type: vulcan.upgrade
		UpgradingCondition: upgrading
		Actor: vulc
		UpgradeAtActors: fix, rep, srep
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: GDI/Vehicles; Nod/Vehicles

APC2.NODAI:
	Inherits: APC2
	Inherits@AIUNLOAD: ^AIUNLOAD
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.nod, ~!vulcan.upgrade, ~vehicles.apc2, ~techlevel.low
	RenderSprites:
		Image: APC2
	Cargo:
		InitialUnits: N1,N1,N1,N3,N4
	-ReplacedInQueue:
	-Upgradeable@VULCAN:
	-WithDecoration@UpgradeOverlay:
	-Encyclopedia:

APC2.GDIAI:
	Inherits: APC2
	Inherits@AIUNLOAD: ^AIUNLOAD
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.gdi, ~!vulcan.upgrade, ~vehicles.apc2, ~techlevel.low
	RenderSprites:
		Image: APC2
	Cargo:
		InitialUnits: N1,N1,N1,N2,N3
	-ReplacedInQueue:
	-Upgradeable@VULCAN:
	-WithDecoration@UpgradeOverlay:
	-Encyclopedia:

APC2.Reinforce:
	Inherits: APC2
	Inherits@AIUNLOAD: ^AIUNLOAD
	Selectable:
		Class: apc2
	RenderSprites:
		Image: APC2
	-Buildable:
	Cargo:
		InitialUnits: N1,N1,N2,N3,N3
	GrantConditionOnDamageState@UNLOAD:
		Condition: damage
	UnloadOnCondition@UNLOAD:
		RequiresCondition: damage
		BotOnly: False
	-Encyclopedia:

VULC:
	Inherits: ^TankTD
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 141
		Prerequisites: ~vehicles.gdi, ~vulcan.upgrade, ~techlevel.medium
		IconPalette: chrometd
		Description: Tough infantry transport, armed with a powerful chaingun.
	TooltipExtras:
		Strengths: • Strong vs Aircraft, Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses
	Valued:
		Cost: 800
	Tooltip:
		Name: Vulcan
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 30000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 100
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@GAT:
		Weapon: MGatt
		LocalOffset: 220,0,350
		MuzzleSequence: muzzle
		PauseOnCondition: reload-ground
		ReloadingCondition: reload-air
		RequiresCondition: gattling < 12
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	Armament@GAT2:
		Weapon: MGatt
		LocalOffset: 220,0,350
		MuzzleSequence: muzzle-huge
		PauseOnCondition: reload-ground
		ReloadingCondition: reload-air
		RequiresCondition: gattling >= 12
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	Armament@GATG:
		Name: secondary
		Weapon: MGattG
		LocalOffset: 321,0,85
		MuzzleSequence: muzzle
		PauseOnCondition: reload-air
		ReloadingCondition: reload-ground
		RequiresCondition: gattling-ground < 14
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	Armament@GATG2:
		Name: secondary
		Weapon: MGattG
		LocalOffset: 321,0,85
		MuzzleSequence: muzzle-huge
		PauseOnCondition: reload-air
		ReloadingCondition: reload-ground
		RequiresCondition: gattling-ground >= 14
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	FirepowerMultiplier@GAT1:
		Modifier: 115
		RequiresCondition: (attacking-air && gattling >= 1 && gattling < 3) || (attacking-ground && gattling-ground >= 1 && gattling-ground < 5)
	FirepowerMultiplier@GAT2:
		Modifier: 130
		RequiresCondition: (attacking-air && gattling >= 3 && gattling < 7) || (attacking-ground && (gattling-ground >= 5 && (gattling-ground < 10 || !rank-elite)))
	FirepowerMultiplier@GAT3:
		Modifier: 145
		RequiresCondition: (attacking-air && gattling >= 7 || (attacking-ground && (gattling-ground >= 10 && rank-elite)))
	AmbientSoundCA@ATTACKSOUNDINITIAL:
		SoundFiles: vvullo1a.aud
		RequiresCondition: (attacking-air && gattling < 2) || (attacking-ground && gattling-ground < 2)
	AmbientSoundCA@ATTACKSOUND1:
		SoundFiles: vvullo2a.aud, vvullo2b.aud, vvullo2c.aud
		FinalSound: vvullo3a.aud
		RequiresCondition: (attacking-air && gattling >= 2 && gattling < 6) || (attacking-ground && gattling-ground >= 2 && gattling-ground < 6)
	AmbientSoundCA@ATTACKSOUND2:
		InitialSound: vvullo4a.aud
		SoundFiles: vvullo5a.aud, vvullo5b.aud
		FinalSound: vvullo6a.aud
		RequiresCondition: (attacking-air && gattling >= 6 && gattling < 12) || (attacking-ground && (gattling-ground >= 6 && (gattling-ground < 14 || !rank-elite)))
	AmbientSoundCA@ATTACKSOUND3:
		InitialSound: vvullo7a.aud
		SoundFiles: vvullo8a.aud, vvullo8b.aud
		FinalSound: vvullo9a.aud
		RequiresCondition: (attacking-air && gattling >= 12 || (attacking-ground && (gattling-ground >= 14 && rank-elite)))
	GrantConditionOnAttackCA@ATTACKING-AIR:
		ArmamentNames: primary
		Condition: attacking-air
		RevokeDelay: 6
	GrantConditionOnAttackCA@ATTACKING-GROUND:
		ArmamentNames: secondary
		Condition: attacking-ground
		RevokeDelay: 6
	GrantConditionOnAttack:
		Condition: gattling
		MaximumInstances: 12
		RevokeDelay: 40
		RevokeOnNewTarget: False
		RevokeAll: True
		RequiresCondition: !attacking-ground
	GrantConditionOnAttack@GROUND:
		ArmamentNames: secondary
		Condition: gattling-ground
		MaximumInstances: 14
		RevokeDelay: 40
		RevokeOnNewTarget: False
		RevokeAll: True
		RequiresCondition: !attacking-air
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary, secondary
	Turreted:
		TurnSpeed: 28
		Offset: 0,0,150
	WithMuzzleOverlay:
	WithSpriteTurret:
		RequiresCondition: !attacking-ground
	WithSpriteTurret@GROUND:
		Sequence: turret-ground
		RequiresCondition: attacking-ground
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 5
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e7: loaded-cmdo
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			mast: loaded-cmdo
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	Encyclopedia:
		Category: GDI/Vehicles

VULC.AI:
	Inherits: VULC
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: VULC
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.gdi, ~vulcan.upgrade, ~techlevel.medium
	Cargo:
		InitialUnits: N1,N1,N1,N2,N3
	WithCargoSounds:
		-EnterSounds:
	-Encyclopedia:

VULC.Reinforce:
	Inherits: VULC
	Inherits@AIUNLOAD: ^AIUNLOAD
	Selectable:
		Class: vulc
	RenderSprites:
		Image: vulc
	-Buildable:
	Cargo:
		InitialUnits: N1,N1,N2,N3,N3
	GrantConditionOnDamageState@UNLOAD:
		Condition: damage
	UnloadOnCondition@UNLOAD:
		RequiresCondition: damage
		BotOnly: False
	-Encyclopedia:

LTNK:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlightlySlowedByCrushing
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	RenderSprites:
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 100
		IconPalette: chrometd
		Prerequisites: ~vehicles.ltnk, ~!lastnk.upgrade, ~techlevel.low
		Description: Standard front-line tank.
	TooltipExtras:
		Strengths: • Strong vs Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
		Attributes: • Amphibious
		RequiresCondition: !lastnk-upgrade
	TooltipExtras@LaserTank:
		Strengths: • Strong vs Light Armor, Infantry
		Weaknesses: • Weak vs Defenses\n• Cannot attack Aircraft
		Attributes: • Amphibious
		RequiresCondition: lastnk-upgrade
	Valued:
		Cost: 700
	Tooltip:
		Name: Light Tank
		RequiresCondition: !lastnk-upgrade
	Tooltip@LaserTank:
		Name: Laser Light Tank
		RequiresCondition: lastnk-upgrade
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 37000
	Armor:
		Type: Heavy
	Mobile:
		Locomotor: Amphibious
		Speed: 76
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	Targetable@WATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater && (!parachute && !being-warped)
	WithFacingSpriteBody:
		RequiresCondition: !onwater || parachute
	WithFacingSpriteBody@WATER:
		Sequence: idle-water
		Name: body-water
		RequiresCondition: onwater && !parachute
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 28
	Armament:
		Weapon: 30mm
		Recoil: 85
		RecoilRecovery: 17
		LocalOffset: 720,0,90
		MuzzleSequence: muzzle
		RequiresCondition: !lastnk-upgrade
	Armament@UPG:
		Weapon: LightTankLaser
		Recoil: 85
		RecoilRecovery: 17
		LocalOffset: 620,0,90
		MuzzleSequence: muzzlelas
		MuzzlePalette: caneon
		RequiresCondition: lastnk-upgrade
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded || parachute
	WithMuzzleOverlay:
	WithSpriteTurret:
		RequiresCondition: !lastnk-upgrade
	WithSpriteTurret@LaserTank:
		Sequence: turretlas
		RequiresCondition: lastnk-upgrade
	SpawnActorOnDeath:
		Actor: LTNK.Husk
		RequiresCondition: !onwater && !being-warped
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnPrerequisite@LaserTank:
		Condition: lastnk-upgrade
		Prerequisites: lastnk.upgrade
	ReplacedInQueue:
		Actors: ltnk.laser
	Encyclopedia:
		Category: Nod/Vehicles

LTNK.Laser:
	Inherits: LTNK
	RenderSprites:
		Image: ltnk
	Buildable:
		Prerequisites: ~vehicles.ltnk, ~lastnk.upgrade, ~techlevel.low
	-Tooltip:
	Tooltip@LaserTank:
		-RequiresCondition:
	-TooltipExtras:
	TooltipExtras@LaserTank:
		-RequiresCondition:
	-GrantConditionOnPrerequisite@LaserTank:
	-Armament:
	Armament@UPG:
		-RequiresCondition:
	-WithSpriteTurret:
	WithSpriteTurret@LaserTank:
		-RequiresCondition:
	Selectable:
		Class: ltnk
	-ReplacedInQueue:

MTNK:
	Inherits: ^TankTD
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlightlySlowedByCrushing
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	RenderSprites:
		FactionImages:
			nod: mtnk.nod
			blackh: mtnk.nod
			legion: mtnk.nod
			marked: mtnk.nod
			shadow: mtnk.nod
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 90
		Prerequisites: ~!bdrone.upgrade, ~!lastnk.upgrade, ~vehicles.mtnk, ~techlevel.low
		IconPalette: chrometd
		Description: Main battle tank.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
		RequiresCondition: !lastnk-upgrade
	TooltipExtras@LaserTank:
		Strengths: • Strong vs Light Armor, Infantry
		Weaknesses: • Weak vs Defenses\n• Cannot attack Aircraft
		RequiresCondition: lastnk-upgrade
	Valued:
		Cost: 900
	Tooltip:
		Name: Battle Tank
		GenericName: Tank
		RequiresCondition: !lastnk-upgrade
	Tooltip@LaserTank:
		Name: Laser Battle Tank
		GenericName: Tank
		RequiresCondition: lastnk-upgrade
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 52000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 72
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 20
		Offset: 0,0,30
	Turreted@SECONDARY:
		Turret: secondary
		Offset: 0,0,40
		TurnSpeed: 512
	Armament:
		Weapon: 120mm
		Recoil: 128
		RecoilRecovery: 26
		LocalOffset: 768,0,90
		MuzzleSequence: muzzle
		RequiresCondition: !lastnk-upgrade
	Armament@UPG:
		Weapon: BattleTankLaser
		Recoil: 128
		RecoilRecovery: 26
		LocalOffset: 720,0,90
		MuzzleSequence: muzzlelas
		MuzzlePalette: caneon
		RequiresCondition: lastnk-upgrade
	Armament@SECONDARY:
		Name: secondary
		Turret: secondary
		Weapon: PointLaser
		LocalOffset: 0,10,90
		PauseOnCondition: empdisable || being-warped || pointlaser-charging
		RequiresCondition: pointdef-upgrade
		ForceTargetRelationships: Enemy
	AttackTurreted:
		Turrets: primary, secondary
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret@default:
		RequiresCondition: !pointdef-upgrade && !lastnk-upgrade
	WithSpriteTurret@pointlaser:
		Sequence: turretpd
		RequiresCondition: pointdef-upgrade
	WithSpriteTurret@LaserTank:
		Sequence: turretlas
		RequiresCondition: lastnk-upgrade
	SpawnActorOnDeath:
		Actor: MTNK.Husk
		RequiresCondition: !being-warped
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	PointDefense:
		Armament: secondary
		PointDefenseTypes: Missile
		RequiresCondition: pointdef-upgrade && !gshield && !gshield-charging
		ValidRelationships: Enemy
	GrantTimedConditionOnPointDefenseHit@POINTDEF:
		Condition: pointlaser-charging
		RequiresCondition: pointdef-upgrade
		ScaleChargeTimeWithDamageAmount: 21
	GrantConditionOnPrerequisite@POINTDEF:
		Condition: pointdef-upgrade
		Prerequisites: pointdef.upgrade
	GrantConditionOnPrerequisite@LaserTank:
		Condition: lastnk-upgrade
		Prerequisites: lastnk.upgrade
	TimedDamageMultiplier@GSHIELD:
		ActiveCondition: gshield
		ChargingCondition: gshield-charging
		ShowSelectionBar: false
		Modifier: 25
		Duration: 2
		ScaleChargeTimeWithDamage: true
		ScaleChargeTimeWithDamageAmount: 21
		PauseOnCondition: pointlaser-charging
		RequiresCondition: pointdef-upgrade
	GrantTimedCondition@NSHIELDVISUAL:
		Condition: gshieldvisual
		RequiresCondition: gshield
		Duration: 8
		ForceFullDuration: true
	WithIdleOverlay@NSHIELD:
		RequiresCondition: (nshield || gshieldvisual) && !(empdisable || invulnerability || invisibility)
	ReplacedInQueue:
		Actors: mtnk.drone, mtnk.laser
	Upgradeable@BDRONE:
		Type: bdrone.upgrade
		UpgradingCondition: upgrading
		Actor: mtnk.drone
		UpgradeAtActors: fix, rep, srep
		Cost: 0
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: GDI/Vehicles; Nod/Vehicles

MTNK.Drone:
	Inherits: MTNK
	Inherits@IDISABLE: ^DisabledByRadarLoss
	Inherits@HACKABLE: ^Hackable
	RenderSprites:
		Image: drone
		-FactionImages:
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		Prerequisites: ~bdrone.upgrade, ~vehicles.mtnk, ~techlevel.high
		IconPalette: chrometd
		Description: Remotely piloted battle tank.
		BuildPaletteOrder: 91
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Infantry, Defenses\n• Cannot attack Aircraft
		Attributes: • Self repairs to 50% out of combat\n• Immune to mind control\n• Requires active radar communication
		-RequiresCondition:
	-Tooltip@LaserTank:
	-TooltipExtras@LaserTank:
	Valued:
		Cost: 800
	Tooltip:
		Name: Battle Drone
		-RequiresCondition:
	Mobile:
		PauseOnCondition: !radarenabled || being-captured || empdisable || being-warped || driver-dead || notmobile
		Voice: Move
	Passenger:
		Voice: Move
	Turreted:
		Offset: 0,0,20
	Armament:
		Weapon: 120mm.Drone
	AttackTurreted:
		PauseOnCondition: !radarenabled || empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	SpawnActorOnDeath:
		Actor: MTNK.Drone.Husk
	Voiced:
		VoiceSet: DroneVoice
		RequiresCondition: radarenabled && !empdisable
	Voiced@OFFLINE:
		VoiceSet: OfflineDroneVoice
		RequiresCondition: !radarenabled || empdisable
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	ChangesHealth:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 50
		DamageCooldown: 150
		RequiresCondition: !mdrn-attached
	-Armament@UPG:
	-WithSpriteTurret@LaserTank:
	WithSpriteTurret@default:
		RequiresCondition: !pointdef-upgrade
	-SpeedMultiplier@BERSERK:
	-ReloadDelayMultiplier@BERSERK:
	-ExternalCondition@BERSERK:
	-Berserkable@BERSERK:
	-WithPalettedOverlay@BERSERK:
	-TimedConditionBar@BERSERK:
	-RejectsOrders@BERSERK:
	-GrantCondition@BERSERK:
	-ReplacedInQueue:
	-Upgradeable@BDRONE:
	-WithDecoration@UpgradeOverlay:
	-ProducibleWithLevel:
	-WithProductionIconOverlay:
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-warped
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: arc

MTNK.Laser:
	Inherits: MTNK
	RenderSprites:
		Image: mtnk
	Buildable:
		Prerequisites: ~vehicles.mtnk, ~lastnk.upgrade, ~techlevel.low
	-Tooltip:
	Tooltip@LaserTank:
		-RequiresCondition:
	-TooltipExtras:
	TooltipExtras@LaserTank:
		-RequiresCondition:
	-GrantConditionOnPrerequisite@POINTDEF:
	-GrantConditionOnPrerequisite@LaserTank:
	-PointDefense:
	-GrantTimedConditionOnPointDefenseHit@POINTDEF:
	-TimedDamageMultiplier@GSHIELD:
	-GrantTimedCondition@NSHIELDVISUAL:
	WithIdleOverlay@NSHIELD:
		RequiresCondition: nshield && !(empdisable || invulnerability || invisibility)
	-Armament:
	-Armament@SECONDARY:
	Armament@UPG:
		-RequiresCondition:
	-WithSpriteTurret@default:
	-WithSpriteTurret@pointlaser:
	-WithDecoration@UpgradeOverlay:
	WithSpriteTurret@LaserTank:
		-RequiresCondition:
	Selectable:
		Class: mtnk
	-ReplacedInQueue:
	-Upgradeable@BDRONE:
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		Subfaction: legion

MDRN:
	Inherits: ^Vehicle
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@HACKABLE: ^Hackable
	Tooltip:
		Name: Mini Drone
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 192
		Prerequisites: anyradar, ~vehicles.arc, ~techlevel.medium
		Description: Hovering drone with machine gun.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses\n• Cannot attack Aircraft
		Attributes: • Attaches to vehicles to provide support and repairs\n• Immune to mind control
	Valued:
		Cost: 225
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Selectable:
		DecorationBounds: 682, 682, 0, -341
	Health:
		HP: 13000
	Armor:
		Type: Light
	DamageTypeDamageMultiplier@FLAKARMOR:
		DamageTypes: FlakVestMitigated
		Modifier: 60
	DamageTypeDamageMultiplier@FLAKARMORMINOR:
		DamageTypes: FlakVestMitigatedMinor
		Modifier: 80
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Mobile:
		Speed: 126
		TurnSpeed: 255
		Locomotor: seal
		Voice: Move
		PauseOnCondition: being-captured || empdisable || being-warped || driver-dead
	Voiced:
		VoiceSet: MiniDroneVoice
	Armament@PRIMARY:
		Weapon: M60mgMD
		LocalOffset: 72,0,72
		MuzzleSequence: muzzle
		PauseOnCondition: empdisable || being-warped
	WithMuzzleOverlay:
	Hovers:
		BobDistance: -35
		RequiresCondition: !empdisable && !being-warped
	WithShadow:
		Offset: 43, 128, -250
		ZOffset: -129
		RequiresCondition: !invisibility
	TurretedFloating:
		TurnSpeed: 18
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Turrets: primary
		Voice: Attack
	WithSpriteTurret:
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@DriverKillImmune:
		TargetTypes: DriverKillImmune
	-WithDamageOverlay:
	-Targetable@MINIDRONE:
	-ChangesHealth@MINIDRONE:
	-AttachableTo@MINIDRONE:
	Attachable:
		Type: MiniDrone
		TargetTypes: MiniDroneAttachable
		ValidRelationships: Ally
		MinAttachDistance: 1c512
		OnAttachTransformInto: mdrn.attached
		AttachSound: mdrn-attach.aud
	-Carryable:
	-GrantCondition@CarriedImmobile:
	WithIdleOverlay@NSHIELD:
		Image: nshield-overlay-sm
	FireWarheadsOnDeath:
		Weapon: VisualExplodeSmall
		EmptyWeapon: VisualExplodeSmall
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-warped
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: arc

MDRN.Attached:
	Inherits: MDRN
	-Buildable:
	-Selectable:
	-Tooltip:
	Interactable:
	RenderSprites:
		Image: mdrn
	-Mobile:
	AttachedAircraft:
		TurnSpeed: 18
		Speed: 0
		CanHover: true
		Repulsable: false
		CruiseAltitude: 1
		LandableTerrainTypes: Clear,Road,Rough,Ore,Gems,Tiberium,BlueTiberium,BlackTiberium,Water,Tree,River,Rock,Beach,Bridge,Tunnel,Wall,Ford
	RevealsShroud:
		Type: GroundPosition
	RevealsShroud@GAPGEN:
		Type: GroundPosition
	HiddenUnderFog:
		Type: GroundPosition
	WithSpriteTurret:
		Sequence: turret-attached
	TurnOnIdleCA:
		MinDelay: 25
		MaxDelay: 75
		TurnWhileAircraftPaused: true
	Armament@PRIMARY:
		Weapon: M60mgMD.Attached
		LocalOffset: -500,0,72
	DamageMultiplier@ATTACHED:
		Modifier: 10
	Attachable:
		OnDetachBehavior: Transform
		OnDetachTransformInto: mdrn
	ChangesHealth:
		PercentageStep: 2
		Delay: 25
		StartIfBelow: 100
		DamageCooldown: 50
	CaptureManager:
		-BeingCapturedCondition:
	-Targetable@HACKABLE:
	-MindControllable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	-ChronoshiftableCA:
	-HealthCapDamageMultiplier@CHRONO:
	-Encyclopedia:

HTNK:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeGround
	Inherits@BERSERK: ^Berserk
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 253
		IconPalette: chrometd
		Prerequisites: ~!mdrone.upgrade, gtek, ~vehicles.htnk, ~!ionmam.upgrade, ~!hovermam.upgrade, ~techlevel.high
		Description: Big and slow tank with anti-air capability.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Attributes: • Can crush concrete walls\n• Self repairs to 50% out of combat
	Valued:
		Cost: 1700
	Tooltip:
		Name: Mammoth Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 90000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 44
		Locomotor: heavytracked
		Voice: Move
	Passenger:
		Weight: 2
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 12
	Armament@PRIMARY:
		Weapon: 130mmTD
		LocalOffset: 900,180,340, 900,-180,340
		Recoil: 171
		RecoilRecovery: 30
		MuzzleSequence: muzzle
	Armament@SECONDARY:
		Name: secondary
		Weapon: MammothTusk
		LocalOffset: -85,384,340, -85,-384,340
		LocalYaw: -100,100
		Recoil: 43
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	Voiced:
		VoiceSet: MammothVoice
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: HTNK.Husk
		RequiresCondition: !being-warped
	ChangesHealth:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 50
		DamageCooldown: 150
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Carryable:
		LocalOffset: 0,0,500
	-Crushable:
	WithIdleOverlay@MINDCONTROL:
		Offset: 0,0,512
	ReplacedInQueue:
		Actors: htnk.drone, htnk.ion, htnk.hover
	Upgradeable@ION:
		Type: ionmam.upgrade
		UpgradingCondition: upgrading
		Actor: htnk.ion
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@HOVER:
		Type: hovermam.upgrade
		UpgradingCondition: upgrading
		Actor: htnk.hover
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@DRONE:
		Type: mdrone.upgrade
		UpgradingCondition: upgrading
		Actor: htnk.drone
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: GDI/Vehicles

HTNK.Ion:
	Inherits: HTNK
	Buildable:
		BuildPaletteOrder: 254
		Prerequisites: gtek, ~vehicles.htnk, ~ionmam.upgrade, ~techlevel.high
	Tooltip:
		Name: Ion Mammoth Tank
		GenericName: Tank
	Armament@PRIMARY:
		Weapon: IonZap
		LocalOffset: 870,0,360
	SpawnActorOnDeath:
		Actor: HTNK.Ion.Husk
	-ReplacedInQueue:
	-Upgradeable@ION:
	-Upgradeable@HOVER:
	-Upgradeable@DRONE:
	-WithDecoration@UpgradeOverlay:
	EncyclopediaExtras:
		Subfaction: zocom

HTNK.Hover:
	Inherits: HTNK
	Inherits@HOVERTRAIL: ^HoverTrail
	Tooltip:
		Name: Hover Mammoth Tank
		GenericName: Tank
	Buildable:
		BuildPaletteOrder: 255
		Prerequisites: gtek, ~vehicles.htnk, ~hovermam.upgrade, ~techlevel.high
		Description: Big and fairly mobile tank with anti-air capability.
	TooltipExtras:
		Attributes: • Can traverse water\n• Self repairs to 50% out of combat
	Mobile:
		Speed: 68
		Locomotor: hover
	Turreted:
		TurnSpeed: 20
	Hovers:
		BobDistance: -35
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	SpawnActorOnDeath:
		Actor: HTNK.Hover.Husk
		RequiresCondition: !being-warped && !onwater
	-ReplacedInQueue:
	-Upgradeable@ION:
	-Upgradeable@HOVER:
	-Upgradeable@DRONE:
	-WithDecoration@UpgradeOverlay:
	EncyclopediaExtras:
		Subfaction: eagle

HTNK.Drone:
	Inherits: HTNK
	Inherits@IDISABLE: ^DisabledByRadarLoss
	Inherits@HACKABLE: ^Hackable
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 256
		IconPalette: chrometd
		Prerequisites: ~mdrone.upgrade, gtek, ~vehicles.htnk, ~techlevel.high
		Description: Remotely piloted heavy tank with anti-air capability.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Requires active radar communication
		Attributes: • Can crush concrete walls\n• Self repairs out of combat\n• Immune to mind control
	Valued:
		Cost: 1500
	Tooltip:
		Name: Mammoth Drone
	Mobile:
		PauseOnCondition: !radarenabled || being-captured || empdisable || being-warped || driver-dead || notmobile
		Voice: Move
	Passenger:
		Voice: Move
	Armament@PRIMARY:
		Weapon: 130mmTD.Drone
	AttackTurreted:
		PauseOnCondition: !radarenabled || empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	SpawnActorOnDeath:
		Actor: HTNK.DRONE.Husk
	Voiced:
		VoiceSet: MamDroneVoice
		RequiresCondition: radarenabled && !empdisable
	Voiced@OFFLINE:
		VoiceSet: OfflineDroneVoice
		RequiresCondition: !radarenabled || empdisable
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@DriverKillImmune:
		TargetTypes: DriverKillImmune
	ChangesHealth:
		StartIfBelow: 100
		RequiresCondition: !mdrn-attached
	-SpeedMultiplier@BERSERK:
	-ReloadDelayMultiplier@BERSERK:
	-ExternalCondition@BERSERK:
	-Berserkable@BERSERK:
	-WithPalettedOverlay@BERSERK:
	-TimedConditionBar@BERSERK:
	-RejectsOrders@BERSERK:
	-GrantCondition@BERSERK:
	-ReplacedInQueue:
	-Upgradeable@ION:
	-Upgradeable@HOVER:
	-Upgradeable@DRONE:
	-WithDecoration@UpgradeOverlay:
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-warped
	EncyclopediaExtras:
		Subfaction: arc

MSAM:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeGround
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	RenderSprites:
		FactionImages:
			nod: msam.nod
			blackh: msam.nod
			legion: msam.nod
			marked: msam.nod
			shadow: msam.nod
	Valued:
		Cost: 1000
	Tooltip:
		Name: MLRS
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 190
		Prerequisites: anyradar, ~vehicles.msam, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Multiple Launch Rocket System. Long-range guided artillery with anti-air capability.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor
	Mobile:
		Speed: 72
		Voice: Move
	Passenger:
		Voice: Move
	Health:
		HP: 22000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 16
		Offset: -256,0,128
	AttackTurreted:
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	WithSpriteTurret:
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	GrantConditionOnPrerequisite@HSONIC:
		Condition: hypersonic-upgrade
		Prerequisites: hypersonic.upgrade
	GrantConditionOnPrerequisite@PERCUS:
		Condition: hammerhead-upgrade
		Prerequisites: hammerhead.upgrade
	GrantConditionOnPrerequisite@HAILST:
		Condition: hailstorm-upgrade
		Prerequisites: hailstorm.upgrade
	Armament@PRIMARY:
		Weapon: 227mm
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 100, -100
		RequiresCondition: !hypersonic-upgrade && !hammerhead-upgrade && !hailstorm-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARY:
		Name: secondary
		Weapon: 227mmAA
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 100, -100
		RequiresCondition: !hypersonic-upgrade && !hammerhead-upgrade && !hailstorm-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Armament@PRIMARYUPG1:
		Weapon: 227mm.Hypersonic
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 75, -75
		RequiresCondition: hypersonic-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARYUPG1:
		Name: secondary
		Weapon: 227mmAA.Hypersonic
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 75, -75
		RequiresCondition: hypersonic-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Armament@PRIMARYUPG2:
		Weapon: 227mm.Hammerhead
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 100, -100
		RequiresCondition: hammerhead-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARYUPG2:
		Name: secondary
		Weapon: 227mmAA.Hammerhead
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 100, -100
		RequiresCondition: hammerhead-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Armament@PRIMARYUPG3:
		Weapon: 227mm.Hailstorm
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 100, -100
		RequiresCondition: hailstorm-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARYUPG3:
		Name: secondary
		Weapon: 227mmAA.Hailstorm
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 100, -100
		RequiresCondition: hailstorm-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Voiced:
		VoiceSet: MsamVoice
	Encyclopedia:
		Category: GDI/Vehicles

MLRS:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	RenderSprites:
	Valued:
		Cost: 1350
	Tooltip:
		Name: SSM Launcher
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 270
		Prerequisites: tmpl, ~vehicles.mlrs, ~techlevel.high
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Long-range incendiary missile artillery.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft\n• Has difficulty hitting moving targets\n• Cannot fire while moving
	Mobile:
		Speed: 72
		PauseOnCondition: aiming || being-captured || empdisable || being-warped || driver-dead || notmobile
		BlockedCursor: move
	Health:
		HP: 15000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 8
		Offset: -128,0,128
		RealignDelay: 0
	AttackMove:
		AttackMoveCondition: attack-move
	Armament@PRIMARY:
		Weapon: HonestJohn
		LocalOffset: 0,171,0, 0,-171,0
		PauseOnCondition: moving
		ReloadingCondition: reloading
		RequiresCondition: !blacknapalm-upgrade
	Armament@PRIMARYUPG:
		Weapon: HonestJohn.UPG
		LocalOffset: 0,171,0, 0,-171,0
		PauseOnCondition: moving
		ReloadingCondition: reloading
		RequiresCondition: blacknapalm-upgrade
	Armament@TARGETTER:
		Name: targetter
		Weapon: HonestJohnTargeting
		PauseOnCondition: reloading
		RequiresCondition: attack-move || assault-move
	AmmoPool:
		Ammo: 2
		AmmoCondition: ammo
	AttackTurreted:
		Armaments: primary, targetter
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
		RequiresCondition: ammo > 1
	WithSpriteTurret@OneMissile:
		RequiresCondition: ammo == 1
		Sequence: turret1
	WithSpriteTurret@NoMissiles:
		RequiresCondition: !ammo
		Sequence: turret0
	GrantConditionOnAttack@AIMING:
		ArmamentNames: primary, targetter
		Condition: aiming
		RevokeDelay: 15
		RevokeAll: false
	GrantConditionOnMovement@MOVING:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: moving
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	ReloadAmmoPool@1:
		Delay: 120
		Count: 2
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	ProductionCostMultiplier@BlackhBonus:
		Multiplier: 90
		Prerequisites: player.blackh
	GrantConditionOnPrerequisite@UPG:
		Condition: blacknapalm-upgrade
		Prerequisites: blacknapalm.upgrade
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		RenderPreviewActor: mlrs.preview

MLRS.Preview:
	Inherits: ^PreviewDummy
	Inherits@TDPalette: ^TDPalette
	RenderSprites:
		Image: mlrs
	WithFacingSpriteBody:
	WithSpriteTurret:
		Sequence: turret
	Turreted:
		Offset: -128,0,128

STNK.Nod:
	Inherits: ^VehicleTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@BERSERK: ^Berserk
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	RenderSprites:
		Image: STNKTnod
	Valued:
		Cost: 1100
	Tooltip:
		Name: Stealth Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 202
		Prerequisites: anyradar, ~vehicles.nod, ~!hstk.upgrade, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Medium-range missile tank that can cloak.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Heavy Armor, Light Armor, Aircraft
		Weaknesses: • Weak vs Infantry
		Attributes: • Can be spotted by nearby infantry and defense structures
	Mobile:
		Speed: 126
		Voice: Move
	Health:
		HP: 20000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Cloak@NORMAL:
		InitialDelay: 75
		CloakDelay: 76
		CloakSound: trans1.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: !cloak-force-disabled && !being-warped && !empdisable && !driver-dead && !parachute
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Armament@PRIMARY:
		Weapon: StnkMissile
		LocalOffset: 213,43,128, 213,-43,128
		RequiresCondition: !tibcore-upgrade
	Armament@SECONDARY:
		Name: secondary
		Weapon: StnkMissileAA
		LocalOffset: 213,43,128, 213,-43,128
		RequiresCondition: !tibcore-upgrade
	Armament@PRIMARYUPG:
		Weapon: StnkMissile.TibCore
		LocalOffset: 213,150,128, 213,-150,128
		RequiresCondition: tibcore-upgrade
	Armament@SECONDARYUPG:
		Name: secondary
		Weapon: StnkMissileAA.TibCore
		LocalOffset: 213,150,128, 213,-150,128
		RequiresCondition: tibcore-upgrade
	Turreted:
		TurnSpeed: 40
		Offset: 0,0,10
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded || parachute
		PersistentTargeting: false
		Voice: Attack
	WithSpriteTurret:
		RequiresCondition: !tibcore-upgrade
	WithSpriteTurret@UPG:
		RequiresCondition: tibcore-upgrade
		Sequence: turret-upg
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnPrerequisite@UPG:
		Condition: tibcore-upgrade
		Prerequisites: tibcore.upgrade
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	DamageTypeDamageMultiplier@A2GPROTECTION:
		Modifier: 60
	ReplacedInQueue:
		Actors: hstk
	Upgradeable@HSTK:
		Type: hstk.upgrade
		UpgradingCondition: upgrading
		Actor: hstk
		UpgradeAtActors: fix, rep, srep
		RequiresCondition: !mindcontrolled
	Voiced:
		VoiceSet: STNKVoice
	Passenger:
		Voice: Move
	AttackMove:
		Voice: Attack
	Encyclopedia:
		Category: Nod/Vehicles

HSTK:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@BERSERK: ^Berserk
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	RenderSprites:
		Image: HSTK
	Valued:
		Cost: 1350
	Tooltip:
		Name: Heavy Stealth Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 202
		Prerequisites: anyradar, ~vehicles.nod, ~hstk.upgrade, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Medium-range missile heavy tank that can cloak.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Heavy Armor, Light Armor, Aircraft
		Weaknesses: • Weak vs Infantry
		Attributes: • Can be spotted by nearby infantry and defense structures
	Mobile:
		Speed: 100
		Voice: Move
	Health:
		HP: 25000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Cloak@NORMAL:
		InitialDelay: 89
		CloakDelay: 90
		CloakSound: trans1.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: !cloak-force-disabled && !being-warped && !empdisable && !driver-dead && !parachute
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Armament@PRIMARY:
		Weapon: HstkMissile
		LocalOffset: 213,160,128, 213,-160,128
		RequiresCondition: !tibcore-upgrade
	Armament@SECONDARY:
		Name: secondary
		Weapon: HstkMissileAA
		LocalOffset: 213,160,128, 213,-160,128
		RequiresCondition: !tibcore-upgrade
	Armament@PRIMARYUPG:
		Weapon: HstkMissile.TibCore
		LocalOffset: 213,160,128, 213,-160,128
		RequiresCondition: tibcore-upgrade
	Armament@SECONDARYUPG:
		Name: secondary
		Weapon: HstkMissileAA.TibCore
		LocalOffset: 213,160,128, 213,-160,128
		RequiresCondition: tibcore-upgrade
	Turreted:
		TurnSpeed: 32
		Offset: 0,0,0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded || parachute
		PersistentTargeting: false
		Voice: Attack
	WithSpriteTurret:
		RequiresCondition: !tibcore-upgrade
	WithSpriteTurret@UPG:
		RequiresCondition: tibcore-upgrade
		Sequence: turret-upg
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnPrerequisite@UPG:
		Condition: tibcore-upgrade
		Prerequisites: tibcore.upgrade
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	DamageTypeDamageMultiplier@A2GPROTECTION:
		Modifier: 60
	Voiced:
		VoiceSet: HSTKVoice
	Passenger:
		Voice: Move
	AttackMove:
		Voice: Attack
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		Subfaction: shadow

BIKE:
	Inherits: ^VehicleTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@BERSERK: ^Berserk
	RenderSprites:
	Valued:
		Cost: 500
	Tooltip:
		Name: Recon Bike
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 150
		Prerequisites: ~vehicles.nod, ~techlevel.low
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Fast scout vehicle armed with rockets.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Buildings
		Weaknesses: • Weak vs Infantry, Light Armor
	Mobile:
		TurnSpeed: 40
		Speed: 170
	Health:
		HP: 11000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: BikeRockets
		LocalOffset: -128, -170, 170, -128, 170, 170
		LocalYaw: 100, -100
		RequiresCondition: !tibcore-upgrade
	Armament@SECONDARY:
		Name: secondary
		Weapon: BikeRocketsAA
		LocalOffset: -128, -170, 170, -128, 170, 170
		LocalYaw: 100, -100
		RequiresCondition: !tibcore-upgrade
	Armament@PRIMARYUPG:
		Weapon: BikeRockets.TibCore
		LocalOffset: -128, -170, 170, -128, 170, 170
		LocalYaw: 100, -100
		RequiresCondition: tibcore-upgrade
	Armament@SECONDARYUPG:
		Name: secondary
		Weapon: BikeRocketsAA.TibCore
		LocalOffset: -128, -170, 170, -128, 170, 170
		LocalYaw: 100, -100
		RequiresCondition: tibcore-upgrade
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnPrerequisite@UPG:
		Condition: tibcore-upgrade
		Prerequisites: tibcore.upgrade
	GrantConditionOnPrerequisite@ZealBonus:
		Condition: zeal-covenant
		Prerequisites: zeal.covenant
	SpeedMultiplier@ZealBonus:
		Modifier: 115
		RequiresCondition: zeal-covenant
	WithFacingSpriteBody:
		RequiresCondition: !tibcore-upgrade
	WithFacingSpriteBody@UPG:
		Name: tibcore
		RequiresCondition: tibcore-upgrade
		Sequence: idle-upgrade
	Encyclopedia:
		Category: Nod/Vehicles

FTNK:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	RenderSprites:
	Valued:
		Cost: 800
	Tooltip:
		Name: Flame Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 200
		Prerequisites: anyradar, ~vehicles.ftnk, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Tank armed with dual short-range flamethrowers.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Buildings, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
	Mobile:
		Speed: 76
	Health:
		HP: 40000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: BigFlamerTD
		MuzzleSequence: muzzle
		MuzzlePalette: tdeffect
		LocalOffset: 682,128,42, 682,-128,42
	Armament@FF:
		Weapon: BigFlamerTDFF
		LocalOffset: 682,128,42, 682,-128,42
	WithMuzzleOverlay:
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded || parachute
		FacingTolerance: 0
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
		EmptyWeapon: BarrelExplode
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Encyclopedia:
		Category: Nod/Vehicles

HFTK:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlightlySlowedByCrushing
	RenderSprites:
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 201
		IconPalette: chrometd
		Prerequisites: anyradar, ~vehicles.blackh, ~techlevel.medium
		Description: Heavy tank armed with dual short-range flamethrowers.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Buildings, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
	Valued:
		Cost: 1100
	Tooltip:
		Name: Heavy Flame Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 44000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 60
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	AutoTarget:
		ScanRadius: 4
	Turreted:
		TurnSpeed: 20
	Armament@PRIMARY:
		Weapon: HeavyFlameTankFlamer
		MuzzleSequence: muzzle
		MuzzlePalette: tdeffect
		LocalOffset: 750,200,150, 750,-200,150
		RequiresCondition: !blacknapalm-upgrade
	Armament@PRIMARYUPG:
		Weapon: HeavyFlameTankFlamer.UPG
		MuzzleSequence: muzzle2
		MuzzlePalette: scrin-ignore-lighting-alpha85
		LocalOffset: 750,200,150, 750,-200,150
		RequiresCondition: blacknapalm-upgrade
	Armament@FF:
		Name: secondary
		Weapon: HeavyFlameTankFlamerFF
		LocalOffset: 750,200,150, 750,-200,150
		RequiresCondition: !blacknapalm-upgrade
	Armament@FFUPG:
		Name: secondary
		Weapon: HeavyFlameTankFlamerFF.UPG
		LocalOffset: 750,200,150, 750,-200,150
		RequiresCondition: blacknapalm-upgrade
	AttackTurreted:
		Voice: Attack
		RangeMargin: 0
		PauseOnCondition: empdisable || being-warped || blinded || parachute
	WithSpriteTurret:
	WithMuzzleOverlay:
	Voiced:
		VoiceSet: FlameTankVoice
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
		EmptyWeapon: BarrelExplode
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	AmbientSoundCA:
		SoundFiles: bigflamer-loop1.aud
		InitialSound: bigflamer-start1.aud
		FinalSound: bigflamer-end1.aud
		RequiresCondition: attacking
		InitialSoundLength: 24
	GrantConditionOnAttackCA:
		Condition: attacking
		RevokeDelay: 10
	GrantConditionOnPrerequisite@UPG:
		Condition: blacknapalm-upgrade
		Prerequisites: blacknapalm.upgrade
	GrantConditionOnAttack@BurstCount:
		Condition: burstcount
		RevokeAll: true
		RevokeDelay: 60
		MaximumInstances: 25
	FirepowerMultiplier@Frontload:
		Modifier: 125
		RequiresCondition: burstcount < 10
	FirepowerMultiplier@Backload:
		Modifier: 82
		RequiresCondition: burstcount >= 10
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		Subfaction: blackh

HARV.TD:
	Inherits: ^VehicleTD-NOUPG
	Inherits@HARVBALANCE: ^HarvesterBalancer
	Inherits@HARVDMGNOTIFY: ^HarvesterDamageNotification
	Inherits@SELECTION: ^SelectableEconomicUnit
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	RenderSprites:
		Image: harv2
	Valued:
		Cost: 1400
	Tooltip:
		Name: Harvester
		GenericName: Harvester
	Buildable:
		BuildPaletteOrder: 12
		Prerequisites: ~!sharv.upgrade, anyrefinery, ~vehicles.td, ~techlevel.low
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Collects Ore, Gems and Tiberium for processing.
	TooltipExtras:
		Weaknesses: • Unarmed\n• Resistant to mind control
	Selectable:
		Priority: 7
		DecorationBounds: 1536, 1536
	Harvester:
		Resources: Tiberium, BlueTiberium, BlackTiberium, Ore, Gems
		BaleUnloadDelay: 1
		BaleLoadDelay: 4
		SearchFromProcRadius: 15
		SearchFromHarvesterRadius: 8
		HarvestFacings: 8
		EmptyCondition: no-ore
	DockClientManager:
	WithStoresResourcesPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 6
		ResourceSequences:
			Ore: pip-yellow
			Gems: pip-red
			Tiberium: pip-green
			BlueTiberium: pip-blue
			BlackTiberium: pip-green
	Mobile:
		Speed: 72
		TurnSpeed: 24
		Locomotor: heavywheeled
	Health:
		HP: 75000
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 4c0
	SpawnActorOnDeath:
		Actor: HARV.TD.Husk
		RequiresCondition: !being-warped
	WithHarvestAnimation:
	WithDockingAnimationCA:
		RefineryTypes: proc.td
	GrantConditionOnTerrain@Tiberium:
		Condition: ontib
		TerrainTypes: Tiberium
	GrantConditionOnTerrain@Ore:
		Condition: onore
		TerrainTypes: Ore
	FireWarheadsOnDeath@Ore:
		RequiresCondition: !no-ore && onore && !being-warped
		Weapon: OreExplosion
	FireWarheadsOnDeath@Tib:
		RequiresCondition: !no-ore && ontib && !being-warped
		Weapon: TibExplosion
	-Crushable:
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
	SpawnRandomActorOnDeath:
		Actors: c1,c7,c10
		Probability: 5
		RequiresCondition: !being-warped
	GrantConditionOnPrerequisite@GSHIELD:
		Condition: pointdef-upgrade
		Prerequisites: pointdef.upgrade
	TimedDamageMultiplier@GSHIELD:
		ActiveCondition: gshield
		ChargingColor: 008888
		DrainingColor: 00ffff
		Modifier: 25
		Duration: 75
		ChargeTime: 375
		ActivateSound: gshieldup.aud
		DeactivateSound: gshielddown.aud
		RequiresCondition: pointdef-upgrade
	WithIdleOverlay@NSHIELD:
		RequiresCondition: (nshield || gshield) && !(empdisable || invulnerability || invisibility)
	ChangesHealth@MINIDRONE:
		Step: 2250
	DamageTypeDamageMultiplier@A2GPROTECTION:
		Modifier: 60
	UpdatesKillCount@BuildingsOrHarvesters:
		Type: BuildingsOrHarvesters
	GrantConditionOnPrerequisite@Stealth:
		Condition: sharv-upgrade
		Prerequisites: sharv.upgrade
	TransformOnCondition:
		RequiresCondition: sharv-upgrade
		IntoActor: harv.td.upg
	RejectsOrders@Transforming:
		RequiresCondition: sharv-upgrade
	TransferResourcesOnTransform:
	ReplacedInQueue:
		Actors: harv.td.upg
	StoresResources:
		Capacity: 26
		Resources: Tiberium, BlueTiberium, BlackTiberium, Ore, Gems
	Encyclopedia:
		Category: GDI/Vehicles; Nod/Vehicles

HARV.TD.UPG:
	Inherits: HARV.TD
	Buildable:
		Prerequisites: ~sharv.upgrade, anyrefinery, ~vehicles.td, ~techlevel.low
	RenderSprites:
		Image: harv2.upg
	Valued:
		Cost: 1400
	Health:
		HP: 65000
	Tooltip:
		Name: Stealth Harvester
	Cloak@NORMAL:
		InitialDelay: 74
		CloakDelay: 75
		CloakSound: trans1.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: !cloak-force-disabled && !being-warped && !empdisable && !driver-dead && !parachute
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	SpawnActorOnDeath:
		Actor: HARV.TD.UPG.Husk
	-GrantConditionOnPrerequisite@Stealth:
	-TransformOnCondition:
	-RejectsOrders@Transforming:
	-TransferResourcesOnTransform:
	-ReplacedInQueue:
	Encyclopedia:
		Category: Nod/Vehicles

BTR:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 30
		Prerequisites: ~vehicles.soviet, ~!gattling.upgrade, ~techlevel.low
		Description: Tough infantry transport.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Aircraft
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses
	Valued:
		Cost: 675
	Tooltip:
		Name: Armored Personnel Carrier (BTR)
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 25000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 100
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 40
	Armament@GROUND:
		Name: secondary
		Weapon: FLAK-23-AG
		LocalOffset: 592,0,376
		MuzzleSequence: muzzle
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	Armament@AIR:
		Weapon: FLAK-23-AA
		LocalOffset: 592,0,376
		MuzzleSequence: muzzle
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret@GROUND:
		Sequence: turret
		RequiresCondition: !reactive-upgrade
	WithSpriteTurret@GROUNDReactiveArmor:
		Sequence: turret-upg
		RequiresCondition: reactive-upgrade
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 5
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e7: loaded-cmdo
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			mast: loaded-cmdo
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	GrantConditionOnPrerequisite@ReactiveArmor:
		Prerequisites: reactive.upgrade
		Condition: reactive-upgrade
	DamageMultiplier@ReactiveArmor:
		Modifier: 90
		RequiresCondition: reactive-upgrade
	ReplacedInQueue:
		Actors: btr.yuri
	Upgradeable@GATTLING:
		Type: gattling.upgrade
		UpgradingCondition: upgrading
		Actor: btr.yuri
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Voiced:
		VoiceSet: BtrVoice
	Encyclopedia:
		Category: Soviets/Vehicles

BTR.AI:
	Inherits: BTR
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: BTR
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.soviet, ~techlevel.low
	Cargo:
		InitialUnits: E1,E1,E1,E3,E4
	WithCargoSounds:
		-EnterSounds:
	-Encyclopedia:

BTR.YURI:
	Inherits: BTR
	RenderSprites:
		Image: btry
	Valued:
		Cost: 750
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 31
		Prerequisites: ~gattling.upgrade, ~vehicles.soviet, ~techlevel.low
		Description: Tough infantry transport with armament improvements.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Aircraft
		Weaknesses: • Weak vs Heavy Armor, Buildings, Defenses
	Tooltip:
		Name: Armored Personnel Carrier (Gattling BTR)
	Armament@AIR:
		Weapon: MGatt
		LocalOffset: 320,50,350, 320,-50,350
		MuzzleSequence: muzzle
		PauseOnCondition: reload-ground
		ReloadingCondition: reload-air
		RequiresCondition: gattling < 12
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,225,200, 0,-225,200
		CasingTargetOffset: 0, 600, 0, 0, -600, 0
	Armament@AIR2:
		Weapon: MGatt
		LocalOffset: 320,50,350, 320,-50,350
		MuzzleSequence: muzzle-huge
		PauseOnCondition: reload-ground
		ReloadingCondition: reload-air
		RequiresCondition: gattling >= 12
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,225,200, 0,-225,200
		CasingTargetOffset: 0, 600, 0, 0, -600, 0
	Armament@GROUND:
		Name: secondary
		Weapon: MGattG
		LocalOffset: 421,50,165, 421,-50,165
		MuzzleSequence: muzzle
		PauseOnCondition: reload-air
		ReloadingCondition: reload-ground
		RequiresCondition: gattling-ground < 14
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,225,200, 0,-225,200
		CasingTargetOffset: 0, 600, 0, 0, -600, 0
	Armament@GROUND2:
		Name: secondary
		Weapon: MGattG
		LocalOffset: 421,50,165, 421,-50,165
		MuzzleSequence: muzzle-huge
		PauseOnCondition: reload-air
		ReloadingCondition: reload-ground
		RequiresCondition: gattling-ground >= 14
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,225,200, 0,-225,200
		CasingTargetOffset: 0, 600, 0, 0, -600, 0
	FirepowerMultiplier@GAT1:
		Modifier: 115
		RequiresCondition: (attacking-air && gattling >= 1 && gattling < 3) || (attacking-ground && gattling-ground >= 1 && gattling-ground < 5)
	FirepowerMultiplier@GAT2:
		Modifier: 130
		RequiresCondition: (attacking-air && gattling >= 3 && gattling < 7) || (attacking-ground && (gattling-ground >= 5 && (gattling-ground < 10 || !rank-elite)))
	FirepowerMultiplier@GAT3:
		Modifier: 145
		RequiresCondition: (attacking-air && gattling >= 7 || (attacking-ground && (gattling-ground >= 10 && rank-elite)))
	AmbientSoundCA@ATTACKSOUNDINITIAL:
		SoundFiles: vgatlo1a.aud
		RequiresCondition: (attacking-air && gattling < 2) || (attacking-ground && gattling-ground < 2)
	AmbientSoundCA@ATTACKSOUND1:
		SoundFiles: vgatlo2a.aud, vgatlo2b.aud, vgatlo2c.aud
		FinalSound: vgatlo3a.aud
		RequiresCondition: (attacking-air && gattling >= 2 && gattling < 6) || (attacking-ground && gattling-ground >= 2 && gattling-ground < 6)
	AmbientSoundCA@ATTACKSOUND2:
		InitialSound: vgatlo4a.aud
		SoundFiles: vgatlo5a.aud, vgatlo5b.aud
		FinalSound: vgatlo6a.aud
		RequiresCondition: (attacking-air && gattling >= 6 && gattling < 12) || (attacking-ground && (gattling-ground >= 6 && (gattling-ground < 14 || !rank-elite)))
	AmbientSoundCA@ATTACKSOUND3:
		InitialSound: vgatlo7a.aud
		SoundFiles: vgatlo8a.aud, vgatlo8b.aud
		FinalSound: vgatlo9a.aud
		RequiresCondition: (attacking-air && gattling >= 12 || (attacking-ground && (gattling-ground >= 14 && rank-elite)))
	GrantConditionOnAttackCA@ATTACKING-AIR:
		ArmamentNames: primary
		Condition: attacking-air
		RevokeDelay: 6
	GrantConditionOnAttackCA@ATTACKING-GROUND:
		ArmamentNames: secondary
		Condition: attacking-ground
		RevokeDelay: 6
	GrantConditionOnAttack:
		Condition: gattling
		MaximumInstances: 12
		RevokeDelay: 40
		RevokeOnNewTarget: False
		RevokeAll: True
		RequiresCondition: !attacking-ground
	GrantConditionOnAttack@GROUND:
		ArmamentNames: secondary
		Condition: gattling-ground
		MaximumInstances: 14
		RevokeDelay: 40
		RevokeOnNewTarget: False
		RevokeAll: True
		RequiresCondition: !attacking-air
	WithSpriteTurret@AIR:
		RequiresCondition: !attacking-ground && !reactive-upgrade
	WithSpriteTurret@GROUND:
		Sequence: turret-ground
		RequiresCondition: attacking-ground && !reactive-upgrade
	WithSpriteTurret@AIRReactiveArmor:
		Sequence: turret-air-upg
		RequiresCondition: !attacking-ground && reactive-upgrade
	WithSpriteTurret@GROUNDReactiveArmor:
		Sequence: turret-ground-upg
		RequiresCondition: attacking-ground && reactive-upgrade
	Turreted:
		TurnSpeed: 28
		Offset: -50,0,0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary, secondary
		Voice: Attack
	Mobile:
		Voice: Move
	Passenger:
		Voice: Move
	Voiced:
		VoiceSet: GattlingTankVoice
	-Upgradeable@GATTLING:
	-WithDecoration@UpgradeOverlay:
	EncyclopediaExtras:
		Subfaction: yuri

BTR.YURI.AI:
	Inherits: BTR.YURI
	Inherits@AIUNLOAD: ^AIUNLOAD
	RenderSprites:
		Image: btry
	Buildable:
		Prerequisites: ~botplayer, ~vehicles.soviet, ~gattling.upgrade, ~techlevel.low
	Cargo:
		InitialUnits: E1,E1,E1,E3,E4
	WithCargoSounds:
		-EnterSounds:
	-Encyclopedia:
	-EncyclopediaExtras:

TRPC:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Inherits@AIUNLOAD: ^AIUNLOAD
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 132
		Prerequisites: ~vehicles.soviet, ~infantry.doctrine, ~techlevel.medium
		Description: Tough infantry transport pre-loaded with infantry.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Empowers nearby basic infantry\n• Heals all nearby infantry\n• Detects cloaked units and mines
	Valued:
		Cost: 1500
	Tooltip:
		Name: Troop Crawler
	UpdatesPlayerStatistics:
		AddToArmyValue: false
		AddToAssetsValue: false
	Health:
		HP: 36000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 64
		Voice: Move
	Passenger:
		Voice: Move
		CargoCondition: passenger
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Cargo:
		Types: Infantry, Hacker
		UnloadVoice: Unload
		MaxWeight: 8
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e7: loaded-cmdo
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			mast: loaded-cmdo
		InitialUnits: E1,E1,E1,E1,E1,E1,E3,E3
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
		RequiresCondition: !no-cargo-sounds
	GrantTimedCondition@NoPassengerSound:
		Duration: 10
		Condition: no-cargo-sounds
	CargoBlocked:
		RequiresCondition: mindcontrolled
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	KeepsDistance:
	AttackFrontal:
	AttackMove:
	AutoTarget:
		ScanRadius: 3
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry
		ValidRelationships: Ally
	Armament:
		Weapon: TroopCrawlerDummyWeapon
		TargetRelationships: Ally
		ForceTargetRelationships: None
		Cursor: guard
		OutsideRangeCursor: guard
	ProximityExternalCondition@InspirationTrpc:
		Range: 4c0
		Condition: trpc-inspiration
		ValidRelationships: Ally
		RequiresCondition: !passenger && !inspiration-delay
	GrantTimedCondition@InspirationDelay:
		Duration: 10
		Condition: inspiration-delay
	WithRadiatingCircle:
		EndRadius: 4c0
		Interval: 75
		Duration: 50
		ValidRelationships: Ally
		AlwaysShowMaxRange: true
	ProximityExternalCondition@HealAura:
		Condition: hospitalheal
		Range: 4c0
		RequiresCondition: !passenger
	GivesExperienceCA:
		ActorExperienceModifier: 2000
		PlayerExperienceModifier: 0
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: !(empdisable || being-warped)
	DetectCloaked@Mines:
		Range: 4c0
		DetectionTypes: Mine
		RequiresCondition: !(empdisable || being-warped)
	GivesBountyCA:
		Percentage: 2
	Voiced:
		VoiceSet: TrpcVoice
	ExternalCondition@InspirationCmsr:
		Condition: cmsr-inspiration
	ExternalCondition@InspirationOvld:
		Condition: ovld-inspiration
	ExternalCondition@InspirationTrpc:
		Condition: trpc-inspiration
	SpeedMultiplier@InspirationCmsr:
		Modifier: 112
		RequiresCondition: cmsr-inspiration || ovld-inspiration || trpc-inspiration
	Sellable:
		RefundPercent: 10
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Infantry Doctrine.

IFV:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 170
		Prerequisites: radarorrepair, ~vehicles.allies, ~techlevel.medium
		Description: Adaptable infantry transport.
	Tooltip:
		Name: Infantry Fighting Vehicle
		RequiresCondition: !full || nochange
	Tooltip@gunturr:
		Name: MG IFV
		RequiresCondition: gunturr
	Tooltip@fragturr:
		Name: Grenade IFV
		RequiresCondition: fragturr
	Tooltip@samturr:
		Name: Missile IFV
		RequiresCondition: samturr
	Tooltip@engturr:
		Name: Engineer IFV
		RequiresCondition: engturr
	Tooltip@mechturr:
		Name: Repair IFV
		RequiresCondition: mechturr
	Tooltip@commturr:
		Name: Commando IFV
		RequiresCondition: commturr
	Tooltip@chemturr:
		Name: Chemical IFV
		RequiresCondition: chemturr
	Tooltip@medturr:
		Name: Medical IFV
		RequiresCondition: medturr
	Tooltip@ivanturr:
		Name: Explosive IFV
		RequiresCondition: ivanturr
	Tooltip@flamturr:
		Name: Flamethrower IFV
		RequiresCondition: flamturr
	Tooltip@spyturr:
		Name: Spy IFV
		RequiresCondition: spyturr
	Tooltip@sealturr:
		Name: SEAL IFV
		RequiresCondition: sealturr
	Tooltip@acolturr:
		Name: Laser IFV
		RequiresCondition: acolturr
	Tooltip@snipturr:
		Name: Sniper IFV
		RequiresCondition: snipturr
	Tooltip@desoturr:
		Name: Desolator IFV
		RequiresCondition: desoturr
	Tooltip@testurr:
		Name: Tesla IFV
		RequiresCondition: testurr
	Tooltip@rmbcturr:
		Name: Cyborg Elite IFV
		RequiresCondition: rmbcturr
	Tooltip@enliturr:
		Name: Enlightened IFV
		RequiresCondition: enliturr
	Tooltip@bhturr:
		Name: Black Hand IFV
		RequiresCondition: bhturr
	Tooltip@mortchemturr:
		Name: Chemical Mortar IFV
		RequiresCondition: mortchemturr
	Tooltip@mortcryoturr:
		Name: Cryo Mortar IFV
		RequiresCondition: mortcryoturr
	Tooltip@mortsonicturr:
		Name: Sonic Mortar IFV
		RequiresCondition: mortsonicturr
	Tooltip@discturr:
		Name: Plasma Disc IFV
		RequiresCondition: discturr
	Tooltip@sharturr:
		Name: Shard IFV
		RequiresCondition: sharturr
	Tooltip@ggiturr:
		Name: GGI IFV
		RequiresCondition: ggiturr
	Tooltip@psyturr:
		Name: Psychic IFV
		RequiresCondition: psyturr
	Tooltip@cryoturr:
		Name: Cryo IFV
		RequiresCondition: cryoturr
	Tooltip@enfoturr:
		Name: Enforcer IFV
		RequiresCondition: enfoturr
	Tooltip@ztrpturr:
		Name: Zone Trooper IFV
		RequiresCondition: ztrpturr
	Tooltip@zdefturr:
		Name: Zone Defender IFV
		RequiresCondition: zdefturr
	Tooltip@zraiturr:
		Name: Zone Raider IFV
		RequiresCondition: zraiturr
	Tooltip@tigrturr:
		Name: Tiger Guard IFV
		RequiresCondition: tigrturr
	Tooltip@disinturr:
		Name: Disintegrator IFV
		RequiresCondition: disinturr
	Tooltip@implturr:
		Name: Impaler IFV
		RequiresCondition: implturr
	Tooltip@stlkturr:
		Name: Stalker IFV
		RequiresCondition: stlkturr
	Tooltip@hoplturr:
		Name: Hoplite IFV
		RequiresCondition: hoplturr
	Tooltip@cmsrturr:
		Name: Commissar IFV
		RequiresCondition: cmsrturr
	Tooltip@shadturr:
		Name: Shadow IFV
		RequiresCondition: shadturr
	Tooltip@hackturr:
		Name: Hacker IFV
		RequiresCondition: hackturr
	Tooltip@confturr:
		Name: Confessor IFV
		RequiresCondition: confturr
	Tooltip@reapturr:
		Name: Reaper IFV
		RequiresCondition: reapturr
	TooltipExtras:
		Strengths: • Strong vs Aircraft, Heavy Armor
		Weaknesses: • Weak vs Infantry
		Attributes: • Weapon/function changes depending on infantry passenger
		RequiresCondition: !full || samturr || ggiturr || disinturr || tigrturr || nochange
	TooltipExtras@gunturr:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: gunturr || enfoturr || shadturr
	TooltipExtras@fragturr:
		Strengths: • Strong vs Buildings, Defenses, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: fragturr
	TooltipExtras@mechturr:
		Weaknesses: • Unarmed
		Attributes: • Repairs friendly vehicles
		IsStandard: false
		RequiresCondition: mechturr
	TooltipExtras@engturr:
		Weaknesses: • Unarmed
		Attributes: • Captures structures
		IsStandard: false
		RequiresCondition: engturr
	TooltipExtras@commturr:
		Strengths: • Strong vs Infantry
		Weaknesses: • Cannot attack Aircraft, Vehicles, Buildings
		IsStandard: false
		RequiresCondition: commturr || sealturr
	TooltipExtras@medturr:
		Weaknesses: • Unarmed
		Attributes: • Heals friendly infantry
		IsStandard: false
		RequiresCondition: medturr
	TooltipExtras@ivanturr:
		Strengths: • Strong vs Everything
		Weaknesses: • Self-destructs
		IsStandard: false
		RequiresCondition: ivanturr
	TooltipExtras@flamturr:
		Strengths: • Strong vs Infantry, Light Armor, Buildings
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: flamturr || chemturr || hoplturr
	TooltipExtras@spyturr:
		Weaknesses: • Cannot deal damage
		Attributes: • Long vision range\n• Can detect cloaked units\n• Marks vehicle/structure targets, revealing them and increasing the damage they take
		IsStandard: false
		RequiresCondition: spyturr
	TooltipExtras@rmbcturr:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: rmbcturr
	TooltipExtras@snipturr:
		Strengths: • Strong vs Infantry, Light Armor, Heavy Armor
		Weaknesses: • Weak vs Defenses, Buildings\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: snipturr
	TooltipExtras@desoturr:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Defenses, Buildings\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: desoturr || acolturr || sharturr || implturr || stlkturr
	TooltipExtras@testurr:
		Strengths: • Strong vs Infantry, Heavy Armor, Light Armor
		Weaknesses: • Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: testurr
	TooltipExtras@bhturr:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Weak vs Buildings, Defenses\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: bhturr || enliturr
	TooltipExtras@ztrpturr:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses
		Weaknesses: • Weak vs Infantry, Buildings\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: ztrpturr || zdefturr
	TooltipExtras@zraiturr:
		Strengths: • Strong vs Light Armor, Buildings
		Weaknesses: • Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: zraiturr
	TooltipExtras@psyturr:
		Strengths: • Strong vs Infantry, Vehicles
		Weaknesses: • Cannot deal damage\n• Cannot attack Aircraft
		Attributes: • Can mind control enemy units\n• Control lost if passenger exits
		IsStandard: false
		RequiresCondition: psyturr
	TooltipExtras@hackturr:
		Strengths: • Strong vs Defenses, Buildings
		Weaknesses: • Cannot deal damage\n• Control lost if passenger exits
		IsStandard: false
		RequiresCondition: hackturr
	TooltipExtras@cryoturr:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Buildings, Defenses\n• Cannot attack Aircraft
		Attributes: • Slows enemy units and makes them take increased damage
		IsStandard: false
		RequiresCondition: cryoturr
	TooltipExtras@reapturr:
		Strengths: • Strong vs Infantry, Light Armor, Buildings
		Weaknesses: • Weak vs Heavy Armor
		IsStandard: false
		RequiresCondition: reapturr
	TooltipExtras@confturr:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Defenses, Buildings\n• Cannot attack Aircraft
		Attributes: • Explosion causes units to attack indiscriminately
		IsStandard: false
		RequiresCondition: confturr
	Valued:
		Cost: 750
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 27000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 100
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
		RequiresCondition: !spyturr
	RevealsShroud@GAPGEN:
		Range: 4c0
	RevealsShroud@SPY:
		Range: 7c0
		MinRange: 4c0
		RevealGeneratedShroud: False
		RequiresCondition: spyturr
	DetectCloaked@SPY:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: spyturr && !(empdisable || being-warped)
	Turreted:
		TurnSpeed: 40
		Offset: 20,0,0
	Armament@Default:
		Weapon: IFVRockets
		LocalOffset: 192,50,176, 192,-50,176
		RequiresCondition: !full || nochange
	Armament@DefaultAA:
		Name: secondary
		Weapon: IFVRocketsAA
		LocalOffset: 192,50,176, 192,-50,176
		RequiresCondition: !full || nochange
	Armament@E1:
		Weapon: M60mgIFV
		MuzzleSequence: muzzle
		LocalOffset: 250,0,150
		RequiresCondition: gunturr || shadturr || cmsrturr
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-125,200
		CasingTargetOffset: 0, -500, 0
	Armament@E2:
		Weapon: 25mmFRAG
		MuzzleSequence: muzzle
		LocalOffset: 450,0,200
		RequiresCondition: fragturr
	Armament@E3:
		Weapon: IFVRocketsE
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: (samturr || ggiturr) && !cryw-upgrade && !tibcore-upgrade
	Armament@E3AA:
		Name: secondary
		Weapon: IFVRocketsAAE
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: (samturr || ggiturr) && !cryw-upgrade && !tibcore-upgrade
	Armament@E3CRYO:
		Weapon: IFVRocketsE.CRYO
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: (samturr || ggiturr) && cryw-upgrade
	Armament@E3CRYOAA:
		Name: secondary
		Weapon: IFVRocketsAAE.CRYO
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: (samturr || ggiturr) && cryw-upgrade
	Armament@E3TIB:
		Weapon: IFVRocketsE.TibCore
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: (samturr || ggiturr) && tibcore-upgrade && !cryw-upgrade
	Armament@E3TIBAA:
		Name: secondary
		Weapon: IFVRocketsAAE.TibCore
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: (samturr || ggiturr) && tibcore-upgrade && !cryw-upgrade
	Armament@E4:
		Weapon: FireballLauncher
		Recoil: 85
		RecoilRecovery: 25
		LocalOffset: 350,0,150
		MuzzleSequence: muzzle2
		RequiresCondition: flamturr
	Armament@E5:
		Weapon: ChemballLauncher
		Recoil: 85
		RecoilRecovery: 25
		LocalOffset: 350,0,150
		MuzzleSequence: muzzle2
		RequiresCondition: chemturr
	Armament@E7:
		Weapon: CommandoIFVGun
		MuzzleSequence: muzzle
		LocalOffset: 560,0,200
		RequiresCondition: commturr
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	Armament@SEAL:
		Weapon: SealIFVGun
		MuzzleSequence: muzzle
		LocalOffset: 560,0,200
		RequiresCondition: sealturr
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	Armament@Sniper:
		Weapon: SniperIFVGun
		MuzzleSequence: muzzle
		LocalOffset: 560,0,200
		RequiresCondition: snipturr && !apb-upgrade
	Armament@SniperUpg:
		Weapon: SniperIFVGun.UPG
		MuzzleSequence: muzzle
		LocalOffset: 560,0,200
		RequiresCondition: snipturr && apb-upgrade
	Armament@Shok:
		Weapon: TTankZap
		LocalOffset: 0,0,213
		RequiresCondition: testurr
	Armament@Deso:
		Weapon: DesolatorBeam
		LocalOffset: 400,0,213
		RequiresCondition: desoturr
	Armament@ENLI:
		Weapon: EnlightenedBeam
		MuzzleSequence: muzzle
		LocalOffset: 192,100,200, 192,-100,200
		RequiresCondition: enliturr
	Armament@RMBC:
		Weapon: CyCannon
		MuzzleSequence: muzzle
		LocalOffset: 192,100,200, 192,-100,200
		RequiresCondition: rmbcturr
	Armament@BH:
		Weapon: BlackHandFlamer
		LocalOffset: 150,0,150
		RequiresCondition: bhturr
	Armament@Heal:
		Weapon: Heal
		Cursor: heal
		OutsideRangeCursor: heal
		TargetRelationships: Ally
		ForceTargetRelationships: None
		RequiresCondition: medturr
	Armament@Repair:
		Weapon: RepairIFV
		LocalOffset: 0,0,213
		Cursor: repair
		OutsideRangeCursor: repair
		TargetRelationships: Ally
		ForceTargetRelationships: None
		RequiresCondition: mechturr
		MuzzleSequence: emp-overlay
		MuzzlePalette: tseffect-ignore-lighting-alpha75
	Armament@defuse:
		Weapon: IFVDefuseKit
		Cursor: goldwrench
		OutsideRangeCursor: goldwrench
		TargetRelationships: Ally
		RequiresCondition: engturr
		ForceTargetRelationships: None
		Name: secondary
	Armament@Acol:
		Weapon: IFVLaser
		LocalOffset: 385,0,300
		RequiresCondition: acolturr
	Armament@Disc:
		Weapon: IntruderDiscs
		LocalOffset: 250,0,300
		RequiresCondition: discturr
	Armament@Shard:
		Weapon: ShardWalkerShards
		LocalOffset: 300,0,300
		RequiresCondition: sharturr
	Armament@ChemMortar:
		Weapon: ChemicalMortar
		LocalOffset: 300,0,400
		RequiresCondition: mortchemturr
	Armament@CryoMortar:
		Weapon: CryoMortar
		LocalOffset: 300,0,400
		RequiresCondition: mortcryoturr
	Armament@SonicMortar:
		Weapon: SonicMortar
		LocalOffset: 300,0,400
		RequiresCondition: mortsonicturr
	Armament@IVAN:
		Weapon: IvanIFVTargeting
		RequiresCondition: ivanturr
	Armament@CryoTrooper:
		Weapon: CryoSprayer
		LocalOffset: 150,0,150
		RequiresCondition: cryoturr
	Armament@Enforcer:
		Weapon: EnforcerIFVShotgun
		LocalOffset: 385,0,300
		RequiresCondition: enfoturr
		MuzzleSequence: muzzle
	Armament@EnforcerLine:
		Weapon: EnforcerIFVShotgunLine
		LocalOffset: 385,0,300
		RequiresCondition: enfoturr
	Armament@Disintegrator:
		Weapon: DisintegratorIFVBeam
		LocalOffset: 350,0,250
		RequiresCondition: disinturr
	Armament@DisintegratorAA:
		Weapon: DisintegratorIFVBeamAA
		LocalOffset: 350,0,250
		RequiresCondition: disinturr
	Armament@Hoplite:
		Weapon: HopliteIFVGun
		LocalOffset: 100,0,300
		RequiresCondition: hoplturr
	Armament@ZoneTrooper:
		Weapon: ZoneTrooperRailgun
		LocalOffset: 350,0,250
		RequiresCondition: ztrpturr
	Armament@ZoneDefender:
		Weapon: ZoneDefenderGun
		LocalOffset: 350,0,250
		RequiresCondition: zdefturr
	Armament@ZoneRaider:
		Weapon: ZoneRaiderGrenade
		LocalOffset: 350,0,250
		RequiresCondition: zraiturr
	Armament@TigerGuard:
		Weapon: TigerGuardMissile
		LocalOffset: 192,100,256, 192,-100,256
		RequiresCondition: tigrturr
	Armament@TigerGuardMissileAA:
		Weapon: TigerGuardMissileAA
		LocalOffset: 192,100,256, 192,-100,256
		RequiresCondition: tigrturr
	Armament@Impaler:
		Weapon: ImpalerSpike
		LocalOffset: 450,0,250
		RequiresCondition: implturr
	Armament@Stalker:
		Weapon: StalkerShards
		LocalOffset: 350,0,300
		RequiresCondition: stlkturr
	Armament@Confessor:
		Weapon: HallucinationGrenade
		LocalOffset: 300,0,400
		RequiresCondition: confturr
	Armament@Reaper:
		Weapon: CyborgReaperMissiles
		LocalOffset: 192,100,256, 192,-100,256
		RequiresCondition: reapturr
	Armament@Spy:
		Weapon: TargetPainter
		RequiresCondition: spyturr
		LocalOffset: 0,0,100
	AutoTargetPriority@Spy:
		ValidTargets: Vehicle, Structure
		RequiresCondition: spyturr
	AttackSoundsCA@REPAIRSOUND:
		Sounds: repair11.aud
		RequiresCondition: mechturr
		Armaments: primary
	WithIdleOverlay@SPINNER:
		Sequence: spinner
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: spyturr
		Offset: 0,0,200
	WithIdleOverlay@TESLA:
		Sequence: tesla
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: testurr
	WithIdleOverlay@PSY:
		Sequence: psy
		RequiresCondition: psyturr
		Palette: scrin
	WithIdleOverlay@HACK:
		Sequence: hack
		RequiresCondition: hackturr
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		TargetFrozenActors: True
	WithMuzzleOverlay:
	WithSpriteTurret@defaultturr:
		RequiresCondition: !full || nochange
		Sequence: turret
	WithSpriteTurret@samturr:
		RequiresCondition: samturr
		Sequence: turret-rocket
	WithSpriteTurret@ggiturr:
		RequiresCondition: ggiturr
		Sequence: turret-ggi
	WithSpriteTurret@gun:
		RequiresCondition: gunturr || shadturr || cmsrturr
		Sequence: turret-mg
	WithSpriteTurret@frg:
		Sequence: turret-frag
		RequiresCondition: fragturr
	WithSpriteTurret@fla:
		Sequence: turret-flame
		RequiresCondition: flamturr
	WithSpriteTurret@che:
		Sequence: turret-chem
		RequiresCondition: chemturr
	WithSpriteTurret@las:
		Sequence: turret-laser
		RequiresCondition: acolturr
	WithSpriteTurret@des:
		Sequence: turret-rad
		RequiresCondition: desoturr
	WithSpriteTurret@com:
		Sequence: turret-cmdo
		RequiresCondition: commturr || sealturr
	WithSpriteTurret@mech:
		Sequence: turret-mech
		RequiresCondition: mechturr
	WithSpriteTurret@snip:
		Sequence: turret-snip
		RequiresCondition: snipturr
	WithSpriteTurret@enli:
		Sequence: turret-enli
		RequiresCondition: enliturr
	WithSpriteTurret@rmbc:
		Sequence: turret-rmbc
		RequiresCondition: rmbcturr
	WithSpriteTurret@bh:
		Sequence: turret-bh
		RequiresCondition: bhturr
	WithSpriteTurret@disc:
		Sequence: turret-pdisc
		RequiresCondition: discturr
	WithSpriteTurret@shar:
		Sequence: turret-shard
		RequiresCondition: sharturr
	WithSpriteTurret@mort:
		Sequence: turret-mort
		RequiresCondition: mortchemturr || mortcryoturr || mortsonicturr
	WithSpriteTurret@cryo:
		Sequence: turret-cryo
		RequiresCondition: cryoturr
	WithSpriteTurret@enfo:
		Sequence: turret-enfo
		RequiresCondition: enfoturr
	WithSpriteTurret@engi:
		Sequence: turret-engi
		RequiresCondition: engturr
	WithSpriteTurret@ztrp:
		Sequence: turret-ztrp
		RequiresCondition: ztrpturr
	WithSpriteTurret@zdef:
		Sequence: turret-zdef
		RequiresCondition: zdefturr
	WithSpriteTurret@zrai:
		Sequence: turret-zrai
		RequiresCondition: zraiturr
	WithSpriteTurret@tigr:
		Sequence: turret-tigr
		RequiresCondition: tigrturr
	WithSpriteTurret@hopl:
		Sequence: turret-hopl
		RequiresCondition: hoplturr
	WithSpriteTurret@impl:
		Sequence: turret-impl
		RequiresCondition: implturr
	WithSpriteTurret@stlk:
		Sequence: turret-stlk
		RequiresCondition: stlkturr
		Palette: playerscrin
	WithSpriteTurret@disin:
		Sequence: turret-disin
		RequiresCondition: disinturr
	WithSpriteTurret@conf:
		Sequence: turret-conf
		RequiresCondition: confturr
	WithSpriteTurret@reap:
		Sequence: turret-reap
		RequiresCondition: reapturr
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 1
		LoadingCondition: notmobile
		LoadedCondition: full
		PassengerConditions:
			e1: gunturr
			n1: gunturr
			n1c: gunturr
			s1: gunturr
			e2: fragturr
			n2: fragturr
			e3: samturr
			n3: samturr
			n3c: samturr
			e4: flamturr
			n4: flamturr
			u3: ggiturr
			u3r2: ggiturr
			n5: chemturr
			e8: desoturr
			deso: desoturr
			e6: engturr
			n6: engturr
			s6: engturr
			mech: mechturr
			cmec: mechturr
			arti: mechturr
			mort.chem: mortchemturr
			mort.cryo: mortcryoturr
			mort.sonic: mortsonicturr
			shok: testurr
			ttrp: testurr
			ztrp: ztrpturr
			zrai: zraiturr
			zdef: zdefturr
			rmbo: commturr
			bori: commturr
			e7: commturr
			cryt: cryoturr
			enfo: enfoturr
			hopl: hoplturr
			tigr: tigrturr
			seal: sealturr
			snip: snipturr
			assa: snipturr
			hack: hackturr
			conf: confturr
			acol: acolturr
			tplr: acolturr
			cscr: acolturr
			reap: reapturr
			enli: enliturr
			rmbc: rmbcturr
			s2: sharturr
			evis: sharturr
			impl: implturr
			stlk: stlkturr
			s3: disinturr
			s4: discturr
			mrdr: discturr
			medi: medturr
			smedi: medturr
			mast: psyturr
			yuri: psyturr
			bh: bhturr
			shad: shadturr
			sab: shadturr
			cmsr: cmsrturr
			brst: ivanturr
			tdog: ivanturr
			ivan: ivanturr
			spy: spyturr
			thf: spyturr
			dog: spyturr
			cdog: spyturr
			wchr: spyturr
			tecn: nochange
			c1: nochange
			c2: nochange
			c3: nochange
			c4: nochange
			c5: nochange
			c6: nochange
			c7: nochange
			c8: nochange
			c9: nochange
			c10: nochange
	WithCargoSounds:
		EnterSounds: vifvtran.aud
		ExitSounds: vifvtran.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	WithIdleOverlay@MEDIC:
		Sequence: medic
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: medturr
		Offset: 0,0,40
	WithIdleOverlay@IVAN:
		Sequence: nuke
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: ivanturr
		Offset: 0,0,40
	FireWarheadsOnDeath@IVAN:
		Weapon: MicroNuke
		EmptyWeapon: MicroNuke
		DamageSource: Killer
		RequiresCondition: ivanturr
	GrantConditionOnAttack@IVAN:
		Condition: triggered
		RequiresCondition: ivanturr
	DamageMultiplier@IRONCURTAIN:
		RequiresCondition: invulnerability && !ivanturr
	KillsSelf:
		RequiresCondition: ivanturr && (invulnerability || tibstealth || triggered || berserk)
	Passenger:
		CargoCondition: passenger
	ProximityExternalCondition@MEDIC:
		RequiresCondition: medturr && !passenger
		EnableSound: heal2.aud
		Condition: hospitalheal
		Range: 3c512
	WithRangeCircle@MEDIC:
		Type: IFVHeal
		Color: 00FF0080
		Range: 3c512
		RequiresCondition: medturr
	Capturable:
		RequiresCondition: !being-warped && !full
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !full
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
		RequiresCondition: confturr || psyturr || commturr || hackturr
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: full
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: commturr || psyturr
	GrantCondition@IsAA:
		Condition: is-aa
		RequiresCondition: !full || samturr || ggiturr || disinturr || tigrturr || reapturr || nochange
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AutoTargetPriority@DEFAULTAA:
		ValidTargets: Infantry, Vehicle, Water, Underwater, Air, AirSmall, Defense
		InvalidTargets: NoAutoTarget, WaterStructure, AntiAirDefense
		RequiresCondition: (!stance-attackanything && !assault-move) && is-aa
	AutoTargetPriority@ATTACKANYTHINGAA:
		ValidTargets: Infantry, Vehicle, Water, Underwater, Air, AirSmall, Structure, Defense
		InvalidTargets: NoAutoTarget
		RequiresCondition: (stance-attackanything || assault-move) && is-aa
	AutoTargetPriority@DEFAULTAAPRIO:
		RequiresCondition: (!stance-attackanything && !assault-move) && is-aa
		ValidTargets: Air, AirSmall
		InvalidTargets: NoAutoTarget
		Priority: 10
	AutoTargetPriority@ATTACKANYTHINGAAPRIO:
		RequiresCondition: (!stance-attackanything && !assault-move) && is-aa
		ValidTargets: Air, AirSmall
		InvalidTargets: NoAutoTarget
		Priority: 10
	AutoTargetPriority@DEFAULTG:
		RequiresCondition: !maxcontrolled && !stance-attackanything && !assault-move && !is-aa
		ValidTargets: Infantry, Vehicle, Water, Underwater, Defense
		InvalidTargets: NoAutoTarget, WaterStructure, AntiAirDefense
	AutoTargetPriority@ATTACKANYTHINGG:
		RequiresCondition: (stance-attackanything || assault-move) && !is-aa
		ValidTargets: Infantry, Vehicle, Water, Underwater, Structure, Defense, Mine
		InvalidTargets: NoAutoTarget
	AttackMove:
		AssaultMoveCondition: assault-move
	AutoTargetPriority@REPAIR:
		ValidTargets: Repair
		RequiresCondition: mechturr
		ValidRelationships: Ally
	AutoTargetPriority@HEAL:
		ValidTargets: Heal
		RequiresCondition: medturr
		ValidRelationships: Ally
	AutoTargetPriority@defuse:
		ValidTargets: C4Attached, TNTAttached
		InvalidTargets: NoAutoTarget
		ValidRelationships: Ally
	GrantConditionOnPrerequisite@APB:
		Condition: apb-upgrade
		Prerequisites: apb.upgrade
	GrantConditionOnPrerequisite@CRYO:
		Condition: cryw-upgrade
		Prerequisites: cryw.upgrade
	GrantConditionOnPrerequisite@TibCore:
		Condition: tibcore-upgrade
		Prerequisites: tibcore.upgrade
	GuardsSelection@REPAIR:
		ValidTargets: Vehicle
		RequiresCondition: mechturr && !stance-attackanything
	GuardsSelection@MEDIC:
		ValidTargets: Infantry
		RequiresCondition: medturr && !stance-attackanything
	GuardsSelection@SPY:
		RequiresCondition: spyturr && !stance-attackanything
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: commturr || psyturr
	WithDecoration@SEALSKULL:
		RequiresCondition: sealturr
		Image: pips
		Sequence: pip-seal
		Palette: effect
		Position: TopLeft
		ValidRelationships: Ally, Enemy, Neutral
	WithDecoration@CMSR:
		RequiresCondition: cmsrturr
		Image: pips
		Sequence: pip-cmsr
		Position: TopLeft
		ValidRelationships: Ally, Enemy, Neutral
	KeepsDistance:
		RequiresCondition: engturr || mechturr || medturr
	DamageTypeDamageMultiplier@A2GPROTECTION:
		RequiresCondition: is-aa
	AmbientSoundCA@BlackHand:
		SoundFiles: flamer-loop1.aud
		InitialSound: flamer-start1.aud
		FinalSound: flamer-end1.aud
		RequiresCondition: bhturr && attacking
		InitialSoundLength: 20
	AmbientSoundCA@Cryo:
		SoundFiles: cryobeam.aud
		InitialSound: cryobeamstart.aud
		FinalSound: cryobeamend.aud
		RequiresCondition: cryoturr && attacking
		InitialSoundLength: 10
	GrantConditionOnAttackCA@BlackHand:
		Condition: attacking
		RevokeDelay: 3
		RequiresCondition: bhturr
	GrantConditionOnAttackCA@Cryo:
		Condition: attacking
		RevokeDelay: 6
		RequiresCondition: cryoturr
	ChronoshiftableCA:
		RequiresCondition: !ivanturr && !being-warped
	ChronoshiftableCA@Ivan:
		Image: chrono
		ExplodeInstead: true
		RequiresCondition: ivanturr && !being-warped
	DamageMultiplier@GGI:
		RequiresCondition: ggiturr
		Modifier: 80
	FirepowerMultiplier@GGI:
		RequiresCondition: ggiturr
		Modifier: 105
	RangeMultiplier@GGI:
		RequiresCondition: ggiturr
		Modifier: 115
	DamageMultiplier@Enforcer:
		RequiresCondition: enfoturr
		Modifier: 50
	SpeedMultiplier@EnfoTigr:
		RequiresCondition: tigrturr || enfoturr
		Modifier: 60
	Captures@Engineer:
		CaptureTypes: building
		CaptureDelay: 300
		ConsumedByCapture: false
		RequiresCondition: !capture-interrupt && engturr
	CaptureManager:
		CapturingCondition: capturing
	GrantConditionOnDamage@InterruptCapture:
		Condition: capture-interrupt
		Duration: 1
	GrantCondition@CaptureDecloak:
		Condition: cloak-force-disabled
		RequiresCondition: capturing
	Cloak@NORMAL:
		InitialDelay: 75
		CloakDelay: 76
		CloakSound: trans1.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: (shadturr || stlkturr) && !cloak-force-disabled && !capturing && !being-warped && !empdisable && !driver-dead && !parachute
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	ProximityExternalCondition@Inspiration:
		Range: 5c0
		Condition: cmsr-inspiration
		ValidRelationships: Ally
		AffectsParent: true
		RequiresCondition: cmsrturr && !passenger
	WithRadiatingCircle:
		EndRadius: 5c0
		Interval: 75
		Duration: 50
		ValidRelationships: Ally
		AlwaysShowMaxRange: true
		RequiresCondition: cmsrturr
	Armament@Psi:
		Weapon: EnslaveInfantry
		LocalOffset: 200,0,300
		RequiresCondition: psyturr
	Armament@PsiVehicle:
		Weapon: EnslaveVehicle
		LocalOffset: 200,0,300
		RequiresCondition: psyturr
	MindController@MC:
		ControlType: MindControl
		Capacity: 3
		ControlSounds: iyurat1a.aud
		ReleaseSounds: yuri-release.aud
		MaxControlledCondition: maxcontrolled
		ControlAtCapacityBehaviour: DetonateOldest
		SlaveDeployEffect: Detonate
		SlaveDetonateWeapon: DetonateSlave
		SlaveKillDamageTypes: BulletDeath
		ExperienceFromControl: 100
		TargetTypeTicksToControl:
			MindControlResistant: 105
		RequiresCondition: psyturr
	WithMindControlArc@MC:
		ControlType: MindControl
		Color: c71585
		Transparency: 55
		Offset: 0,0,300
		Angle: 32
		Width: 116
	Armament@Hack:
		Weapon: Hack
		TargetRelationships: Enemy, Neutral
		RequiresCondition: hackturr
		LocalOffset: 200,0,300
	MindController@HACK:
		ControlType: Hack
		ArmamentNames: primary
		Capacity: -1
		TicksToControl: 150
		TicksToRevoke: 25
		InitSounds: hacker-init.aud
		InitSoundControllerOnly: true
		ControlSounds: hacker-hacked.aud
		AutoUndeploy: true
		TargetTypeTicksToControl:
			Structure: 300
		RequiresCondition: hackturr
	WithMindControlArc@HACK:
		ControlType: Hack
		Color: 1ce312
		Transparency: 65
		Angle: 60
		Width: 86
		Offset: 0,0,300
	MindControllerCapacityModifier@RANK-1:
		Amount: 1
		RequiresCondition: rank-veteran == 1
	MindControllerCapacityModifier@RANK-2:
		Amount: 2
		RequiresCondition: rank-veteran == 2
	MindControllerCapacityModifier@RANK-ELITE:
		Amount: 3
		RequiresCondition: rank-elite
	RejectsOrders@AnathemaNoUnload:
		Reject: Unload
		RequiresCondition: anathema
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	Encyclopedia:
		Category: Allies/Vehicles

IFV.AI:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 170
		Prerequisites: anyradar, ~vehicles.allies, ~botplayer, ~techlevel.medium
		Description: Adaptable infantry transport.
	Tooltip:
		Name: Infantry Fighting Vehicle
		RequiresCondition: !gunturr && !samturr
	Tooltip@gunturr:
		Name: MG IFV
		RequiresCondition: gunturr
	Tooltip@samturr:
		Name: Missile IFV
		RequiresCondition: samturr
	TooltipExtras:
		Strengths: • Strong vs Aircraft, Heavy Armor
		Weaknesses: • Weak vs Infantry
		Attributes: • Weapon/function changes depending on infantry passenger
		RequiresCondition: defturr || samturr
	TooltipExtras@gunturr:
		Strengths: • Strong vs Infantry, Light Armor, Buildings
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft
		IsStandard: false
		RequiresCondition: gunturr
	Valued:
		Cost: 750
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 27000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 100
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 40
		Offset: 20,0,0
	Armament@Default:
		Weapon: IFVRockets
		LocalOffset: 192,50,176, 192,-50,176
		RequiresCondition: defturr
	Armament@DefaultAA:
		Name: secondary
		Weapon: IFVRocketsAA
		LocalOffset: 192,50,176, 192,-50,176
		RequiresCondition: defturr
	Armament@E1:
		Weapon: M60mgIFV
		MuzzleSequence: muzzle
		LocalOffset: 250,0,150
		RequiresCondition: gunturr
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-125,200
		CasingTargetOffset: 0, -500, 0
	Armament@E3:
		Weapon: IFVRocketsE
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: samturr && !cryw-upgrade
	Armament@E3AA:
		Name: secondary
		Weapon: IFVRocketsAAE
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: samturr && !cryw-upgrade
	Armament@E3CRYO:
		Weapon: IFVRocketsE.CRYO
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: samturr && cryw-upgrade
	Armament@E3CRYOAA:
		Name: secondary
		Weapon: IFVRocketsAAE.CRYO
		LocalOffset: 192,100,176, 192,-100,176
		RequiresCondition: samturr && cryw-upgrade
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret@default:
		Sequence: turret
		RequiresCondition: defturr
	WithSpriteTurret@gun:
		RequiresCondition: gunturr
		Sequence: turret-mg
	WithSpriteTurret@samturr:
		RequiresCondition: samturr
		Sequence: turret-rocket
	RenderSprites:
		Image: ifv
	GrantRandomCondition:
		Conditions: defturr, samturr, gunturr
	-Capturable:
	-Capturable@DRIVER_DEAD:
	-Targetable@DRIVERKILL:
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
		ScanRadius: 6
	AutoTargetPriority@DEFAULTAA:
		ValidTargets: Infantry, Vehicle, Water, Underwater, Air, Defense
		InvalidTargets: NoAutoTarget, WaterStructure, AntiAirDefense
		RequiresCondition: (!stance-attackanything && !assault-move) && (defturr || samturr)
	AutoTargetPriority@ATTACKANYTHINGAA:
		ValidTargets: Infantry, Vehicle, Water, Underwater, Air, Structure, Defense
		InvalidTargets: NoAutoTarget
		RequiresCondition: (stance-attackanything || assault-move) && (defturr || samturr)
	AutoTargetPriority@DEFAULTAAPRIO:
		RequiresCondition: (!stance-attackanything && !assault-move) && (defturr || samturr)
		ValidTargets: Air
		InvalidTargets: NoAutoTarget
		Priority: 10
	AutoTargetPriority@ATTACKANYTHINGAAPRIO:
		RequiresCondition: (!stance-attackanything && !assault-move) && (defturr || samturr)
		ValidTargets: Air
		InvalidTargets: NoAutoTarget
		Priority: 10
	AutoTargetPriority@DEFAULTG:
		RequiresCondition: !stance-attackanything && !assault-move && !defturr && !samturr
		ValidTargets: Infantry, Vehicle, Water, Underwater, Defense
		InvalidTargets: NoAutoTarget, WaterStructure, AntiAirDefense
	AutoTargetPriority@ATTACKANYTHINGG:
		RequiresCondition: (stance-attackanything || assault-move) && !defturr && !samturr
		ValidTargets: Infantry, Vehicle, Water, Underwater, Structure, Defense, Mine
		InvalidTargets: NoAutoTarget
	AttackMove:
		AssaultMoveCondition: assault-move
	GrantConditionOnPrerequisite@CRYO:
		Condition: cryw-upgrade
		Prerequisites: cryw.upgrade
	GrantCondition@CRYO:
		Condition: cryw-upgrade
		RequiresCondition: cryw-upgrade
	ExternalCondition@CRYO:
		Condition: cryw-upgrade

RECK:
	Inherits: IFV
	Inherits@InfiltratorStolenTech: ^InfiltratorStolenTech
	RenderSprites:
		Image: reck
	Valued:
		Cost: 1000
	Health:
		HP: 52000
	Mobile:
		Speed: 60
		TurnSpeed: 24
	Buildable:
		BuildPaletteOrder: 500
		Prerequisites: radarorrepair, ~vehicles.reck
	Tooltip:
		Name: Reckoner
	Tooltip@gunturr:
		Name: MG Reckoner
	Tooltip@fragturr:
		Name: Grenade Reckoner
	Tooltip@samturr:
		Name: Missile Reckoner
	Tooltip@mechturr:
		Name: Repair Reckoner
	Tooltip@engturr:
		Name: Engineer Reckoner
	Tooltip@commturr:
		Name: Commando Reckoner
	Tooltip@chemturr:
		Name: Chemical Reckoner
	Tooltip@medturr:
		Name: Medical Reckoner
	Tooltip@ivanturr:
		Name: Explosive Reckoner
	Tooltip@flamturr:
		Name: Flamethrower Reckoner
	Tooltip@spyturr:
		Name: Spy Reckoner
	Tooltip@sealturr:
		Name: SEAL Reckoner
	Tooltip@acolturr:
		Name: Laser Reckoner
	Tooltip@snipturr:
		Name: Sniper Reckoner
	Tooltip@desoturr:
		Name: Desolator Reckoner
	Tooltip@testurr:
		Name: Tesla Reckoner
	Tooltip@rmbcturr:
		Name: Cyborg Elite Reckoner
	Tooltip@enliturr:
		Name: Enlightened Reckoner
	Tooltip@bhturr:
		Name: Black Hand Reckoner
	Tooltip@mortchemturr:
		Name: Chemical Mortar Reckoner
	Tooltip@mortcryoturr:
		Name: Cryo Mortar Reckoner
	Tooltip@mortsonicturr:
		Name: Sonic Mortar Reckoner
	Tooltip@discturr:
		Name: Plasma Disc Reckoner
	Tooltip@sharturr:
		Name: Shard Reckoner
	Tooltip@ggiturr:
		Name: GGI Reckoner
	Tooltip@psyturr:
		Name: Psychic Reckoner
	Tooltip@enfoturr:
		Name: Enforcer Reckoner
	Tooltip@ztrpturr:
		Name: Zone Trooper Reckoner
	Tooltip@zdefturr:
		Name: Zone Defender Reckoner
	Tooltip@zraiturr:
		Name: Zone Raider Reckoner
	Tooltip@tigrturr:
		Name: Tiger Guard Reckoner
	Tooltip@disinturr:
		Name: Disintegrator Reckoner
	Tooltip@implturr:
		Name: Impaler Reckoner
	Tooltip@stlkturr:
		Name: Stalker Reckoner
	Tooltip@hoplturr:
		Name: Hoplite Reckoner
	Tooltip@cmsrturr:
		Name: Commissar Reckoner
	Tooltip@shadturr:
		Name: Shadow Reckoner
	Tooltip@hackturr:
		Name: Hacker Reckoner
	Tooltip@confturr:
		Name: Confessor Reckoner
	Tooltip@reapturr:
		Name: Reaper Reckoner
	ReloadDelayMultiplier@CyborgElite:
		RequiresCondition: rmbcturr
		Modifier: 80
	SpeedMultiplier@TigerGuard:
		Modifier: 90
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Stolen from Allied War Factory.

TITN:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeGround
	Inherits@BERSERK: ^Berserk
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@GYRO: ^GyroStabilizers
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 257
		IconPalette: chrometd
		Prerequisites: gtek, ~vehicles.talon, ~!railgun.upgrade, ~techlevel.high
		Description: Tough, slow combat battle-mech.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Attributes: • Can crush smaller vehicles and concrete walls\n• Self repairs to 50% out of combat
	Valued:
		Cost: 2000
	Selectable:
		Bounds: 1280, 1706
		DecorationBounds: 1280, 1706, 0, -100
	Tooltip:
		Name: Titan
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 102000
	Armor:
		Type: Heavy
	Mobile:
		Locomotor: sheavytracked
		Speed: 43
		Voice: Move
	Passenger:
		CustomPipType: red
		Weight: 3
		Voice: Move
	QuantizeFacingsFromSequence:
		Sequence: stand
	WithFacingSpriteBody:
		Sequence: stand
	WithMoveAnimation:
		MoveSequence: run
	WithSpriteTurret:
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: TitanGun
		LocalOffset: 830,290,170
		Recoil: 171
		RecoilRecovery: 30
		MuzzleSequence: muzzle
	Armament@SECONDARY:
		Name: secondary
		Weapon: TitanTusk
		LocalOffset: -85,-384,340
		LocalYaw: -100,100
		Recoil: 43
		MuzzleSequence: muzzle
		PauseOnCondition: gyrostabilizers
	Armament@SECONDARYGYRO:
		Name: secondary
		Weapon: TitanTusk.Gyro
		LocalOffset: -85,-384,340
		LocalYaw: -100,100
		Recoil: 43
		MuzzleSequence: muzzle
		PauseOnCondition: !gyrostabilizers
	Turreted:
		TurnSpeed: 10
		Offset: 0,0,0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary, secondary
		Voice: Attack
	AttackMove:
		Voice: Attack
	WithMuzzleOverlay:
	Voiced:
		VoiceSet: TitnVoice
	ChangesHealth@DEFAULT:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 50
		DamageCooldown: 150
	Carryable:
		LocalOffset: 0,0,700
	-Crushable:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	SpawnActorOnDeath:
		Actor: TITN.Husk
		RequiresCondition: !being-warped
	WithIdleOverlay@MINDCONTROL:
		Offset: 0,0,512
	ReplacedInQueue:
		Actors: titn.rail
	Upgradeable@RAILGUN:
		Type: railgun.upgrade
		UpgradingCondition: upgrading
		Actor: titn.rail
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	RangeMultiplier@SEEK:
		RequiresCondition: seek && !gyrostabilizers
	RangeMultiplier@SEEK2:
		RequiresCondition: seek2 && !gyrostabilizers
	RangeMultiplier@SEEK3:
		RequiresCondition: seek3 && !gyrostabilizers
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: talon

TITN.RAIL:
	Inherits: TITN
	Tooltip:
		Name: Railgun Titan
	Buildable:
		BuildPaletteOrder: 258
		Prerequisites: gtek, ~vehicles.talon, ~railgun.upgrade, ~techlevel.high
	Armament@PRIMARY:
		Weapon: TitanRailgun
		LocalOffset: 830,290,170
		Recoil: 171
		RecoilRecovery: 30
		MuzzleSequence: muzzle
	Armament@PRIMARYUPGDIRECT:
		Weapon: TitanRailgunImpact
		LocalOffset: 830,290,170
	SpawnActorOnDeath:
		Actor: TITN.RAIL.Husk
	-ReplacedInQueue:
	-Upgradeable@RAILGUN:
	-WithDecoration@UpgradeOverlay:

JUGG:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	Inherits@GYRO: ^GyroStabilizers
	Tooltip:
		Name: Juggernaut
	Buildable:
		BuildPaletteOrder: 261
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Tough artillery battle-mech.
		Prerequisites: gtek, ~vehicles.talon, ~techlevel.high
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets\n• Cannot fire while moving
		Attributes: • Can crush smaller vehicles and concrete walls
	Mobile:
		Locomotor: sheavytracked
		Speed: 43
		Voice: Move
		PauseOnCondition: aiming || being-captured || empdisable || being-warped || driver-dead || notmobile
		BlockedCursor: move
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Selectable:
		Bounds: 1280, 1706
		DecorationBounds: 1280, 1706, 0, -100
	Valued:
		Cost: 2000
	QuantizeFacingsFromSequence:
		Sequence: stand
	WithFacingSpriteBody:
		Sequence: stand
	WithMoveAnimation:
		MoveSequence: run
	WithSpriteTurret:
	AttackMove:
		AttackMoveCondition: attack-move
		Voice: Attack
	Armament@PRIMARY:
		Weapon: JuggernautGun
		LocalOffset: 700,0,650, 700,100,650, 700,-100,650
		Recoil: 171
		RecoilRecovery: 30
		MuzzleSequence: muzzle
		PauseOnCondition: moving || reloading-secondary || gyrostabilizers
		ReloadingCondition: reloading-primary
	Armament@SECONDARY:
		Name: secondary
		Weapon: RocketShells
		LocalOffset: 700,0,650, 700,100,650, 700,-100,650
		Recoil: 171
		RecoilRecovery: 30
		MuzzleSequence: muzzle
		PauseOnCondition: moving || reloading-primary || !gyrostabilizers
		ReloadingCondition: reloading-secondary
	Armament@TARGETTER:
		Name: targetter
		LocalOffset: 700,0,650, 700,100,650, 700,-100,650
		Weapon: JuggernautGunTargeting
		PauseOnCondition: reloading-primary || reloading-secondary
		RequiresCondition: attack-move || assault-move
	AttackTurreted:
		Armaments: primary, secondary, targetter
		PauseOnCondition: empdisable || being-warped || blinded
		TargetFrozenActors: True
		Voice: Attack
		ForceFireIgnoresActors: True
	WithMuzzleOverlay:
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		Offset: -20,0,20
		TurnSpeed: 12
	Health:
		HP: 62000
	Armor:
		Type: Heavy
	Passenger:
		Weight: 3
		Voice: Move
	Voiced:
		VoiceSet: JuggVoice
	GrantConditionOnAttack@AIMING:
		ArmamentNames: primary, secondary, targetter
		Condition: aiming
		RevokeDelay: 15
		RevokeAll: false
	GrantConditionOnAttack@SECONDARYFIRING:
		ArmamentNames: secondary
		Condition: secondary-firing
		RevokeDelay: 15
		MaximumInstances: 3
	GrantCondition@SECONDARYFIRING:
		Condition: gyrostabilizers
		RequiresCondition: secondary-firing
	GrantConditionOnMovement@MOVING:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: moving
	RangeMultiplier@GyroPassive:
		Modifier: 150
		RequiresCondition: gyro-upgrade
	RangeMultiplier@GYRO:
		Modifier: 135
	ReloadDelayMultiplier@GYRO:
		Modifier: 135
	Carryable:
		LocalOffset: 0,0,700
	-Crushable:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
		LoadedChance: 75
	SpawnActorOnDeath:
		Actor: JUGG.Husk
	WithIdleOverlay@MINDCONTROL:
		Offset: 0,0,512
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: talon

TTRK:
	Inherits: DTRK
	RenderSprites:
		PlayerPalette: playertd
	Buildable:
		BuildPaletteOrder: 510
		Prerequisites: techcenter.any, ~vehicles.ttrk, ~techlevel.high
		Description: Truck carrying a Tiberium bomb.
	TooltipExtras:
		Weaknesses: • Very weak armor
		Attributes: • Special Ability: Detonate
	Tooltip:
		Name: Toxin Truck
	FireWarheadsOnDeath:
		Weapon: UnitExplodeToxinTruck
		EmptyWeapon: UnitExplodeToxinTruck
	Encyclopedia:
		Category: Other/Units

DISR:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Valued:
		Cost: 1600
	Tooltip:
		Name: Disruptor
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 259
		IconPalette: chrometd
		Prerequisites: gtek, ~vehicles.zocom, ~techlevel.high
		Description: Armored high-tech vehicle with medium-range sonic armament.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Light Armor, Infantry
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		Attributes: • Slows enemy movement and rate of fire (with upgrade)
	Health:
		HP: 66000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 16
		Speed: 46
		Voice: Move
	Passenger:
		Weight: 2
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@primary:
		Weapon: DisruptorPulse
		LocalOffset: -100,0,200
		RequiresCondition: !sonic-upgrade && !seek3
	Armament@primaryAmp:
		Weapon: DisruptorPulse.Amp
		LocalOffset: -100,0,200
		RequiresCondition: sonic-upgrade && !seek3
	Armament@primarySeek3:
		Weapon: DisruptorPulse.Seek3
		LocalOffset: -100,0,200
		RequiresCondition: !sonic-upgrade && seek3
	Armament@primaryAmpSeek3:
		Weapon: DisruptorPulse.Amp.Seek3
		LocalOffset: -100,0,200
		RequiresCondition: sonic-upgrade && seek3
	Turreted:
		TurnSpeed: 16
		Offset: -300,0,0
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	WithSpriteTurret:
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Carryable:
		LocalOffset: 0,0,500
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Targetable@Disruptor:
		RequiresCondition: !being-warped
		TargetTypes: Disruptor
	Voiced:
		VoiceSet: DisrVoice
	-Crushable:
	SpawnActorOnDeath:
		Actor: DISR.Husk
		RequiresCondition: !being-warped
	GrantConditionOnPrerequisite@SONIC:
		Condition: sonic-upgrade
		Prerequisites: sonic.upgrade
	-RangeMultiplier@SEEK:
	-RangeMultiplier@SEEK2:
	-RangeMultiplier@SEEK3:
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: zocom

HSAM:
	Inherits: ^VehicleTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeGround
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@HOVERTRAIL: ^HoverTrail
	RenderSprites:
		Image: hmlrs
	Valued:
		Cost: 1100
	Tooltip:
		Name: Hover MLRS
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 191
		Prerequisites: anyradar, ~vehicles.eagle, ~techlevel.medium
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Prototype Hover Multiple Launch Rocket System. Long-range guided artillery with anti-air capability.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor
		Attributes: • Can traverse water
	Mobile:
		Speed: 86
		Locomotor: hover
		Voice: Move
	Passenger:
		Voice: Move
	Health:
		HP: 19000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 16
		Offset: -200,0,0
	Hovers:
		BobDistance: -35
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	WithShadow:
		Offset: 43, 128, 0
		ZOffset: -129
		RequiresCondition: !invisibility
	AttackTurreted:
		TargetFrozenActors: True
		ForceFireIgnoresActors: false
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	WithSpriteTurret:
	FireWarheadsOnDeath:
		Weapon: ArtilleryExplode
		LoadedChance: 75
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	GrantConditionOnPrerequisite@HSONIC:
		Condition: hypersonic-upgrade
		Prerequisites: hypersonic.upgrade
	GrantConditionOnPrerequisite@PERCUS:
		Condition: hammerhead-upgrade
		Prerequisites: hammerhead.upgrade
	GrantConditionOnPrerequisite@HAILST:
		Condition: hailstorm-upgrade
		Prerequisites: hailstorm.upgrade
	Armament@PRIMARY:
		Weapon: 227mmH
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 100, -100
		RequiresCondition: !hypersonic-upgrade && !hammerhead-upgrade && !hailstorm-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARY:
		Name: secondary
		Weapon: 227mmAAH
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 100, -100
		RequiresCondition: !hypersonic-upgrade && !hammerhead-upgrade && !hailstorm-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Armament@PRIMARYUPG1:
		Weapon: 227mmH.Hypersonic
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 75, -75
		RequiresCondition: hypersonic-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARYUPG1:
		Name: secondary
		Weapon: 227mmAA.Hypersonic
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 75, -75
		RequiresCondition: hypersonic-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Armament@PRIMARYUPG2:
		Weapon: 227mmH.Hammerhead
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 100, -100
		RequiresCondition: hammerhead-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARYUPG2:
		Name: secondary
		Weapon: 227mmAA.Hammerhead
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 100, -100
		RequiresCondition: hammerhead-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Armament@PRIMARYUPG3:
		Weapon: 227mmH.Hailstorm
		LocalOffset: 213,128,0, 213,-128,0
		LocalYaw: 100, -100
		RequiresCondition: hailstorm-upgrade
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARYUPG3:
		Name: secondary
		Weapon: 227mmAA.Hailstorm
		LocalOffset: 213,-128,0, 213,128,0
		LocalYaw: 100, -100
		RequiresCondition: hailstorm-upgrade
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater
	Voiced:
		VoiceSet: MsamVoice
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: eagle

RTNK:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlightlySlowedByCrushing
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 120
		Prerequisites: ~vehicles.england, ~techlevel.low
		Description: Advanced battle tank that can disguise when stationary.
	TooltipExtras:
		Strengths: • Strong vs Light Armor, Heavy Armor
		Weaknesses: • Weak vs Defenses, Infantry\n• Cannot attack Aircraft
		Attributes: • Disguised when not moving\n• Deals additional damage firing from disguised state
	Valued:
		Cost: 750
	Tooltip:
		Name: Mirage Tank
	Mirage:
		RequiresCondition: !cloak-force-disabled && !invisibility && !driver-dead && !empdisable && !being-warped
		RevealOn: Attack, Dock, Move, Damage, Heal
		MirageCondition: tree
		DefaultTargetTypes: v08, v09, v10, v11, v12, v13, v14, v15, v16, v17, v18
		InitialDelay: 99
		RevealDelay: 100
		EffectiveOwner: Self
	WithMirageSpriteBody:
		RequiresCondition: tree
		IsPlayerPalette: false
		Name: tree
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 32000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 100
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 20
	Armament:
		Weapon: 120mmHEAT
		Recoil: 128
		RecoilRecovery: 38
		LocalOffset: 720,0,80
		MuzzleSequence: muzzle
	AttackTurreted:
		Voice: Attack
		PauseOnCondition: empdisable || being-warped || blinded
	AttackMove:
		Voice: Attack
	WithMuzzleOverlay:
	WithFacingSpriteBody:
		RequiresCondition: !tree
	WithSpriteTurret:
		RequiresCondition: !tree
	SpawnActorOnDeath:
		Actor: RTNK.Husk
		RequiresCondition: !being-warped
	Cloak@NORMAL:
		InitialDelay: 99
		CloakDelay: 100
		CloakSound: vmirat2a.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		UncloakOn: Attack, Dock, Move, Damage, Heal
		CloakStyle: Palette
		CloakedPalette: player
		IsPlayerPalette: false
		RequiresCondition: !cloak-force-disabled && !being-warped && !empdisable && !driver-dead
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	GrantConditionOnDamageState@UNCLOAK:
		ValidDamageStates: Critical
	Selectable:
		DecorationBounds: 1194, 1194
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Voiced:
		VoiceSet: MirageVoice
	WithDecoration@disguise:
		RequiresCondition: hidden
		Image: gpsdot
		Sequence: MirageTank
		Palette: player
		IsPlayerPalette: true
		Position: Center
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	FirepowerMultiplier@Ambush:
		Modifier: 150
		RequiresCondition: hidden
	Encyclopedia:
		Category: Allies/Vehicles
	EncyclopediaExtras:
		Subfaction: england

V3RL:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 181
		Prerequisites: ~vehicles.soviet, ~v3.upgrade, ~techlevel.high
		Description: Extreme long-range rocket artillery.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets\n• Rockets can be shot down by static anti-air defenses
	Valued:
		Cost: 1500
	Tooltip:
		Name: V3 Launcher
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 16000
	Armor:
		Type: Light
	Mobile:
		Speed: 46
		TurnSpeed: 16
		Voice: Move
		PauseOnCondition: launching || being-captured || empdisable || being-warped || driver-dead || notmobile
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: V3Launcher
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	WithFacingSpriteBody:
		RequiresCondition: loaded
		Name: loaded
	WithFacingSpriteBody@EMPTY:
		RequiresCondition: !loaded
		Sequence: empty-idle
		Name: reloading
	MissileSpawnerMaster:
		Actors: V3
		RespawnTicks: 235
		LoadedCondition: loaded
		LaunchingCondition: launching
		SpawnOffset: 200,0,350
	WithSpawnerMasterPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	Voiced:
		VoiceSet: V3Voice
	ProductionCostMultiplier@UkraineBonus:
		Multiplier: 90
		Prerequisites: player.ukraine
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		RenderPreviewActor: v3rl.preview

V3RL.Preview:
	Inherits: ^PreviewDummy
	RenderSprites:
		Image: v3rl
	WithFacingSpriteBody:

V3:
	Inherits: ^ShootableMissile
	Valued:
		Cost: 50
	Tooltip:
		Name: V3 Rocket
	Health:
		HP: 5000
	BallisticMissile:
		LaunchAngle: 100
		Speed: 165
		MinAirborneAltitude: 256
		AirborneCondition: airborne
	LeavesTrailsCA:
		Image: smokey3
		Sequences: idle, idle2, idle3, idle4
		MovingInterval: 2
		Type: CenterPosition
		Offsets: -200, 0, 0
	MissileSpawnerSlave:
	SpawnedExplodes:
		Weapon: V3Weapon
		Type: Footprint
		RequiresCondition: !airborne
	FireWarheadsOnDeath:
		Weapon: V3ExplodeAirborne
		RequiresCondition: airborne
	FireProjectilesOnDeath@Debris:
		Weapons: SmallDebris
		Pieces: 3, 5
		Range: 1c511, 3c0
		RequiresCondition: airborne

TNKD:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMovePrioritizeVehicles
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 171
		Prerequisites: ~vehicles.germany, anyradar, ~techlevel.medium
		Description: German tank destroyer.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor
		Weaknesses: • Weak vs Infantry, Buildings, Defenses, Light Armor\n• Cannot attack Aircraft
	Valued:
		Cost: 1150
	Tooltip:
		Name: Tank Destroyer
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 38000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 60
		TurnSpeed: 16
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 8
		Offset: -80,0,20
	Armament:
		Weapon: 183mm
		Recoil: 85
		RecoilRecovery: 25
		LocalOffset: 868,0,0
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: TNKD.Husk
		RequiresCondition: !being-warped
	Voiced:
		VoiceSet: TnkdVoice
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	Encyclopedia:
		Category: Allies/Vehicles
	EncyclopediaExtras:
		Subfaction: germany

TNKD.TEMP:
	Inherits: TNKD
	Inherits@TEMPINC: ^TemporalReinforcement
	RenderSprites:
		Image: tnkd
	FireWarheadsOnDeath:
		RequiresCondition: !warpout
	-ActorLostNotification:
	-SpawnActorOnDeath:
	-Buildable:
	-ChronoshiftableCA:
	-HealthCapDamageMultiplier@CHRONO:
	-MapEditorData:
	-Encyclopedia:
	-EncyclopediaExtras:

MSAR:
	Inherits: ^VehicleTD
	Inherits@SELECTION: ^SelectableSupportUnit
	Valued:
		Cost: 1250
	Tooltip:
		Name: Mobile Sensor Array
		RequiresCondition: !deployed
	Tooltip@DEPLOYED:
		Name: Mobile Sensor Array (deployed)
		RequiresCondition: deployed
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 321
		IconPalette: chrometd
		Prerequisites: anyradar, ~vehicles.gdi, ~techlevel.medium
		Description: When deployed, detects enemy vehicles, aircraft and structures within a large radius.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Can detect cloaked units (when deployed)\n• Increases nearby ground unit vision range (when deployed)\n• Special Ability: Activate Sensor Array
	Health:
		HP: 24000
	Armor:
		Type: Light
	Mobile:
		Speed: 72
		ImmovableCondition: deployed
		RequireForceMoveCondition: !undeployed
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	RenderSprites:
		Image: msar
	GrantCondition@PREVIEWWORKAROUND:
		Condition: real-actor
	WithMakeAnimation:
		BodyNames: deployedbody
	Carryable:
		LocalOffset: 0,0,150
	WithSpriteBody@deployed:
		Sequence: idle-static
		RequiresCondition: !undeployed && real-actor
		Name: deployedbody
	WithFacingSpriteBody:
		RequiresCondition: !deployed
	GrantConditionOnDeploy:
		PauseOnCondition: empdisable || being-warped || being-captured
		DeployedCondition: deployed
		UndeployedCondition: undeployed
		Facing: 660
		AllowedTerrainTypes: Clear, Road, Rough, Ore, Gems, Tiberium, BlueTiberium
		DeploySounds: placbldg.aud
		UndeploySounds: clicky1.aud
		UndeployOnMove: true
		UndeployOnPickup: true
	DetectCloaked@Deployed:
		Range: 10c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: deployed && !(empdisable || being-warped)
	ProvidesRadar:
		RequiresCondition: deployed && !(empdisable || being-warped)
	Selectable:
		DecorationBounds: 1280, 1280
	Passenger:
		CargoCondition: passenger
	ProximityExternalCondition@Bino:
		Condition: bino
		Range: 12c0
		EnableSound: dsaping1.aud
		AffectsParent: False
		RequiresCondition: deployed && !(empdisable || being-warped) && !passenger
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	RangedGpsRadarProvider:
		Range: 24c0
		TargetTypes: Vehicle, Air, Structure
		RequiresCondition: deployed && !(empdisable || being-warped)
	WithDetectionCircle:
		Range: 24c0
		RequiresCondition: deployed && !(empdisable || being-warped)
		TrailCount: 3
	ExternalCondition@JAMMED:
		Condition: jammed
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active
		RequiresCondition: deployed && !(jammed || empdisable || being-warped)
	-WithDecoration@BOMBARD:
	-WithDecoration@BOMBARD2:
	-WithDecoration@BOMBARD3:
	Encyclopedia:
		Category: GDI/Vehicles

PTNK:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 172
		Prerequisites: anyradar, ~vehicles.allies, ~!pcan.upgrade, ~techlevel.medium
		Description: Long-range artillery with Prism Tower derived weapon.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Buildings, Defenses, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
	Valued:
		Cost: 1350
	Tooltip:
		Name: Prism Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 20000
	Armor:
		Type: Light
	Mobile:
		Speed: 72
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 20
		Offset: 100,0,0
	Armament:
		Weapon: PrisTLaser
		LocalOffset: 150,0,250
		MuzzleSequence: muzzle
		MuzzlePalette: caneon
	WithMuzzleOverlay:
	AttackTurreted:
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	AttackMove:
		Voice: Attack
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: PTNK.Husk
		RequiresCondition: !being-warped
	Voiced:
		VoiceSet: PrismVoice
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	ReplacedInQueue:
		Actors: pcan
	Upgradeable@PCAN:
		Type: pcan.upgrade
		UpgradingCondition: upgrading
		Actor: pcan
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Allies/Vehicles

PCAN:
	Inherits: PTNK
	Buildable:
		BuildPaletteOrder: 173
		Prerequisites: anyradar, ~vehicles.allies, ~pcan.upgrade, ~techlevel.high
		Description: A prototype long-range artillery that fires highly focused beams of light.
	Tooltip:
		Name: Prism Cannon
	Mobile:
		Speed: 60
		TurnSpeed: 12
	Armament:
		Weapon: PrisCLaser
		LocalOffset: 450,0,180
	SpawnActorOnDeath:
		Actor: PCAN.Husk
	Turreted:
		TurnSpeed: 12
		Offset: 0,0,0
	-AttackTurreted:
	AttackTurretedCharged:
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
		ChargeLevel: 15
		ChargingSounds: pcancharge.aud
	Voiced:
		VoiceSet: PrismCannonVoice
	-ReplacedInQueue:
	-Upgradeable@PCAN:
	-WithDecoration@UpgradeOverlay:

WTNK:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	RenderSprites:
		Image: mwtnk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 203
		IconPalette: chrometd
		Prerequisites: anyradar, ~vehicles.legion, ~techlevel.medium
		Description: Prototype MGT-1A microwave gun tank.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses, Infantry
		Weaknesses: • Weak vs Buildings\n• Cannot attack Aircraft
		Attributes: • Briefly disables vehicles/defenses\n• Kills crew of vehicles with less than 50% HP (with upgrade)\n• Crewless vehicles capturable by infantry
	Valued:
		Cost: 1500
	Tooltip:
		Name: Microwave Tank
		GenericName: Tank
	Health:
		HP: 20000
	Armor:
		Type: Light
	Mobile:
		Speed: 68
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 16
		Offset: 0,0,0
	Armament@PRIMARY:
		Weapon: MicrowaveZap
		LocalOffset: 500,0,140
		MuzzleSequence: muzzle
		MuzzlePalette: tseffect-ignore-lighting-alpha75
		RequiresCondition: !microwave-upgrade
	Armament@PRIMARYUPG:
		Weapon: MicrowaveZap.UPG
		LocalOffset: 500,0,140
		MuzzleSequence: muzzle
		MuzzlePalette: tseffect-ignore-lighting-alpha75
		RequiresCondition: microwave-upgrade
	AttackTurretedCharged:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
		ChargeLevel: 9
		ChargingSounds: coreup1.aud
	AttackMove:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: WTNK.Husk
		RequiresCondition: !being-warped
	Voiced:
		VoiceSet: MWTnkVoice
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	GrantConditionOnPrerequisite@UPG:
		Condition: microwave-upgrade
		Prerequisites: microwave.upgrade
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		Subfaction: legion

#adjust offsets
BATF:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@TRANSPORT: ^Transport
	Inherits@NOUNLOADCHRONO: ^NoUnloadWhenChronoshifted
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	Inherits@COMMANDOSKULL: ^CommandoSkull
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 223
		Prerequisites: atek, ~vehicles.france, ~techlevel.high
		Description: Tough infantry transport with weapon ports for infantry to fire from.
	TooltipExtras:
		Attributes: • Can crush smaller vehicles and concrete walls
	Valued:
		Cost: 2000
	Tooltip:
		Name: Battle Fortress
	Health:
		HP: 135000
	Armor:
		Type: Heavy
	Mobile:
		Locomotor: sheavytracked
		Speed: 43
		Voice: Move
		TurnSpeed: 8
	Passenger:
		Weight: 2
		Voice: Move
		CargoCondition: passenger
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	DetectCloaked@SPY:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: loaded-spy && !(empdisable || being-warped)
	AttackFrontal:
		FacingTolerance: 24
		PauseOnCondition: empdisable || being-warped || blinded
		RequiresCondition: !(loaded || loaded-cmdo || loaded-seal || loaded-air)
		Voice: Attack
	AttackMove:
		Voice: Attack
	Armament@DEFAULT:
		Name: primary
		Weapon: BATFGun
		LocalOffset: 825,0,271
		MuzzleSequence: muzzle
	WithMuzzleOverlay:
	Cargo:
		Types: Infantry, Hacker
		MaxWeight: 5
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			e1: loaded
			e2: loaded
			e3: loaded-air
			e4: loaded
			e6: loaded-repair
			e7: loaded-cmdo
			e8: loaded
			u3: loaded-air
			u3r2: loaded-air
			n1: loaded
			n2: loaded
			n3: loaded-air
			n4: loaded
			n5: loaded
			n6: loaded-repair
			bh: loaded
			ivan: loaded
			shad: loaded
			medi: loaded-medic
			mech: loaded-repair
			seal: loaded-seal
			rmbo: loaded-cmdo
			bori: loaded-cmdo
			yuri: loaded-cmdo
			acol: loaded
			tplr: loaded
			cscr: loaded
			enfo: loaded
			hopl: loaded
			tigr: loaded
			cryt: loaded
			ztrp: loaded
			zrai: loaded
			zdef: loaded
			mort.cryo: loaded
			mort.sonic: loaded
			mort.chem: loaded
			cmsr: loaded
			spy: loaded-spy
			snip: loaded
			assa: loaded
			shok: loaded
			sab: loaded-spy
			thf: loaded-spy
			n1c: loaded
			n3c: loaded-air
			tecn: loaded
			enli: loaded
			rmbc: loaded
			s1: loaded
			s2: loaded
			s3: loaded
			s4: loaded
			s6: loaded-repair
			evis: loaded
			impl: loaded
			stlk: loaded
			mast: loaded-cmdo
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	CargoBlocked:
		RequiresCondition: mindcontrolled
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Carryable:
		LocalOffset: 0,0,500
	-Crushable:
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything && !assault-move && !loaded-air
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: (stance-attackanything || assault-move) && !loaded-air
	AutoTargetPriority@DEFAULT_AIR:
		RequiresCondition: !stance-attackanything && !assault-move && loaded-air
		ValidTargets: Infantry, Vehicle, Water, Underwater, Air, AirSmall, Defense
		InvalidTargets: NoAutoTarget, WaterStructure, AntiAirDefense
	AutoTargetPriority@ATTACKANYTHING_AIR:
		RequiresCondition: (stance-attackanything || assault-move) && loaded-air
		ValidTargets: Infantry, Vehicle, Water, Underwater, Air, AirSmall, Structure, Defense
		InvalidTargets: NoAutoTarget
	Voiced:
		VoiceSet: BattleFortressVoice
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	AttackOpenTopped:
		Voice: Attack
		Armaments: batf, batf-bh, batf-cryt
		PortOffsets: 825,0,256, 348,-482,256, -348,-482,256, -448,482,256, 448,482,256
		PauseOnCondition: being-warped || empdisable || blinded
		RequiresCondition: loaded || loaded-cmdo || loaded-seal || loaded-air
	Capturable:
		RequiresCondition: !being-warped && !cargo
	Capturable@DRIVER_DEAD:
		RequiresCondition: driver-dead && !cargo
	Targetable@MindControlResistant:
		TargetTypes: MindControlResistant
		RequiresCondition: cargo
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
		RequiresCondition: loaded-cmdo
	SpawnActorOnDeath:
		Actor: BATF.Husk
		RequiresCondition: !being-warped
	ChangesHealth@REPAIR:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 100
		DamageCooldown: 50
		RequiresCondition: loaded-repair
	WithDecoration@COMMANDOSKULL:
		RequiresCondition: loaded-cmdo
	WithDecoration@SEALSKULL:
		RequiresCondition: loaded-seal && !loaded-cmdo
		Image: pips
		Sequence: pip-seal
		Palette: effect
		Position: TopLeft
		ValidRelationships: Ally, Enemy, Neutral
	WithIdleOverlay@MEDIC:
		Sequence: medic
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: loaded-medic
		Offset: 0,0,250
	ProximityExternalCondition@MEDIC:
		RequiresCondition: loaded-medic && !passenger
		Condition: hospitalheal
		Range: 4c512
	WithRangeCircle@MEDIC:
		Type: IFVHeal
		Color: 00FF0080
		Range: 4c512
		RequiresCondition: loaded-medic
	AmbientSoundCA@BlackHand:
		SoundFiles: flamer-loop1.aud
		InitialSound: flamer-start1.aud
		FinalSound: flamer-end1.aud
		RequiresCondition: attacking-bh
		InitialSoundLength: 20
	GrantConditionOnAttackCA@BlackHand:
		ArmamentNames: batf-bh
		Condition: attacking-bh
		RevokeDelay: 3
	AmbientSoundCA@CryoTrooper:
		SoundFiles: cryobeam.aud
		InitialSound: cryobeamstart.aud
		FinalSound: cryobeamend.aud
		RequiresCondition: attacking-cryt
		InitialSoundLength: 10
	GrantConditionOnAttackCA@CryoTrooper:
		ArmamentNames: batf-cryt
		Condition: attacking-cryt
		RevokeDelay: 6
	GrantConditionOnHealingReceived@REPAIRCOOLDOWN:
		RequiredHealing: 120000
		StackDuration: 1050
	RejectsOrders@AnathemaNoUnload:
		Reject: Unload
		RequiresCondition: anathema
	Encyclopedia:
		Category: Allies/Vehicles
	EncyclopediaExtras:
		Subfaction: france

BATF.AI:
	Inherits: BATF
	RenderSprites:
		Image: batf
	Buildable:
		Prerequisites: atek, ~vehicles.france, ~botplayer, ~techlevel.high
	Cargo:
		InitialUnits: E1,E1,E3,E3,E3
	WithCargoSounds:
		-EnterSounds:
	-Encyclopedia:
	-EncyclopediaExtras:

MEMP:
	Inherits: ^TankTD
	Inherits@IDISABLE: ^DisabledByRadarLoss
	Inherits@HACKABLE: ^Hackable
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 410
		IconPalette: chrometd
		Prerequisites: gtek, ~vehicles.gdi, ~techlevel.high
		Description: Remotely piloted vehicle, deploys to disable nearby vehicles & structures.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses, Buildings
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
		Attributes: • Special Ability: EMP Shockwave\n• Immune to mind control\n• Requires active radar communication
	Valued:
		Cost: 1150
	Tooltip:
		Name: Mobile E.M.P
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 55000
	Armor:
		Type: Heavy
	Selectable:
		Voice: SelectEMP
	Mobile:
		PauseOnCondition: !radarenabled || being-captured || empdisable || being-warped || driver-dead || notmobile
		Speed: 126
		Voice: Move
		TurnSpeed: 28
	Passenger:
		Voice: Move
	GrantTimedConditionOnDeploy:
		DeploySound: mobempcharge1.aud
		DeployedCondition: triggered
		CooldownTicks: 450
		DeployedTicks: 25
		RequiresCondition: radarenabled && !(empdisable || being-warped)
		ChargingColor: 0277bd
		DischargingColor: 03a9f4
		Voice: Unload
		Instant: true
	PeriodicExplosion:
		Weapon: MEMP
		RequiresCondition: triggered && ammo
		AmmoPoolName: explosion
		InitialDelay: 23
	AttackFrontal:
		FacingTolerance: 512
		PauseOnCondition: !ammo || empdisable || being-warped
		Voice: Attack
	Armament@PRIMARY:
		Weapon: MempTargeting
	DeployOnAttack:
		RequiresCondition: !triggered
	AmmoPool:
		Name: explosion
		Armaments: explosion
		ReloadDelay: 0
		InitialAmmo: 0
		AmmoCondition: ammo
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		FullSequence: pip-red
	ReloadAmmoPool:
		AmmoPool: explosion
		Delay: 450
		Count: 1
	ChronoshiftableCA:
		RequiresCondition: !being-warped && !triggered
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithIdleOverlay@charging:
		RequiresCondition: triggered
		Palette: tseffect
		Offset: 160, 120, 100
		Sequence: idle-overlay
	WithIdleOverlay@chargingpower:
		RequiresCondition: triggered
		Palette: tseffect-ignore-lighting-alpha75
		Offset: 0, 0, 0
		Sequence: emp-overlay
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@DriverKillImmune:
		TargetTypes: DriverKillImmune
	Targetable@EmpImmune:
		TargetTypes: EmpImmune
	WithRangeCircle:
		Type: MEMP
		Color: 0000FF80
		Range: 5c0
	Voiced:
		VoiceSet: DroneVoice
		RequiresCondition: radarenabled && !empdisable
	Voiced@OFFLINE:
		VoiceSet: OfflineDroneVoice
		RequiresCondition: !radarenabled || empdisable
	WithColoredOverlay@DRONEDISABLE:
		RequiresCondition: (!radarenabled || empdisable) && !triggered
	WithDecoration@DRONEDISABLE:
		RequiresCondition: (!radarenabled || empdisable) && !triggered
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-warped
	AutoTargetPriority@DEFAULT:
		ValidTargets: Vehicle, Defense
	Encyclopedia:
		Category: GDI/Vehicles

CDRN:
	Inherits: ^VehicleTD
	Inherits@AUTOTARGET: ^AutoTargetGround
	Inherits@HACKABLE: ^Hackable
	Inherits@SELECTION: ^SelectableSupportUnit
	RenderSprites:
		Image: cdrone
	AutoTargetPriority@DEFAULT:
		ValidTargets: Infantry, Vehicle, Water
		InvalidTargets: NoAutoTarget, ChaosImmune
	AutoTargetPriority@ATTACKANYTHING:
		ValidTargets: Infantry, Vehicle, Water
		InvalidTargets: NoAutoTarget, ChaosImmune
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 311
		IconPalette: chrometd
		Prerequisites: stek, ~vehicles.yuri, ~techlevel.high
		Description: Drone armed with Chaos Gas which causes units to become frenzied and attack indiscriminately.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft, Buildings, Defenses
		Attributes: • Special Ability: Expel Chaos Gas
	Valued:
		Cost: 850
	Tooltip:
		Name: Chaos Drone
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 12000
	Armor:
		Type: Light
	Mobile:
		Speed: 135
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	FireWarheadsOnDeath:
		Weapon: ChaosDroneExplodeSmall
	AttackFrontal:
		FacingTolerance: 512
		PauseOnCondition: empdisable || being-warped
		Voice: Attack
	Armament@PRIMARY:
		Weapon: ChaosDroneTargeting
	GrantConditionOnAttack:
		Condition: triggered
	AmmoPool:
		Name: explosion
		Armaments: explosion
		ReloadDelay: 0
		InitialAmmo: 0
		AmmoCondition: ammo
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		FullSequence: pip-red
	ReloadAmmoPool:
		AmmoPool: explosion
		Delay: 100
		Count: 1
	GrantTimedConditionOnDeploy:
		DeployedCondition: triggered
		CooldownTicks: 100
		DeployedTicks: 10
		RequiresCondition: !(empdisable || being-warped)
		Instant: true
	PeriodicExplosion:
		Weapon: ChaosGas
		RequiresCondition: triggered && ammo
		AmmoPoolName: explosion
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Targetable@DriverKillImmune:
		TargetTypes: DriverKillImmune
	Voiced:
		VoiceSet: ChaosDroneVoice
	Sellable:
		RequiresCondition: unit-sellable && !c4 && !being-warped
	WithRangeCircle:
		Type: ChaosDrone
		Range: 3c0
		Color: c33d73
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		Subfaction: yuri

CHPR:
	Inherits: ^Tank
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	RenderSprites:
		Image: chpr
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 222
		IconPalette: chrometd
		Prerequisites: atek, ~vehicles.germany, ~techlevel.high
		Description: Advanced heavy tank that can erase targets from existence.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Defenses, Aircraft
		Weaknesses: • Weak vs Buildings
		Attributes: • Target is disabled while being erased\n• Can crush concrete walls\n• Special Ability: Teleport (with upgrade)
	Valued:
		Cost: 1700
	Tooltip:
		Name: Chrono Prison
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 75000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 44
		Locomotor: heavytracked
		Voice: Move
	Passenger:
		Weight: 2
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 16
		Offset: -156,0,0
	Armament@PRIMARY:
		Weapon: NeutronCannon
		LocalOffset: 600,0,480
	Armament@SECONDARY:
		Weapon: NeutronCannonAA
		LocalOffset: 600,0,480
	Armament@VISUAL:
		Weapon: NeutronCannonBeam
		LocalOffset: 600,0,480
	Armament@VISUALCORE:
		Weapon: NeutronCannonPulse
		LocalOffset: 600,0,480
	Armament@SOUND:
		Weapon: NeutronCannonSound
		LocalOffset: 600,0,480
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret@inactiveturret:
		RequiresCondition: !aiming
		Sequence: turret
	WithSpriteTurret@activeturret:
		RequiresCondition: aiming
		Sequence: turretactive
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Carryable:
		LocalOffset: 0,0,500
	-Crushable:
	GrantConditionWhileAiming:
		Condition: aiming
	Voiced:
		VoiceSet: ChronoPrisonVoice
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AttackMove:
		AssaultMoveCondition: assault-move
		Voice: Attack
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything && !assault-move
		ValidTargets: Temporal
		InvalidTargets: NoAutoTarget, Building, WaterStructure, AntiAirDefense
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything || assault-move
		ValidTargets: Temporal
		InvalidTargets: NoAutoTarget
	SpawnActorOnDeath:
		Actor: CHPR.Husk
		RequiresCondition: !being-warped
	PortableChronoCA:
		ChargeDelay: 375
		MaxDistance: 18
		RequiresCondition: !(empdisable && being-warped) && !chronoshifted && tflx-upgrade
		ShowSelectionBarWhenFull: false
	GrantConditionOnPrerequisite@TEMPORALFLUX:
		Condition: tflx-upgrade
		Prerequisites: tflx.upgrade
	RejectsOrders@AICHPR:
		RequiresCondition: botwarping
	GrantConditionOnAttack@AICHPR:
		Condition: botwarping
		ArmamentNames: primary
		RevokeDelay: 75
		RequiresCondition: botowner
	GrantConditionOnBotOwner@AICHPR:
		Condition: botowner
		Bots: brutal, vhard, hard, normal, easy, naval
	WithIdleOverlay@MINDCONTROL:
		Offset: -200,0,768
	RangeMultiplier@TEMPORALFLUX:
		RequiresCondition: tflx-upgrade
		Modifier: 130
	Encyclopedia:
		Category: Allies/Vehicles
	EncyclopediaExtras:
		Subfaction: germany

CRYO:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	RenderSprites:
	Valued:
		Cost: 1350
	Tooltip:
		Name: Cryo Launcher
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 221
		Prerequisites: ~vehicles.allies, alhq, ~sweden.coalition, ~techlevel.high
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Long-range support artillery that slows enemies and makes them take more damage.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Slows enemy movement and increases damage taken
	Mobile:
		Speed: 72
		TurnSpeed: 16
		Voice: Move
	Passenger:
		Voice: Move
	Health:
		HP: 26000
	Armor:
		Type: Light
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament:
		Weapon: CryoMissile
		LocalOffset: 150,100,150, 150,-100,150
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
		Voice: Attack
	Voiced:
		VoiceSet: CryoVoice
	AttackMove:
		Voice: Attack
	FireWarheadsOnDeath:
		Weapon: CryoExplosion
	Selectable:
		DecorationBounds: 1194, 1194
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Encyclopedia:
		Category: Allies/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Sweden Coalition.

PBUL:
	Inherits: ^VehicleTD
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeAir
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	RenderSprites:
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 252
		IconPalette: chrometd
		Prerequisites: gtek, ~vehicles.eagle, ~techlevel.high
		Description: Fast scout vehicle with rockets that disrupts weapons and vision. Can designates targets, revealing them and increasing the damage they take.
	TooltipExtras:
		Strengths: • Strong vs Light Armor, Aircraft
		Weaknesses: • Weak vs Infantry, Buildings, Defenses
		Attributes: • Can detect spies and cloaked units.\n• Special Ability: Target Painter
	Valued:
		Cost: 850
	Tooltip:
		Name: Pitbull
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 17000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 40
		Speed: 130
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 40
		Offset: -200,0,120
	Armament@PRIMARY:
		Weapon: PitbullRockets
		LocalOffset: 0,-100,250, 0,100,250
		PauseOnCondition: reloading-secondary
		ReloadingCondition: reloading-primary
	Armament@SECONDARY:
		Name: secondary
		Weapon: PitbullRocketsAA
		LocalOffset: 0,-100,250, 0,100,250
		PauseOnCondition: reloading-primary
		ReloadingCondition: reloading-secondary
	Armament@TERTIARY:
		Name: tertiary
		Weapon: TargetPainter
		LocalOffset: 0,0,250
		RequiresCondition: target-painting
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
		Armaments: primary, secondary, tertiary
		TargetFrozenActors: true
	AttackMove:
		Voice: Attack
	WithSpriteTurret:
	Voiced:
		VoiceSet: PitbVoice
	IgnoresDisguise:
	DetectCloaked:
		Range: 6c0
		DetectionTypes: Cloak, Thief, AirCloak
		RequiresCondition: !(empdisable || being-warped)
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	TargetedAttackAbility:
		ActiveCondition: target-painting
		ArmamentNames: tertiary
		CircleColor: ffffff88
		Type: PitbullTargetPainter
		TargetModifiedCursor: ability2
		ActiveUntilCancelled: true
		UseDisabledArmaments: true
		TargetFrozenActors: true
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: eagle

CYCP:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@InfiltratorStolenTech: ^InfiltratorStolenTech
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 501
		Prerequisites: anyradar, ~vehicles.cycp
		Description: Light tank with a Tesla Coil derived weapon.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
	Valued:
		Cost: 1250
	Tooltip:
		Name: Cyclops
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 20000
	Armor:
		Type: Light
	Mobile:
		Speed: 82
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: CyclopsZap
		LocalOffset: 150,0,213
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 20
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1280, 1280
	SpawnActorOnDeath:
		Actor: CYCP.Husk
		RequiresCondition: !being-warped
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Stolen from Soviet War Factory.

BASI:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	Inherits@InfiltratorStolenTech: ^InfiltratorStolenTech
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 502
		Prerequisites: anyradar, ~vehicles.basi
		Description: Stealth tank with an EMP wave weapon.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses
		Weaknesses: • Cannot attack Aircraft\n• No effect on infantry\n• Can be spotted by nearby infantry and defense structures
	Valued:
		Cost: 1350
	Tooltip:
		Name: Basilisk
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 22000
	Armor:
		Type: Light
	Mobile:
		Speed: 100
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: BasiliskPulse
		LocalOffset: 0,0,213
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 20
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1280, 1280
	SpawnActorOnDeath:
		Actor: BASI.Husk
		RequiresCondition: !being-warped
	Targetable@EmpImmune:
		TargetTypes: EmpImmune
	Cloak@NORMAL:
		InitialDelay: 75
		CloakDelay: 76
		CloakSound: trans1.aud
		CloakedCondition: hidden
		UncloakSound: appear1md.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		IsPlayerPalette: false
		UncloakOn: Attack, Unload, Infiltrate, Demolish, Dock, Damage, Heal
		RequiresCondition: !cloak-force-disabled && !being-warped && !empdisable && !driver-dead && !parachute
		PauseOnCondition: invisibility
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden || driver-dead)
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	WithColoredSelectionBox@INVIS:
		RequiresCondition: invisibility || hidden
		ColorSource: Player
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Stolen from GDI Weapons Factory.

MANT:
	Inherits: ^VehicleTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@InfiltratorStolenTech: ^InfiltratorStolenTech
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 503
		Prerequisites: anyradar, ~vehicles.mant
		Description: Tracked vehicle armed with anti-aircraft laser cannons.
		IconPalette: chrometd
	TooltipExtras:
		Strengths: • Strong vs Aircraft
		Weaknesses: • Cannot attack ground targets
		Attributes: • Can detect cloaked aircraft
	Valued:
		Cost: 1000
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 28000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 126
		TurnSpeed: 30
	Tooltip:
		Name: Mantis
	WithMoveAnimation:
		ValidMovementTypes: Horizontal, Vertical, Turn
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: MantisLaser
		LocalOffset: 0,-50,400, 0,50,400
		MuzzleSequence: muzzle
		MuzzlePalette: caneon
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 24
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	DetectCloaked:
		Range: 8c0
		DetectionTypes: AirCloak
		RequiresCondition: empdisable || being-warped
	KeepsDistance:
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Stolen from Nod Airstrip.

VIPR:
	Inherits: ^VehicleTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@HOVERTRAIL: ^HoverTrail
	Inherits@InfiltratorStolenTech: ^InfiltratorStolenTech
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 504
		Prerequisites: anyradar, ~stolentech.wsph
		Description: Medium hover tank.
		IconPalette: chrometd
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Defenses
		Weaknesses: • Weak vs Buildings, Infantry\n• Cannot attack Aircraft
	Valued:
		Cost: 1350
	Tooltip:
		Name: Viper
	Selectable:
		DecorationBounds: 1194, 1194
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 24000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 512
		Speed: 82
		Locomotor: lighthover
	Hovers:
		BobDistance: -25
		RequiresCondition: !empdisable && !being-warped && !driver-dead
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: ViperLaser
		LocalOffset: 750,0,208
		MuzzleSequence: muzzle
		MuzzlePalette: caneon
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	TurretedFloating:
		TurnSpeed: 16
		Offset: 0,0,0
		RealignDelay: 100
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
	GrantConditionOnTerrain@ONWATER:
		TerrainTypes: Water
		Condition: onwater
	KillsSelf@SINK:
		RequiresCondition: onwater && (empdisable || driver-dead)
	Targetable@ONWATER:
		TargetTypes: Water, Ship
		RequiresCondition: onwater
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Stolen from Scrin Warp Sphere.

WOLV:
	Inherits: ^VehicleTD
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Inherits@GYRO: ^GyroStabilizers
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 250
		Prerequisites: gtek, ~vehicles.talon, ~techlevel.medium
		Description: Compact & durable battle-mech with dual chainguns.
		IconPalette: chrometd
	TooltipExtras:
		Strengths: • Strong vs Infantry, Buildings, Light Armor
		Weaknesses: • Weak vs Heavy Armor, Defenses\n• Cannot attack Aircraft
	Valued:
		Cost: 900
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 1024, 1024, 0, -100
	Tooltip:
		Name: Wolverine
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 44000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 64
		Speed: 60
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Voiced:
		VoiceSet: WolverineVoice
	WithMoveAnimation:
	WithAttackAnimation:
		Sequence: shoot
		RequiresCondition: !moving
	GrantConditionOnMovement@MOVING:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: moving
	Armament@PRIMARY:
		Weapon: WolverineGun
		LocalOffset: 300,-220,180, 300,220,180
		MuzzleSequence: muzzle
	Armament@SECONDARY:
		Name: secondary
		Weapon: WolverineGunLine
		LocalOffset: 300,-220,180, 300,220,180
	Armament@TERTIARY:
		Name: secondary
		Weapon: WolverineGunTracer
		LocalOffset: 300,-220,180, 300,220,180
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
		FacingTolerance: 32
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	WithMuzzleOverlay:
	RangeMultiplier@GYRO:
		Modifier: 140
	ReloadDelayMultiplier@GYRO:
		Modifier: 140
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: talon

XO:
	Inherits: ^VehicleTD
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 251
		Prerequisites: gtek, ~vehicles.zocom, ~techlevel.medium
		Description: Light battle-mech armed with a laser and a coil-gun. Able to jump short distances using its jump-pack.
	TooltipExtras:
		Strengths: • Strong vs Light Armor, Heavy Armor, Buildings
		Weaknesses: • Weak vs Defenses\n• Cannot attack Aircraft
		Attributes: • Special Ability: Jump-Pack
	Valued:
		Cost: 1000
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 1024, 1024, 0, -100
	Tooltip:
		Name: X-O Powersuit
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 22500
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 64
		Speed: 60
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Voiced:
		VoiceSet: XOVoice
	WithMoveAnimation:
	WithAttackAnimation:
		Sequence: shoot
		RequiresCondition: !moving
	GrantConditionOnMovement@MOVING:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: moving
	Armament@PRIMARY:
		Weapon: XOCoilGun
		LocalOffset: 400,-220,180
		MuzzleSequence: muzzle
	Armament@SECONDARY:
		Name: secondary
		Weapon: XOLaser
		LocalOffset: 400,220,180
		MuzzleSequence: muzzle2
		MuzzlePalette: scrin
	AttackFollowFrontal:
		PauseOnCondition: empdisable || being-warped || blinded || parachute
		Voice: Attack
		FacingTolerance: 32
		RangeMargin: 0
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	WithMuzzleOverlay:
	Parachutable:
		FallRate: 130
	WithIdleAnimation@DESCENDING:
		RequiresCondition: parachute
		Interval: 3
		Sequences: descend
	WithParachute:
		ShadowImage: parach-shadow
		ShadowSequence: idle
		Image: empty
		Sequence: idle
		OpeningSequence: idle
		Offset: 0,0,427
		RequiresCondition: parachute
	GpsRadarDot:
		Sequence: Infantry
	WithFacingSpriteBody:
		RequiresCondition: !jumping
	WithFacingSpriteBody@Jump:
		Name: jump
		Sequence: descend
		RequiresCondition: !being-warped && jumping
	TargetedLeapAbility:
		TakeOffSounds: xo-jump1.aud
		LandingSounds: xo-land1.aud
		LeapCondition: jumping
		ShowSelectionBarWhenFull: false
		ChargeDelay: 625
		SelectionBarColor: ffaa00
		CircleColor: ffaa0077
		MaxDistance: 7
		Speed: 140
	Contrail@Jumping:
		Offset: -30,0,350
		StartColorUsePlayerColor: false
		ZOffset: -128
		StartColor: ff990090
		StartColorAlpha: 128
		TrailLength: 12
		RequiresCondition: jumping
	WithShadow@Jump:
		RequiresCondition: jumping
	Targetable@Jump:
		RequiresCondition: jumping && !being-warped
		TargetTypes: AirSmall
	Targetable@TEMPORAL:
		TargetTypes: Temporal
		RequiresCondition: !jumping
	DamageMultiplier@Jumping:
		Modifier: 50
		RequiresCondition: jumping
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		Subfaction: zocom

APOC:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMovePrioritizeGround
	Inherits@BERSERK: ^Berserk
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	Inherits@ATOMICAMMO: ^AtomicAmmunition
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 239
		Prerequisites: stek, ~vehicles.soviet, ~!atomicengines.upgrade, ~!erad.upgrade, ~apoc.upgrade, ~techlevel.high
		Description: Enormous slow tank with uranium shells and anti-air capability.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Attributes: • Can crush smaller vehicles and concrete walls\n• Self repairs to 50% out of combat
	Valued:
		Cost: 2600
	Tooltip:
		Name: Apocalypse Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 125000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 43
		TurnSpeed: 8
		Locomotor: sheavytracked
		Voice: Move
	Passenger:
		Weight: 2
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 8
	Armament@PRIMARY:
		Weapon: 152mm
		Recoil: 171
		RecoilRecovery: 30
		LocalOffset: 1400,100,340, 1400,-100,340
		MuzzleSequence: muzzle
		PauseOnCondition: has-atomic-ammo || atomic-reloading
		ReloadingCondition: reloading
	Armament@ATOMICAMMO:
		Weapon: 152mmAtomic
		Recoil: 171
		RecoilRecovery: 30
		LocalOffset: 1400,100,340, 1400,-100,340
		MuzzleSequence: muzzle
		PauseOnCondition: !has-atomic-ammo || reloading
		ReloadingCondition: atomic-reloading
	Armament@SECONDARY:
		Name: secondary
		Weapon: ApocalypseTusk
		LocalOffset: -85,384,340, -85,-384,340
		LocalYaw: -100,100
		Recoil: 43
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	WithFacingSpriteBody:
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: APOC.Husk
		RequiresCondition: !being-warped
	ChangesHealth:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 50
		DamageCooldown: 150
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Carryable:
		LocalOffset: 0,0,500
	WithIdleOverlay@MINDCONTROL:
		Offset: 0,0,512
	-Crushable:
	Voiced:
		VoiceSet: ApocalypseVoice
	AttackMove:
		Voice: Attack
	WithRestartableIdleOverlay@ATOMICAMMO:
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	WithAmmoPipsDecoration@ATOMICAMMO:
		PipCount: 3
	AmmoPool@ATOMICAMMO:
		Ammo: 6
	ReloadAmmoPool@ATOMICAMMO:
		Count: 6
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	ReplacedInQueue:
		Actors: apoc.atomic, apoc.erad
	Upgradeable@ATOMIC:
		Type: atomicengines.upgrade
		UpgradingCondition: upgrading
		Actor: apoc.atomic
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@ERAD:
		Type: erad.upgrade
		UpgradingCondition: upgrading
		Actor: apoc.erad
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Armor Doctrine.

APOC.ATOMIC:
	Inherits: APOC
	RenderSprites:
		Image: apoci
	Tooltip:
		Name: Atomic Apocalypse Tank
		GenericName: Tank
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 240
		Prerequisites: stek, ~vehicles.soviet, ~atomicengines.upgrade, ~!erad.upgrade, ~apoc.upgrade, ~techlevel.high
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Attributes: • Can crush smaller vehicles and concrete walls\n• Self repairs to 50% out of combat\n• Explodes and leaves radiation on death
	WithFacingSpriteBody:
	SpeedMultiplier:
		Modifier: 125
	FireWarheadsOnDeath@ATOMICUPGRADE:
		Weapon: UnitExplodeIraqTank
		EmptyWeapon: UnitExplodeIraqTank
		RequiresCondition: !being-warped
	-SpawnActorOnDeath:
	ReplacedInQueue:
		Actors: apoc.erad.atomic
	Upgradeable@ERAD:
		Actor: apoc.erad.atomic
	-Upgradeable@ATOMIC:

APOC.ERAD:
	Inherits: APOC
	Tooltip:
		Name: Apocalyptic Eradicator
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Heavy Armor
		Weaknesses: • Cannot attack Aircraft
	Buildable:
		Prerequisites: stek, ~vehicles.soviet, ~!atomicengines.upgrade, ~erad.upgrade, ~apoc.upgrade, ~techlevel.high
		BuildPaletteOrder: 241
		Description: Enormous slow tank with twin heavy radiation cannons.
	Armament@PRIMARY:
		Weapon: ApocRadBeamWeapon
		MuzzlePalette: caneon
		LocalOffset: 500,100,240, 500,-100,240
		-PauseOnCondition:
		-ReloadingCondition:
	-Armament@SECONDARY:
	-Armament@ATOMICAMMO:
	-ExternalCondition@ATOMICAMMO:
	-WithRestartableIdleOverlay@ATOMICAMMO:
	-FirepowerMultiplier@ATOMICAMMO:
	-Targetable@ATOMICAMMO:
	-AmmoPool@ATOMICAMMO:
	-WithAmmoPipsDecoration@ATOMICAMMO:
	-ReloadAmmoPool@ATOMICAMMO:
	-ReloadAmmoPoolCA@ATOMICAMMODECAY:
	-DamagedByTintedCells@RADSTRONG:
	-DamagedByTintedCells@RADMED:
	SpawnActorOnDeath:
		Actor: APOC.ERAD.Husk
		RequiresCondition: !being-warped
	ReplacedInQueue:
		Actors: apoc.erad.atomic
	Upgradeable@ATOMIC:
		Actor: apoc.erad.atomic
	-Upgradeable@ERAD:
	Voiced:
		VoiceSet: EradVoice
	EncyclopediaExtras:
		Subfaction: iraq

APOC.ERAD.ATOMIC:
	Inherits: APOC.ATOMIC
	RenderSprites:
		Image: apoc.eradi
	Tooltip:
		Name: Atomic Apocalyptic Eradicator
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Heavy Armor
		Weaknesses: • Cannot attack Aircraft
	Buildable:
		Prerequisites: stek, ~vehicles.soviet, ~atomicengines.upgrade, ~erad.upgrade, ~apoc.upgrade, ~techlevel.high
		BuildPaletteOrder: 242
		Description: Enormous slow tank with twin heavy radiation cannons.
	Armament@PRIMARY:
		Weapon: ApocRadBeamWeapon
		MuzzlePalette: caneon
		LocalOffset: 500,100,240, 500,-100,240
		-PauseOnCondition:
		-ReloadingCondition:
	-Armament@SECONDARY:
	-Armament@ATOMICAMMO:
	-ExternalCondition@ATOMICAMMO:
	-WithRestartableIdleOverlay@ATOMICAMMO:
	-FirepowerMultiplier@ATOMICAMMO:
	-Targetable@ATOMICAMMO:
	-AmmoPool@ATOMICAMMO:
	-WithAmmoPipsDecoration@ATOMICAMMO:
	-ReloadAmmoPool@ATOMICAMMO:
	-ReloadAmmoPoolCA@ATOMICAMMODECAY:
	-DamagedByTintedCells@RADSTRONG:
	-DamagedByTintedCells@RADMED:
	-WithDecoration@UpgradeOverlay:
	-ReplacedInQueue:
	-Upgradeable@ERAD:
	Voiced:
		VoiceSet: EradVoice
	EncyclopediaExtras:
		Subfaction: iraq

OVLD:
	Inherits: ^Tank
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	Inherits@ATOMICAMMO: ^AtomicAmmunition
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Inherits@UpgradeOverlay: ^UpgradeOverlay
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 235
		Prerequisites: stek, ~vehicles.soviet, ~ovld.upgrade, ~!erad.upgrade, ~!atomicengines.upgrade, ~techlevel.high
		Description: Enormous slow tank fitted with a commissar tower which supports infantry in combat.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Can crush smaller vehicles and concrete walls\n• Self repairs to 50% out of combat\n• Empowers nearby basic infantry
	Valued:
		Cost: 2350
	Tooltip:
		Name: Overlord Tank
		GenericName: Tank
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 125000
	Armor:
		Type: Heavy
	Mobile:
		Speed: 43
		TurnSpeed: 12
		Locomotor: sheavytracked
		Voice: Move
	Passenger:
		Weight: 2
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 10
	Armament@PRIMARY:
		Weapon: 135mm
		Recoil: 171
		RecoilRecovery: 30
		LocalOffset: 1400,300,340, 1400,-300,340
		MuzzleSequence: muzzle
		PauseOnCondition: has-atomic-ammo || atomic-reloading
		ReloadingCondition: reloading
	Armament@ATOMICAMMO:
		Weapon: 135mmAtomic
		Recoil: 171
		RecoilRecovery: 30
		LocalOffset: 1400,100,340, 1400,-100,340
		MuzzleSequence: muzzle
		PauseOnCondition: !has-atomic-ammo || reloading
		ReloadingCondition: atomic-reloading
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
		Voice: Attack
	WithFacingSpriteBody:
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: OVLD.Husk
		RequiresCondition: !being-warped
	ChangesHealth:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 50
		DamageCooldown: 150
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1877, 1621, 0, -170
	Carryable:
		LocalOffset: 0,0,500
	WithIdleOverlay@MINDCONTROL:
		Offset: 0,0,512
	-Crushable:
	Voiced:
		VoiceSet: OverlordVoice
	AttackMove:
		Voice: Attack
	WithRestartableIdleOverlay@ATOMICAMMO:
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	WithAmmoPipsDecoration@ATOMICAMMO:
		PipCount: 3
	AmmoPool@ATOMICAMMO:
		Ammo: 6
	ReloadAmmoPool@ATOMICAMMO:
		Count: 6
		RequiresCondition: atomic-ammo && has-atomic-ammo < 6
	ProximityExternalCondition@InspirationOvld:
		Range: 6c0
		Condition: ovld-inspiration
		ValidRelationships: Ally
		AffectsParent: true
	WithRadiatingCircle:
		EndRadius: 6c0
		Interval: 75
		Duration: 50
		ValidRelationships: Ally
		AlwaysShowMaxRange: true
	ReplacedInQueue:
		Actors: ovld.atomic, ovld.erad
	Upgradeable@ATOMIC:
		Type: atomicengines.upgrade
		UpgradingCondition: upgrading
		Actor: ovld.atomic
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Upgradeable@ERAD:
		Type: erad.upgrade
		UpgradingCondition: upgrading
		Actor: ovld.erad
		UpgradeAtActors: fix, rep, srep
		BuildDuration: 100
		RequiresCondition: !mindcontrolled
	Encyclopedia:
		Category: Soviets/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Infantry Doctrine.

OVLD.ATOMIC:
	Inherits: OVLD
	Tooltip:
		Name: Atomic Overlord Tank
	Buildable:
		Prerequisites: stek, ~vehicles.soviet, ~ovld.upgrade, ~!erad.upgrade, ~atomicengines.upgrade, ~techlevel.high
		BuildPaletteOrder: 236
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Can crush smaller vehicles and concrete walls\n• Self repairs to 50% out of combat\n• Empowers nearby basic infantry\n• Explodes and leaves radiation on death
	RenderSprites:
		Image: ovldi
	ReplacedInQueue:
		Actors: ovld.erad.atomic
	Upgradeable@ERAD:
		Actor: ovld.erad.atomic
	-Upgradeable@ATOMIC:
	FireWarheadsOnDeath@ATOMICENGINES:
		Weapon: UnitExplodeIraqTank
		EmptyWeapon: UnitExplodeIraqTank
		RequiresCondition: !being-warped
	SpeedMultiplier:
		Modifier: 125
	-SpawnActorOnDeath:

OVLD.ERAD:
	Inherits: OVLD
	Tooltip:
		Name: Overlord Eradicator
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Heavy Armor
	Buildable:
		Prerequisites: stek, ~vehicles.soviet, ~ovld.upgrade, ~erad.upgrade, ~!atomicengines.upgrade, ~techlevel.high
		BuildPaletteOrder: 237
		Description: Enormous slow tank equipped with radiation cannons and a\ncommissar tower which supports infantry in combat.
	Armament@PRIMARY:
		Weapon: OverlordRadBeamWeapon
		MuzzlePalette: caneon
		-PauseOnCondition:
		-ReloadingCondition:
		LocalOffset: 260,300,240, 260,-300,240
	-Armament@ATOMICAMMO:
	-ExternalCondition@ATOMICAMMO:
	-WithRestartableIdleOverlay@ATOMICAMMO:
	-FirepowerMultiplier@ATOMICAMMO:
	-Targetable@ATOMICAMMO:
	-AmmoPool@ATOMICAMMO:
	-WithAmmoPipsDecoration@ATOMICAMMO:
	-ReloadAmmoPool@ATOMICAMMO:
	-ReloadAmmoPoolCA@ATOMICAMMODECAY:
	-DamagedByTintedCells@RADSTRONG:
	-DamagedByTintedCells@RADMED:
	ReplacedInQueue:
		Actors: ovld.erad.atomic
	Upgradeable@ATOMIC:
		Actor: ovld.erad.atomic
	-Upgradeable@ERAD:
	Voiced:
		VoiceSet: EradVoice
	EncyclopediaExtras:
		Subfaction: iraq

OVLD.ERAD.ATOMIC:
	Inherits: OVLD.ATOMIC
	Tooltip:
		Name: Atomic Overlord Eradicator
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor, Heavy Armor
	Buildable:
		Prerequisites: stek, ~vehicles.soviet, ~ovld.upgrade, ~erad.upgrade, ~atomicengines.upgrade, ~techlevel.high
		BuildPaletteOrder: 238
		Description: Enormous slow tank equipped with radiation cannons and a\ncommissar tower which supports infantry in combat.
	Armament@PRIMARY:
		Weapon: OverlordRadBeamWeapon
		MuzzlePalette: caneon
		-PauseOnCondition:
		-ReloadingCondition:
		LocalOffset: 260,300,240, 260,-300,240
	-Armament@ATOMICAMMO:
	-ExternalCondition@ATOMICAMMO:
	-WithRestartableIdleOverlay@ATOMICAMMO:
	-FirepowerMultiplier@ATOMICAMMO:
	-Targetable@ATOMICAMMO:
	-AmmoPool@ATOMICAMMO:
	-WithAmmoPipsDecoration@ATOMICAMMO:
	-ReloadAmmoPool@ATOMICAMMO:
	-ReloadAmmoPoolCA@ATOMICAMMODECAY:
	-DamagedByTintedCells@RADSTRONG:
	-DamagedByTintedCells@RADMED:
	RenderSprites:
		Image: ovld.eradi
	-WithDecoration@UpgradeOverlay:
	-ReplacedInQueue:
	-Upgradeable@ERAD:
	Voiced:
		VoiceSet: EradVoice
	EncyclopediaExtras:
		Subfaction: iraq

THWK:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SLOWCRUSH: ^SlowedByCrushing
	RenderSprites:
	Valued:
		Cost: 1850
	Tooltip:
		Name: Tomahawk Launcher
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 260
		Prerequisites: gtek, ~thwk.upgrade, ~techlevel.high
		Queue: VehicleSQ, VehicleMQ
		IconPalette: chrometd
		Description: Extreme long-range missile artillery.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets\n• Cannot fire while moving\n• Rockets can be shot down by static anti-air defenses
	Mobile:
		Speed: 44
		Voice: Move
		PauseOnCondition: launching || aiming || being-captured || empdisable || being-warped || driver-dead || notmobile
	Passenger:
		Voice: Move
	Health:
		HP: 22000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Turreted:
		TurnSpeed: 4
		RealignDelay: 0
	Armament@TARGETTER:
		Name: targetter
		Weapon: THTargetter
	Armament@PRIMARY:
		Weapon: THLauncher
		PauseOnCondition: moving
	AttackTurreted:
		TargetFrozenActors: True
		Voice: Attack
		PauseOnCondition: empdisable || being-warped || blinded
		Armaments: primary, targetter
	AttackMove:
		Voice: Attack
	WithSpriteTurret@OneMissile:
		RequiresCondition: loaded
	WithSpriteTurret@NoMissile:
		RequiresCondition: !loaded
		Sequence: turret-empty
	MissileSpawnerMaster:
		Actors: TH
		RespawnTicks: 235
		LoadedCondition: loaded
		LaunchingCondition: launching
		SpawnOffset: 100,426,512
		PauseOnCondition: moving
	WithSpawnerMasterPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	GrantConditionOnAttack@AIMING:
		ArmamentNames: primary, targetter
		Condition: aiming
		RevokeDelay: 15
		RevokeAll: false
	GrantConditionOnMovement@MOVING:
		ValidMovementTypes: Horizontal, Vertical, Turn
		Condition: moving
	FireWarheadsOnDeath:
		Weapon: BarrelExplode
	Voiced:
		VoiceSet: ThwkVoice
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Selectable:
		DecorationBounds: 1194, 1194
	Encyclopedia:
		Category: GDI/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Bombardment Strategy.

TH:
	Inherits: ^ShootableMissile
	RenderSprites:
		Palette: temptd
	Valued:
		Cost: 50
	Tooltip:
		Name: Tomahawk Missile
	Health:
		HP: 5000
	CruiseMissile:
		LaunchAngle: 120
		Speed: 165
		MaxAltitude: 2c511
		MinAirborneAltitude: 256
		AirborneCondition: airborne
		TrackTarget: true
		MaxTargetMovement: 2c0
	LeavesTrailsCA:
		Image: icbmsmoke
		Sequences: idle, idle2, idle3, idle4
		MovingInterval: 2
		Type: CenterPosition
		Offsets: -200, 0, -50
	MissileSpawnerSlave:
	SpawnedExplodes:
		Weapon: THWeapon
		EmptyWeapon: VisualExplodeHusk
		RequiresCondition: !airborne
	FireWarheadsOnDeath:
		Weapon: THExplodeAirborne
		RequiresCondition: airborne
	FireProjectilesOnDeath@Debris:
		Weapons: SmallDebris
		Pieces: 3, 5
		Range: 1c511, 3c0
		RequiresCondition: airborne

BSKY:
	Inherits: ^ShootableMissile
	RenderSprites:
		Image: bsky
	Valued:
		Cost: 50
	Tooltip:
		Name: Black Sky Missile
	Health:
		HP: 5000
	GuidedMissile:
		Speed: 430
		MaxTargetMovementTicks: 160
		MinAltitude: 128
	LeavesTrailsCA:
		Image: icbmsmoke
		Sequences: idle, idle2, idle3, idle4
		MovingInterval: 1
		Type: CenterPosition
		Offsets: -200, 0, -50
	RevealsShroud:
		Range: 3c512
		Type: GroundPosition
	FireWarheadsOnDeath:
		Weapon: BSKY
	WithEnterExitWorldOverlay:
		Image: smigboom
		EnterSequence: enter
		Palette: effect

ZEUS:
	Inherits: ^Vehicle
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Valued:
		Cost: 2000
	Tooltip:
		Name: Zeus Artillery
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 224
		Prerequisites: ~vehicles.allies, alhq, ~greece.coalition, ~techlevel.high
		Description: Extreme long range artillery that can generate localized storm clouds.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Defenses
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets\n• Cannot fire while moving
	Selectable:
		DecorationBounds: 1280, 1280
	Health:
		HP: 20000
	Armor:
		Type: Light
	Mobile:
		Speed: 46
		Voice: Move
	Passenger:
		Voice: Move
	RevealsShroud:
		Range: 5c0
	Armament:
		Weapon: ZeusCloud
	AttackFrontalCharged:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 16
		ChargeLevel: 75
		DischargeRate: 5
		ShotsPerCharge: 1
		ShowSelectionBar: true
		SelectionBarColor: d5cfff
		ChargingCondition: charging
		Voice: Attack
		LosesChargeWhileTurning: true
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	AttackMove:
		Voice: Attack
	AmbientSoundCA:
		RequiresCondition: charging
		SoundFiles: zeuscharge.aud
	WithIdleOverlay@Active:
		Sequence: active-overlay
		RequiresCondition: charging
		Offset: -256,0,256
		IsDecoration: True
	WithFacingSpriteBody:
		RequiresCondition: !charging
	WithFacingSpriteBody@Active:
		Name: body-charging
		Sequence: shoot
		RequiresCondition: charging
	Voiced:
		VoiceSet: ZeusVoice
	Encyclopedia:
		Category: Allies/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Greece Coalition.

MOLE:
	Inherits: ^Vehicle-NOUPG
	-Voiced:
	-AttackMove:
	-Guard:
	-Repairable:
	-Capturable:
	-CaptureNotification:
	-Parachutable:
	-WithParachute:
	-WithFacingSpriteBody:
	-ChronoshiftableCA:
	-HealthCapDamageMultiplier@CHRONO:
	-Carryable:
	-GrantCondition@CarriedImmobile:
	Tooltip:
		Name: Subterranean APC
	Valued:
		Cost: 2000
	Armor:
		Type: Light
	Health:
		HP: 52000
	RevealsShroud:
		Range: 6c0
	WithSpriteBody@Unburrowing:
		Name: unburrowing
		StartSequence: unburrow
		RequiresCondition: unburrowing
	WithSpriteBody@Arrived:
		RequiresCondition: !burrowing && !being-warped && !unburrowing
	WithSpriteBody@Warped:
		Name: warped
		Sequence: paused
		RequiresCondition: !burrowing && being-warped && !unburrowing
	WithSpriteBody@Burrowing:
		StartSequence: burrow
		Sequence: empty
		RequiresCondition: burrowing
		Name: burrowing
	FireWarheadsOnDeath:
		RequiresCondition: !burrowing && !being-warped
	KillsSelf:
		RemoveInstead: true
		Delay: 35
		RequiresCondition: burrowing
	GrantTimedCondition@Unburrow:
		Condition: unburrowing
		Duration: 30
	GrantCondition@Unload:
		Condition: unload
	GrantDelayedCondition@Burrow:
		Delay: 200
		Condition: burrowing
		PauseOnCondition: being-warped
	ActorLostNotification:
		RequiresCondition: !burrowing
	-Mobile:
	Immobile:
	RejectsOrders:
	Targetable@MindControlImmune:
		TargetTypes: MindControlImmune
	Targetable@ChaosImmune:
		TargetTypes: ChaosImmune
	Cargo:
		InitialUnits: acol, acol, acol, n3, n4
		BeforeUnloadDelay: 100
	WithCargoSounds:
		ExitSounds: gexit1a.aud
	UnloadOnCondition@Unload:
		RequiresCondition: unload
		BotOnly: False
	Targetable:
		RequiresCondition: !being-warped && !burrowing && !unburrowing
	Targetable@C4Plantable:
		RequiresCondition: !being-warped && !burrowing && !unburrowing
	Targetable@TNTPlantable:
		RequiresCondition: !being-warped && !burrowing && !unburrowing
	Targetable@REPAIR:
		RequiresCondition: !being-warped && damaged && !repair-cooldown && !burrowing && !unburrowing
	PeriodicExplosion@Unburrow:
		Weapon: SubterraneanAPCUnburrow
		RequiresCondition: !being-warped
		ResetReloadWhenEnabled: false
	PeriodicExplosion@Burrow:
		InitialDelay: 200
		Weapon: SubterraneanAPCBurrow
		RequiresCondition: !being-warped
		ResetReloadWhenEnabled: false
	SpawnActorOnDeath:
		Actor: MOLE.Husk
		RequiresCondition: !being-warped

MOLE.UPG:
	Inherits: MOLE
	RenderSprites:
		Image: mole
	Cargo:
		InitialUnits: tplr, tplr, tplr, n3c, n5

AVTR:
	Inherits: ^TankTD
	Inherits@GAINSEXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@BERSERK: ^Berserk
	Inherits@SHRAPNEL: ^ThrowsShrapnel
	Inherits@TANKBUSTERVULN: ^TankBusterVulnerability
	Buildable:
		Queue: VehicleSQ, VehicleMQ
		BuildPaletteOrder: 273
		Prerequisites: ~vehicles.nod, tmpp, advcyber.upgrade, ~wrath.covenant, ~techlevel.high
		Description: Heavy mech armed with a powerful laser and a flamethrower.
		IconPalette: chrometd
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Can crush smaller vehicles and concrete walls\n• Self repairs to 50% out of combat
	Valued:
		Cost: 2000
	Tooltip:
		Name: Avatar
	Selectable:
		Bounds: 1280, 1900, 0, -250
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 78000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 18
		Speed: 56
		Locomotor: sheavytracked
		Voice: Move
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: AvatarLaser
		LocalOffset: 850,400,350
		MuzzleSequence: muzzle
		MuzzlePalette: caneon
		RequiresCondition: !quantum-upgrade
	Armament@PRIMARYUPG:
		Name: primary-upg
		Weapon: AvatarLaser.Adv
		LocalOffset: 850,400,350
		MuzzleSequence: muzzle
		MuzzlePalette: caneon
		RequiresCondition: quantum-upgrade
	Armament@SECONDARY:
		Name: secondary
		Weapon: AvatarFlamer
		LocalOffset: 550,200,700
		MuzzleSequence: muzzle-flame
		MuzzlePalette: tdeffect
		RequiresCondition: !blacknapalm-upgrade
	Armament@SECONDARYUPG:
		Name: secondary
		Weapon: AvatarFlamer.UPG
		MuzzleSequence: muzzle-black
		MuzzlePalette: scrin-ignore-lighting-alpha85
		LocalOffset: 550,200,700
		RequiresCondition: blacknapalm-upgrade
	Armament@FF:
		Name: secondary
		Weapon: AvatarFlamerFF
		LocalOffset: 550,200,700
		RequiresCondition: !blacknapalm-upgrade
	Armament@FFUPG:
		Name: secondary
		Weapon: AvatarFlamerFF.UPG
		LocalOffset: 550,200,700
		RequiresCondition: blacknapalm-upgrade
	AmbientSoundCA@3:
		SoundFiles: bigflamer-loop1.aud
		InitialSound: bigflamer-start1.aud
		FinalSound: bigflamer-end1.aud
		RequiresCondition: flaming
		InitialSoundLength: 24
	GrantConditionOnAttack@Flaming:
		ArmamentNames: secondary
		Condition: flaming
		RevokeDelay: 10
	AttackFollowFrontal:
		Armaments: primary, primary-upg, secondary
		TargetFrozenActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 64
		Voice: Attack
		MustFaceTarget: true
	WithMuzzleOverlay:
	ProducibleWithLevel:
		Prerequisites: vehicles.upgraded
	WithProductionIconOverlay:
		Types: Veterancy
		Prerequisites: vehicles.upgraded
	Voiced:
		VoiceSet: AvatarVoice
	SpawnActorOnDeath:
		Actor: AVTR.Husk
		RequiresCondition: !being-warped
	WithFacingSpriteBody:
		Sequence: bodyidle
	WithFacingSpriteBody@LegsIdle:
		Sequence: legsidle
		Name: legs
		RequiresCondition: !moving && !movecooldown
	WithFacingSpriteBody@LegsWalk:
		Sequence: legsmove
		Name: legsmove
		RequiresCondition: moving || movecooldown
	WithMoveAnimation@BodyMove:
		ValidMovementTypes: Horizontal, Vertical
		RequiresCondition: !unfolding
		MoveSequence: bodymove
		Body: body
	WithAttackAnimation@primary:
		Sequence: bodyshoot
		Armament: primary
	WithAttackAnimation@primary-upg:
		Sequence: bodyshoot
		Armament: primary-upg
	WithEnabledAnimation:
		Sequence: bodymake
	GrantConditionOnMovement@Moving:
		Condition: moving
		ValidMovementTypes: Horizontal, Vertical, Turn
	GrantTimedCondition@StoppedMoving:
		Condition: movecooldown
		Duration: 3
		RequiresCondition: !moving
	GrantTimedCondition@Make:
		Condition: unfolding
		Duration: 40
	ChangesHealth:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 50
		DamageCooldown: 150
	GrantConditionOnPrerequisite@UPG:
		Condition: blacknapalm-upgrade
		Prerequisites: blacknapalm.upgrade
	GrantConditionOnPrerequisite@QUANTUM:
		Condition: quantum-upgrade
		Prerequisites: quantum.upgrade
	Encyclopedia:
		Category: Nod/Vehicles
	EncyclopediaExtras:
		AdditionalInfo: Requires Wrath Covenant.
