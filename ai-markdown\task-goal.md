# Ziel: Neue Skirmish-AI „serious“ auf Basis der Easy-AI (<PERSON><PERSON><PERSON>, mit Debug-Ausgaben)

## Schritt 1: Relevante Dateien analysieren
- Öffne `mods/combined-arms/rules/ai.yaml`.
- <PERSON><PERSON> den Block für die „Easy“-Skirmish-AI.

## Schritt 2: Easy-AI klonen und anpassen
- Kopiere den gesamten Block der Easy-AI und füge ihn als neuen Block „serious“ ein.
- Passe die folgenden Parameter an, um die KI leichter zu machen:
  - `SquadSize`: Reduziere den Wert um 1–2 Einheiten.
  - `RushInterval`: <PERSON>r<PERSON><PERSON><PERSON> den Wert, damit Angriffe seltener erfolgen.
  - Optional: Setze `MinimumAttackForce` niedriger als bei Easy.
- Beispiel:
    ```yaml
    serious:
    Name: Serious
    SquadSize: 5
    RushInterval: 900
    MinimumAttackForce: 3
    # weitere Parameter wie bei Easy
    ```

## Schritt 3: Debug-Ausgaben einbauen
- <PERSON><PERSON> in `mods/combined-arms/logic/` nach C#-<PERSON><PERSON><PERSON>, die von der Easy-AI verwendet werden (siehe ggf. `bot-modules.yaml`).
- Ergänze in mindestens einem relevanten BotModule (z. B. beim Start eines Angriffs oder beim Bau einer Einheit) eine Debug-Ausgabe:
    ```csharp
    if (Game.Settings.Debug.BotDebug)
    BotDebug("Serious AI: startet Angriff mit {0} Einheiten", currentSquad.Count);
    ```

- Stelle sicher, dass Debug-Ausgaben nur für die neue „serious“-KI erfolgen.

## Schritt 4: Dokumentation nutzen
- Konsultiere die OpenRA-Dokumentation ([https://docs.openra.net/](https://docs.openra.net/)) für YAML-Syntax, Traits und BotModule-Referenzen.
- Prüfe die lokale Datei `bot-modules.yaml` für eine Übersicht der verwendeten Module.

## Schritt 5: Änderungen dokumentieren
- Erstelle oder ergänze eine Datei `CHANGES_SERIOUS_AI.md` mit einer Liste aller Anpassungen und Debug-Ausgaben.

## Schritt 6: Build vorbereiten
- Speichere alle Änderungen.
- Folge der Anleitung in `BUILD_AND_TEST_SERIOUS_AI.md` (siehe nächste Datei).
