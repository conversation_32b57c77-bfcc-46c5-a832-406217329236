^Palettes:
	OverlayPlayerColorPalette@RAUNIT:
		BasePalette: player
		BaseName: player-twotonenod
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
		PlayerColors:
			Nod: E6E6FF
			Nod1: E6E6FF
			Nod2: E6E6FF
			Nod3: E6E6FF
	OverlayPlayerColorPalette@TDUNIT:
		BasePalette: temptd
		BaseName: playertd-twotonenod
		RemapIndex: 176, 178, 180, 182, 184, 186, 189, 191, 177, 179, 181, 183, 185, 187, 188, 190
		PlayerColors:
			Nod: E6E6FF
			Nod1: E6E6FF
			Nod2: E6E6FF
			Nod3: E6E6FF
	OverlayPlayerColorPalette@SCRINUNIT:
		BasePalette: playerscrin
		BaseName: playerscrin-twotonenod
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
		PlayerColors:
			Nod: E6E6FF
			Nod1: E6E6FF
			Nod2: E6E6FF
			Nod3: E6E6FF

^Vehicle-NOUPG:
	RenderSprites:
		PlayerPalette: player-twotonenod

^Plane:
	RenderSprites:
		PlayerPalette: player-twotonenod

^Helicopter:
	RenderSprites:
		PlayerPalette: player-twotonenod

^BasicHusk:
	RenderSprites:
		PlayerPalette: player-twotonenod

^TDPalette:
	RenderSprites:
		PlayerPalette: playertd-twotonenod

^ScrinUnitPalette:
	RenderSprites:
		PlayerPalette: playerscrin-twotonenod

^Infantry:
	RenderSprites:
		PlayerPalette: playertd-twotonenod
	WithDeathAnimation:
		DeathSequencePalette: playertd-twotonenod
		CrushedSequencePalette: playertd-twotonenod

^ScrinInfantry:
	RenderSprites:
		PlayerPalette: playerscrin-twotonenod
	WithDeathAnimation:
		DeathSequencePalette: playerscrin-twotonenod
		CrushedSequencePalette: playerscrin-twotonenod

SHAD:
	RenderSprites:
		PlayerPalette: playertd
	WithDeathAnimation:
		DeathSequencePalette: playertd

TPLR:
	RenderSprites:
		PlayerPalette: playertd
	WithDeathAnimation:
		DeathSequencePalette: playertd

RMBC:
	RenderSprites:
		PlayerPalette: playertd
	WithDeathAnimation:
		DeathSequencePalette: playertd

ENLI:
	RenderSprites:
		PlayerPalette: playertd
	WithDeathAnimation:
		DeathSequencePalette: playertd

REAP:
	RenderSprites:
		PlayerPalette: playertd
	WithDeathAnimation:
		DeathSequencePalette: playertd

^BuildingTD:
	RenderSprites:
		PlayerPalette: playertd

^DefenseTD:
	RenderSprites:
		PlayerPalette: playertd

SAPC:
	RenderSprites:
		PlayerPalette: player-twotonenod

HARV:
	RenderSprites:
		PlayerPalette: player

HARV.Husk:
	RenderSprites:
		PlayerPalette: player

HARV.TD:
	RenderSprites:
		PlayerPalette: playertd

HARV.TD.Husk:
	RenderSprites:
		PlayerPalette: playertd

MCV:
	RenderSprites:
		PlayerPalette: player

MCV.Husk:
	RenderSprites:
		PlayerPalette: player

AMCV:
	RenderSprites:
		PlayerPalette: playertd

AMCV.Husk:
	RenderSprites:
		PlayerPalette: playertd

LST:
	RenderSprites:
		PlayerPalette: player-twotonenod

SB:
	RenderSprites:
		PlayerPalette: player-twotonenod

SS2:
	RenderSprites:
		PlayerPalette: player-twotonenod

ISUB:
	RenderSprites:
		PlayerPalette: player-twotonenod

TRAN:
	RenderSprites:
		PlayerPalette: player-twotonenod

SCRN:
	RenderSprites:
		PlayerPalette: playertd

SCRN.Husk:
	RenderSprites:
		PlayerPalette: playertd

BEAG:
	RenderSprites:
		PlayerPalette: player-twotonenod

KAMV:
	RenderSprites:
		PlayerPalette: player-twotonenod

RECK:
	RenderSprites:
		PlayerPalette: player-twotonenod

3TNK.RHINO:
	RenderSprites:
		PlayerPalette: player-twotonenod

MSG:
	RenderSprites:
		PlayerPalette: player-twotonenod

MNLY:
	RenderSprites:
		PlayerPalette: player-twotonenod

JJET:
	RenderSprites:
		PlayerPalette: playertd-twotonenod

GSCR:
	RenderSprites:
		PlayerPalette: playerscrin-twotonenod
	WithDeathAnimation:
		DeathSequencePalette: playerscrin-twotonenod

GSCR.Mutating:
	RenderSprites:
		PlayerPalette: playerscrin-twotonenod
	WithDeathAnimation:
		DeathSequencePalette: playerscrin-twotonenod
