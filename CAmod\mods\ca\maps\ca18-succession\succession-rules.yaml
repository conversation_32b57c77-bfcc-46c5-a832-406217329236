
^Palettes:
	TerrainLighting:
	TintPostProcessEffect:
		Red: 0.9
		Green: 1.0
		Blue: 1.2
		Ambient: 0.75

World:
	LuaScript:
		Scripts: campaign.lua, succession.lua
	MissionData:
		Briefing: Revenge is ours Comrade General! <PERSON>'s overconfidence made him careless and <PERSON> now commands many of his cyborgs.\n\nWhile this is excellent news, <PERSON> is still able to produce cyborgs too fast for <PERSON> to manage. Research into using Tiberium to enhance <PERSON>'s powers has begun, but it will be some time before significant progress is made.\n\nYou must return to where the cyborgs were first unleashed, where your predecessor failed miserably to contain them. The primary cyborg production facilities are still there, and we must take control of them to prevent <PERSON> from overwhelming us again. With the cyborg factories under our control, nothing will be able to stop us.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: drok

Player:
	PlayerResources:
		DefaultCash: 6000

^PowerDrainable:
	Power@DiscPowerDrain:
		Amount: -50

TMPP:
	Inherits@CAMPAIGNDISABLED: ^Disabled
	CaptureManager:
	Capturable:
		RequiresCondition: !build-incomplete && !being-warped
		Types: building
	CapturableProgressBar:
	CapturableProgressBlink:
	Health:
		HP: 300000
	-Sellable:
	Targetable@NoAutoTarget:
		TargetTypes: NoAutoTarget

BIO:
	-OwnerLostAction:
	-SpawnActorOnDeath:
	-ProvidesPrerequisite@mortar:
	-ProvidesPrerequisite@toxintruck:
	Tooltip:
		Name: Cyborg Factory
	FireWarheadsOnDeath:
		Type: Footprint
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode
	Health:
		HP: 300000
	RepairableBuilding:
		RepairStep: 500
		RepairPercent: 30
		RepairingNotification: Repairing
	Targetable@NoAutoTarget:
		TargetTypes: NoAutoTarget

TTRK:
	-Buildable:

BRUT:
	Buildable:
		Prerequisites: barr, anyradar

DISC:
	Buildable:
		Prerequisites: afld, stek

lasher.upgrade:
	Buildable:
		Prerequisites: anyradar

gattling.upgrade:
	Buildable:
		Prerequisites: anyradar
