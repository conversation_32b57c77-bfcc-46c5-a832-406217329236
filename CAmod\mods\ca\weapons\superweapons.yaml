ParaBomb:
	ReloadDelay: 8
	Range: 3c0
	Report: chute1.aud
	Projectile: GravityBomb
		Image: PARABOMB
		OpenSequence: open
		Velocity: 0, 0, -35
		Acceleration: 0, 0, 0
		Shadow: False
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 40000
		Versus:
			None: 40
			Wood: 55
			Light: 65
			Heavy: 80
			Concrete: 90
		DamageTypes: Prone50Percent, Trigger<PERSON>rone, ExplosionDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
	Warhead@3Eff: CreateEffect
		Explosions: building, building2
		ImpactSounds: kaboom25.aud
		ValidTargets: Ground, Ship, Trees
	Warhead@4EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: splash9.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure

ChaosBomb:
	Inherits: ParaBomb
	Warhead@3Eff: CreateEffect
		Explosions: chaosexplosion
		ExplosionPalette: caneon
		ImpactSounds: firebl3.aud
	Warhead@Cloud1: SpawnActor
		Actors: chaoscloud, chaoscloud2
		Range: 5
		ValidTargets: Ground, Water
		ImpactActors: false

CarpetBomb:
	Inherits: ParaBomb
	Report: bwhis.aud
	-Projectile:
	Projectile: GravityBomb
		Image: BOMBLET
		Shadow: true
		Acceleration: 0, 0, -1
	Warhead@1Dam: SpreadDamage
		Damage: 30000
		Versus:
			Wood: 32
			Concrete: 60
			Heavy: 70

AtomBomb:
	Inherits: CarpetBomb
	Range: 1c0
	Projectile: GravityBomb
		Image: bigbomb
		Velocity: 15, 0, -35
		Acceleration: 0, 0, 0
	ReloadDelay: 80
	ValidTargets: Ground, Water, Air
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 22000
		Falloff: 1000, 500, 250, 125, 75, 37, 18, 9, 4, 0
		ValidTargets: Ground, Trees, Water, Air
		Versus:
			Tree: 200
			None: 10
			Light: 75
			Heavy: 100
			Concrete: 100
			Wood: 38
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Res_impact: DestroyResource
	Warhead@3Eff_impact: CreateEffect
		Explosions: nuke
		ImpactSounds: kaboom1.aud
		ImpactActors: false
	Warhead@5Res_areanuke1: DestroyResource
		Size: 4
		Delay: 5
	Warhead@6Eff_areanuke1: CreateEffect
		ImpactSounds: kaboom22.aud
		Delay: 5
		ImpactActors: false
	Warhead@6Smu_areanuke1: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Trees
		Size: 4
		Delay: 5
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 5
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 350
		Falloff: 100, 75, 52, 37, 24, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong
	Warhead@22FlashEffect: FlashEffect
		Duration: 20
		FlashType: Nuke

InfernoBomb:
	Inherits: CarpetBomb
	Range: 3c0
	Projectile: GravityBomb
		Image: bigbomb
	Warhead@1Dam: SpreadDamage
		Versus:
			None: 70
			Wood: 50
			Light: 85
			Heavy: 40
			Concrete: 60
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall
		Size: 2
	Warhead@3Eff: CreateEffect
		Explosions: b2bexp, b2bexp2
		ExplosionPalette: tseffect
		ImpactSounds: expnew06.aud
	Warhead@4Dam_areanuke1: SpreadDamage
		Spread: 2c0
		Damage: 1000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: Ground, Trees, Water, Air
		Versus:
			Wood: 50
			Concrete: 25
			Brick: 25
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@8Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 10
		ValidTargets: Trees
		DamageTypes: Incendiary
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 5
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@Shrap: FireShrapnel
		Weapon: FireDebris
		Amount: 7
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

InfernoBombTargeter:
	Range: 13c0
	ReloadDelay: 50
	Projectile: InstantHit

GeneticMutationBomb:
	ReloadDelay: 25
	Range: 1c0
	Report: bwhis.aud
	Projectile: GravityBomb
		Image: bigbomb
		Velocity: 15, 0, -35
		Acceleration: 0, 0, 0
		Shadow: true
	Warhead@COND: GrantExternalConditionCA
		Range: 4c0
		Duration: 16
		Condition: geneticmutation
		ValidTargets: Infantry
		InvalidTargets: MindControlImmune
	Warhead@MUT: FireShrapnel
		Weapon: GeneticMutation
		AimChance: 100
		Amount: 6
		Delay: 8
		ImpactActors: false
	Warhead@3Eff: CreateEffect
		Explosions: mutablast
		ExplosionPalette: mutablast
		ImpactSounds: gmutation.aud

GeneticMutationBomb.UPG:
	Inherits: GeneticMutationBomb
	Warhead@COND: GrantExternalConditionCA
		Range: 5c0
	Warhead@MUT: FireShrapnel
		Weapon: GeneticMutation.UPG
		Amount: 9

GeneticMutation:
	Range: 4c0
	ValidTargets: GeneticallyMutatable
	Projectile: PlasmaBeam
		Duration: 7
		Colors: ff509a08
		InnerLightness: 200
		OuterLightness: 100
		Radius: 2
		Distortion: 150
		DistortionAnimation: 150
		SegmentLength: 350
		ZOffset: 512
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 50000
		DamageTypes: MutatedDeath
		ValidTargets: GeneticallyMutatable

GeneticMutation.UPG:
	Inherits: GeneticMutation
	Range: 5c0

HeliosBomb:
	Inherits: ParaBomb
	Range: 1c0
	ReloadDelay: 80
	Projectile: GravityBomb
		Image: heliosbomb
		Shadow: true
		Velocity: 15, 0, -35
	Warhead@3Eff: CreateEffect
		Explosions: heliosexplode
		ImpactSounds: heliosexplode.aud
		Delay: 1
		ExplosionPalette: effect-ignore-lighting
	Warhead@4Eff: CreateEffect
		Explosions: heliosexplode2
		Delay: 3
		ExplosionPalette: effect-ignore-lighting
	Warhead@Blind: GrantExternalConditionCA
		Range: 8c0
		Duration: 200
		Condition: blinded
		InvalidTargets: BlindImmune
	Warhead@Flash: FlashEffect
		Duration: 10
		FlashType: Nuke

Atomic:
	ValidTargets: Ground, Trees, Water, Underwater, Air, AirSmall
	Warhead@1Dam_impact: SpreadDamage
		Spread: 1c0
		Damage: 12200
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: Ground, Trees, Water, Underwater, Air, AirSmall
		Versus:
			Wood: 28
			Heavy: 80
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Res_impact: DestroyResource
		Size: 1
	Warhead@3Smu_impact: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall
		Size: 1
	Warhead@4Eff_impact: CreateEffect
		Explosions: nuke2
		ImpactSounds: nuke.aud
		ImpactActors: false
		ValidTargets: Ground, Water, Air
	Warhead@5Dam_areanuke1: SpreadDamage
		Spread: 2c0
		Damage: 4200
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: Ground, Trees, Water, Underwater, Air, AirSmall
		Versus:
			Wood: 28
			Heavy: 80
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@6Res_areanuke1: DestroyResource
		Size: 2
		Delay: 5
	Warhead@7Smu_areanuke1: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 2
		Delay: 5
	Warhead@8Eff_areanuke1: CreateEffect
		ImpactSounds: kaboom22.aud
		Delay: 5
		ImpactActors: false
	Warhead@9Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 4200
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 10
		ValidTargets: Ground, Water, Underwater, Air, AirSmall
		Versus:
			Wood: 50
			Heavy: 80
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@11Res_areanuke2: DestroyResource
		Size: 3
		Delay: 10
	Warhead@12Smu_areanuke2: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 3
		Delay: 10
	Warhead@13Dam_areanuke3: SpreadDamage
		Spread: 4c0
		Damage: 4200
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 15
		ValidTargets: Ground, Trees, Water, Underwater, Air, AirSmall
		Versus:
			Tree: 200
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@15Res_areanuke3: DestroyResource
		Size: 4
		Delay: 15
	Warhead@16Smu_areanuke3: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 4
		Delay: 15
	Warhead@17Dam_areanuke4: SpreadDamage
		Spread: 5c0
		Damage: 4200
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 20
		ValidTargets: Ground, Trees, Water, Underwater, Air, AirSmall
		Versus:
			Tree: 200
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@19Res_areanuke4: DestroyResource
		Size: 5
		Delay: 20
	Warhead@20Smu_areanuke4: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 5
		Delay: 20
	Warhead@21Shake: ShakeScreen
		Duration: 20
		Intensity: 5
		Multiplier: 1,1
	Warhead@22FlashEffect: FlashEffect
		Duration: 20
		FlashType: Nuke
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 7
		Dimensions: 4,4
		Footprint: xxxx xxxx xxxx xxxx
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 750
		Falloff: 100, 75, 52, 37, 24, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong

IonCannon:
	ValidTargets: Ground, Water, Air
	Warhead@1Dam: SpreadDamage
		Range: 0, 0c512, 1c512, 2c512
		Damage: 10500
		Falloff: 1000, 400, 100, 50
		ValidTargets: Ground
		Versus:
			None: 25
			Light: 65
			Wood: 15
			Heavy: 70
		DamageTypes: TriggerProne, ExplosionDeath
	Warhead@2Dam: SpreadDamage
		Range: 0, 0c512, 1c512, 2c512
		Damage: 3000
		Falloff: 1000, 400, 100, 50
		ValidTargets: Cyborg
		Versus:
			Heavy: 0
		DamageTypes: TriggerProne, ExplosionDeath
	Warhead@3Dam: SpreadDamage
		Range: 0, 0c682, 1c341, 2c682
		Damage: 34000
		Falloff: 100, 100, 35, 10
		ValidTargets: Air, AirSmall
		DamageTypes: TriggerProne, ExplosionDeath
	Warhead@4Eff: CreateEffect
		Explosions: ion_ring2
		ExplosionPalette: tseffect
	Warhead@5Smu_impact: LeaveSmudge
		SmudgeType: Scorch
	Warhead@6Smu_area: LeaveSmudge
		SmudgeType: Scorch
		Size: 1
		Delay: 3
	Warhead@7Res_area2: DestroyResource
		Size: 2
		Delay: 6
	Warhead@8Smu_area2: LeaveSmudge
		SmudgeType: Scorch
		Size: 2,1
		Delay: 6

Empicbm:
	ValidTargets: Ground, Water
	Warhead@1Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 3c1, 3c512, 4c1, 4c512
		Damage: 4500
		Falloff: 1000, 368, 135, 90, 68, 37, 10
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath
		Versus:
			None: 20
			Wood: 20
			Light: 20
			Heavy: 20
			Concrete: 20
			Brick: 20
	Warhead@2Eff: CreateEffect
		ExplosionPalette: tseffect-ignore-lighting-alpha75
		Explosions: pulse_explosion
		ImpactSounds: empexpl.aud
	Warhead@emp: GrantExternalConditionCA
		Range: 5c0
		Duration: 500
		Condition: empdisable
		ValidTargets: Ground, Vehicle, Air
		InvalidTargets: EmpImmune
	Warhead@2Smu_impact: LeaveSmudge
		SmudgeType: Scorch-NoFlame
	Warhead@3Smu_area: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 1
		Delay: 3
	Warhead@4Smu_area2: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 2,1
		Delay: 6

EMPMissileLauncher:
	ReloadDelay: 120
	Range: 300c0
	MinRange: 3c0
	Report: icbm1.aud
	Projectile: BulletCA
		Blockable: false
		Shadow: true
		Inaccuracy: 0
		Image: empmissile
		Palette: playertd
		IsPlayerPalette: true
		TrailImage: smokey2
		TrailPalette: tseffect-ignore-lighting-alpha75
		TrailDelay: 3
		ContrailStartColor: cc550080
		ContrailStartColorAlpha: 128
		ContrailLength: 25
		ContrailDelay: 3
		Speed: 300
		LaunchAngle: 55
	Warhead@2Dam_impact: SpreadDamage
		Range: 1c0
		Damage: 4500
		Falloff: 1000
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		Versus:
			None: 80
			Wood: 60
			Light: 60
			Heavy: 40
			Concrete: 60
			Brick: 5
	Warhead@1Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 3c1, 3c512, 4c1, 4c512
		Damage: 4500
		Falloff: 1000, 368, 135, 90, 68, 37, 10
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath
		Versus:
			None: 20
			Wood: 20
			Light: 20
			Heavy: 20
			Concrete: 20
			Brick: 5
	Warhead@2Eff: CreateEffect
		ExplosionPalette: tseffect-ignore-lighting-alpha75
		Explosions: pulse_explosion
		ImpactSounds: empexpl.aud
	Warhead@emp1: GrantExternalConditionCA
		Range: 1c512
		Duration: 600
		Condition: empdisable
		ValidTargets: Ground, Vehicle, Air
		InvalidTargets: EmpImmune
	Warhead@emp2: GrantExternalConditionCA
		Range: 3c0
		Duration: 300
		Condition: empdisable
		ValidTargets: Ground, Vehicle, Air
		InvalidTargets: EmpImmune
	Warhead@emp3: GrantExternalConditionCA
		Range: 4c512
		Duration: 150
		Condition: empdisable
		ValidTargets: Ground, Vehicle, Air
		InvalidTargets: EmpImmune
	Warhead@2Smu_impact: LeaveSmudge
		SmudgeType: Crater
	Warhead@3Smu_area: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 1
		Delay: 3
	Warhead@4Smu_area2: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 2,1
		Delay: 6

FirestormBarrage:
	ReloadDelay: 100
	Range: 300c0
	Burst: 3
	BurstDelays: 20
	MinRange: 3c0
	Report: icbm1.aud
	Projectile: Bullet
		Blockable: false
		Shadow: true
		Inaccuracy: 12c0
		Image: red-missile
		Palette: temptd
		IsPlayerPalette: false
		TrailImage: smokey2
		TrailPalette: tseffect-ignore-lighting-alpha75
		TrailDelay: 4
		Speed: 220
		LaunchAngle: 65
		ContrailLength: 30
		ContrailStartColor: cc550080
		ContrailStartColorAlpha: 128
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 30000
		Versus:
			None: 80
			Wood: 40
			Light: 100
			Heavy: 90
			Concrete: 80
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Eff: CreateEffect
		Explosions: b2bexp, b2bexp2
		ExplosionPalette: tseffect
		ImpactSounds: expnew06.aud
		ValidTargets: Ground, Water, Air, Trees
	Warhead@2Smu_impact: LeaveSmudge
		SmudgeType: Crater
	Warhead@3Smu_area: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 2
		Delay: 3
	Warhead@8Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 10
		ValidTargets: Trees
		DamageTypes: Incendiary
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 5
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@Shrap: FireShrapnel
		Weapon: FireDebris
		Amount: 7
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

IronCurtain:
	Projectile: InstantExplode
	ReloadDelay: 1
	ValidTargets: Ground, Infantry, Water
	Warhead@1Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 2c512
		Damage: 250
		Delay: 5
		Falloff: 1000, 1000, 250, 100
		ValidTargets: Ground, Infantry, Water
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 100
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
	Warhead@2Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 2c512
		Damage: 250
		Delay: 10
		Falloff: 1000, 1000, 250, 100
		ValidTargets: Ground, Infantry, Water
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 100
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
	Warhead@3Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 2c512
		Damage: 250
		Delay: 15
		Falloff: 1000, 1000, 250, 100
		ValidTargets: Ground, Infantry, Water
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 100
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
	Warhead@4Eff: CreateEffect
		ExplosionPalette: effect-ignore-lighting-alpha85
		Explosions: ironcurtain_effect

ForceShield:
	Projectile: InstantExplode
	ReloadDelay: 1
	ValidTargets: Ground, Structure
	Warhead@1Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 2c512
		Damage: 1
		Delay: 5
		Falloff: 1000, 1000, 250, 100
		ValidTargets: Ground, Structure
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 0
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
	Warhead@4Eff: CreateEffect
		ExplosionPalette: effect-ignore-lighting-alpha85
		Explosions: forceshield_effect
	Warhead@fspower: GrantExternalConditionCA
		Range: 15c0
		Duration: 500
		Condition: forcedisabled
		ValidTargets: Ground, Structure
		ValidRelationships: Ally

StealthBubble:
	Projectile: InstantExplode
	ReloadDelay: 1
	ValidTargets: Ground, Water
	Warhead@1Dam: SpreadDamage
		Range: 0, 1c1, 2c1, 2c512
		Damage: 450
		Delay: 5
		Falloff: 1000, 1000, 250, 100
		ValidTargets: Infantry
		InvalidTargets: Cyborg
		ValidRelationships: Enemy, Neutral
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
		Versus:
			None: 100
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
	Warhead@2Dam: SpreadDamage
		Range: 0, 1c1, 2c1, 2c512
		Damage: 450
		Delay: 5
		Falloff: 1000, 1000, 250, 100
		ValidTargets: Infantry
		InvalidTargets: Cyborg, Hazmat
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
		Versus:
			None: 100
			Wood: 0
			Light: 0
			Heavy: 0
			Concrete: 0
			Brick: 0
	Warhead@2Eff: CreateEffect
		ExplosionPalette: tseffect
		Explosions: stealthbub

Chemicbm:
	ValidTargets: Ground, Water
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 6500
		Falloff: 1000, 368, 135, 50, 18
		ValidTargets: Ground, Underwater, Air, AirSmall
		InvalidTargets: Creep
		Versus:
			None: 36
			Wood: 60
			Concrete: 100
			Light: 65
			Heavy: 100
			Brick: 10
			Aircraft: 160
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
	Warhead@3Eff_impact: CreateEffect
		Explosions: chem_miss
		ExplosionPalette: tseffect-ignore-lighting-alpha75
		ImpactSounds: chembomb.aud
	Warhead@2Dam_chem: SpreadDamage
		Spread: 2c0
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 20
		ValidTargets: Ground, Underwater, Air, AirSmall
		InvalidTargets: Creep
		Versus:
			None: 36
			Wood: 50
			Concrete: 120
			Light: 65
			Heavy: 100
			Brick: 10
			Aircraft: 160
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
	Warhead@3Dam_chem2: SpreadDamage
		Spread: 3c0
		Damage: 3250
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 40
		ValidTargets: Ground, Underwater, Air, AirSmall
		InvalidTargets: Creep
		Versus:
			None: 36
			Wood: 145
			Concrete: 210
			Light: 85
			Heavy: 140
			Brick: 10
			Aircraft: 160
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
	Warhead@4Dam_chem2: SpreadDamage
		Spread: 4c0
		Damage: 2750
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 65
		ValidTargets: Ground, Underwater, Air, AirSmall
		InvalidTargets: Creep
		Versus:
			None: 36
			Wood: 175
			Concrete: 210
			Light: 85
			Heavy: 140
			Brick: 10
			Aircraft: 160
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
	Warhead@11Smu_areanuke3: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Structure, Wall, Trees
		Size: 3
		Delay: 5
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 750
		Falloff: 100, 75, 52, 37, 24, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong
	Warhead@13Spawn: SpawnActor
		Actors: toxiccloud
		Range: 5
		ForceGround: false
		Image: Cloud1d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@14Spawn: SpawnActor
		Actors: toxiccloud2, toxiccloud
		Range: 5
		ForceGround: false
		Image: Cloud1d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@15Spawn: SpawnActor
		Actors: toxiccloud2, toxiccloud
		Range: 5
		ForceGround: false
		Image: Cloud1d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@16Spawn: SpawnActor
		Actors: toxiccloud2
		Range: 5
		ForceGround: false
		Image: Cloud2d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@17Spawn: SpawnActor
		Actors: toxiccloud2
		Range: 5
		ForceGround: false
		Image: Cloud2d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@Shrap: FireShrapnel
		Weapon: ChemDebris
		Amount: 10
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

IonStormInit:
	Projectile: InstantExplode
	ReloadDelay: 1
	Warhead@2: SpawnMultiWeaponImpact
		ValidTargets: Ground, Air, Water
		Weapon: IonCloud
		ImpactActors: false
		ImpactOffsets: 0,0, 0,0, -4,0, 4,0, 0,-4, 0,4, -2,-2, 2,2, -2,2, 2,-2
		RandomImpactSequence: true

IonCloud:
	ReloadDelay: 25
	Range: 5c0
	Projectile: AthenaProjectile
		Altitude: 5c768
	Warhead@1: FireFragment
		UseZOffsetAsAbsoluteHeight: true
		Weapon: IonBolt
		ValidTargets: Air, Ground, Water
		Delay: 28
		ImpactActors: false
	Warhead@TargetValidation: SpreadDamage

IonBolt:
	Projectile: InstantHit
	Range: 512
	ValidTargets: Ground, Water, Air, AirSmall
	Warhead@1Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 3c1, 4c1
		Delay: 1
		Damage: 2650
		Falloff: 1000, 1000, 200, 100, 75
		ValidTargets: Ground, Water, Underwater, Air, AirSmall
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		Versus:
			None: 50
			Concrete: 300
			Heavy: 120
			Light: 75
			Wood: 105
			Aircraft: 280
	Warhead@4Effect: CreateEffect
		Explosions: ionbeam
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
		ImpactSounds: ion1.aud
		ImpactActors: false
	Warhead@5Effect: CreateEffect
		Explosions: ionbeam2
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
		ImpactSounds: ion1.aud
		ImpactActors: false
	Warhead@6Effect: CreateEffect
		Explosions: ionbeam3
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
		ImpactActors: false
	Warhead@7Effect: CreateEffect
		Explosions: ionbeam4
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
		ImpactSounds: ion1.aud
		ImpactActors: false
	Warhead@8Effect: CreateEffect
		Explosions: ionbeam5
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
		ImpactActors: false
	Warhead@9Effect: CreateEffect
		Explosions: ionbeam6
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
		ImpactActors: false
	Warhead@10Eff: CreateEffect
		Image: ionsfx
		Explosions: idle
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
	Warhead@11Eff: CreateEffect
		Explosions: ion_ring2
		ImpactSounds: ion2.aud
		ExplosionPalette: tseffect
		Delay: 5
	Warhead@3Smu_impact: LeaveSmudge
		SmudgeType: Scorch
	Warhead@4Smu_area: LeaveSmudge
		SmudgeType: Scorch
		Size: 1
		Delay: 3
	Warhead@5Res_area2: DestroyResource
		Size: 2
		Delay: 6
	Warhead@6Smu_area2: LeaveSmudge
		SmudgeType: Scorch
		Size: 2,1
		Delay: 6

DeathHand:
	Projectile: InstantHit
	Warhead@Cluster: FireCluster
		Weapon: DeathHandCluster
		RandomClusterCount: 6
		Dimensions: 3,3
		Footprint: xxx xXx xxx
	Warhead@2Eff: CreateEffect
		Explosions: chem_miss
		ExplosionPalette: tseffect-ignore-lighting-alpha75
		ImpactSounds: expnew06.aud
		ImpactActors: false
		AffectsParent: true
		AirThreshold: 3c511

DeathHandCluster:
	ReloadDelay: 60
	Range: 7c0
	ValidTargets: Ground, Ship, Air, Water
	Projectile: Bullet
		Blockable: false
		Image: 120mm
		TrailImage: smokey
		Shadow: true
		Speed: 96
		LaunchAngle: 0, 32
		Inaccuracy: 1c512
		BounceCount: 0
	Warhead@1Dam: SpreadDamage
		Range: 0, 1c1, 2c1, 2c512
		Delay: 1
		Damage: 3500
		Falloff: 1000, 500, 250, 100
		Versus:
			None: 90
			Concrete: 50
			Brick: 75
			Wood: 25
			Light: 60
			Heavy: 60
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure
	Warhead@3Eff: CreateEffect
		Explosions: b2bexp, b2bexp2
		ExplosionPalette: tseffect
		ImpactSounds: expnew06.aud
		ValidTargets: Ground, Ship, Trees
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 5
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@Shrap: FireShrapnel
		Weapon: FireDebris
		Amount: 7
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

WeatherStormInit:
	Projectile: InstantExplode
	ReloadDelay: 1
	Warhead@1: SpawnMultiWeaponImpact
		ValidTargets: Ground, Air, Water
		Delay: 50
		Weapon: WeatherCloud
		ImpactActors: false
		ImpactOffsets: 0,0, -1,-1, 1,-1, -1,1, 1,1, -2,-2, 0,-2, 2,-2, -2,0, 2,0, -2,2, 0,2, 2,2, -3,-3, -1,-3, 1,-3, 3,-3, -3,-1, 3,-1, -3,1, 3,1, -3,3, -1,3, 1,3, 3,3, -5,-1, -5,1, -4,-2, -4,0, -4,2, -1,-5, 1,-5, -2,-4, 0,-4, 2,-4, 4,-2, 4,0, 4,2, 5,-1, 5,1, -2,4, 0,4, 2,4, -1,5, 1,5
		RandomImpactSequence: true
		RandomOffset: 512
		Interval: 5
	Warhead@DirectDamage: SpreadDamage
		Damage: 48000
		Delay: 78
	Warhead@4: CreateEffect
		Delay: 50
		ImpactSounds: sweaintr.aud
		ValidTargets: Ground, Air, Water

WeatherCloud:
	ReloadDelay: 8
	Range: 6c512
	Projectile: AthenaProjectile
		Altitude: 5c768
	Warhead@1: FireFragment
		UseZOffsetAsAbsoluteHeight: true
		Weapon: WeatherBolt
		ValidTargets: Air, Ground, Water
		Delay: 28
		ImpactActors: false
	Warhead@TargetValidation: SpreadDamage
	Warhead@4: CreateEffect
		Explosions: weathercloud1, weathercloud2, weathercloud1f, weathercloud2f
		ExplosionPalette: ra2unit
		ValidTargets: Ground, Air, Water
	Warhead@Shadow: FireFragment
		UseZOffsetAsAbsoluteHeight: true
		Weapon: WeatherCloudShadow
		ValidTargets: Ground, Air, Water
		ImpactActors: false

WeatherCloudShadow:
	Projectile: InstantHit
	Range: 512
	Warhead@Shadow: CreateEffect
		Explosions: weathercloudshadow
		ValidTargets: Ground, Air, Water
		Inaccuracy: 341

WeatherBolt:
	Projectile: InstantHit
	Range: 512
	ValidTargets: Ground, Water, Air, AirSmall
	Warhead@1Dam: SpreadDamage
		Range: 0, 1c1, 2c1, 3c1
		Falloff: 1000, 700, 450, 0
		Damage: 1000
		AffectsParent: true
		ValidTargets: Ground, Air, AirSmall, Underwater, Water
		DamageTypes: TriggerProne, ElectricityDeath
		Versus:
			None: 35
			Wood: 65
			Concrete: 150
			Brick: 5
			Heavy: 85
			Light: 55
			Aircraft: 175
	Warhead@4: CreateEffect
		Explosions: weatherbolt1, weatherbolt2, weatherbolt3, weatherbolt1f, weatherbolt2f, weatherbolt3f
		ImpactSounds: sweastra.aud, sweastrb.aud, sweastrc.aud, sweastrd.aud
		ValidTargets: Ground, Air, Water
		ExplosionPalette: ra2effect-ignore-lighting-alpha90
	Warhead@5: CreateEffect
		Explosions: large_explosion
		ValidTargets: Ground, Water
	Warhead@6Smu: LeaveSmudge
		SmudgeType: Scorch

DropPodVisual:
	Warhead@1Eff: CreateEffect
		Explosions: droppod_explosion2
		ExplosionPalette: tseffect
		InvalidTargets: Water
		ImpactSounds: methit1.aud
	Warhead@2Eff: CreateEffect
		Explosions: ion_ring
		ExplosionPalette: tseffect-ignore-lighting-alpha75
		InvalidTargets: Water
	Warhead@3EffWater: CreateEffect
		Explosions: large_splash
		ImpactSounds: splash9.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

DropPodArrive:
	Report: meteor1.aud, meteor2.aud
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage

Frenzy:
	Warhead@2Eff: CreateEffect
		Image: crate-effects
		Explosions: rapid

GpsScramble:
	Warhead@2Eff: CreateEffect
		Image: crate-effects
		Explosions: stealth

TemporalIncursion:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@2Eff: CreateEffect
		Explosions: chronowarpbig_effect
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ValidTargets: Ground, Air, Water
		ImpactSounds: chrono2.aud
	Warhead@teleport: SpawnActor
		Actors: 2tnk.temp, 2tnk.temp, tnkd.temp, arty.temp, jeep.temp
		Range: 5
		Image: chronoappear
		Sequence: idle
		ValidTargets: Ground, Water
		ImpactActors: false
	Warhead@3Flash: ChronoFlashEffect

ChronoAI:
	Inherits: TemporalIncursion
	Warhead@teleport: SpawnActor
		Actors: 2tnk.chrono, 2tnk.chrono, 2tnk.chrono, arty.chrono, arty.chrono

TimeWarp:
	ValidTargets: Temporal
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@2Eff: CreateEffect
		Explosions: chronowarpbig_effect
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ValidTargets: Ground, Air, Water
	Warhead@Damage1: SpreadDamage
		Damage: 1
	Warhead@3Flash: ChronoFlashEffect

ClusterMineSpawner:
	ValidTargets: Ground, Water
	ReloadDelay: 50
	Report: bwhis.aud
	Range: 2c0
	TargetActorCenter: true
	Projectile: Bullet
		Image: bigbomb
		Speed: 78
		LaunchAngle: 10
		Shadow: true
		AirburstAltitude: 1c512
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: kaboom15.aud
		ValidTargets: Air, Structure, Bridge
	Warhead@4Eff: CreateEffect
		ImpactSounds: scluster.aud
		Delay: 15
		ValidTargets: Air, Structure, Bridge
	Warhead@Cluster: FireClusterCA
		Weapon: MineSpawner
		RandomClusterCount: 6
		Dimensions: 3,3
		Footprint: xxx x_x xxx
		ValidTargets: Ground, Air
	Warhead@ClusterWater: FireClusterCA
		Weapon: MineSpawnerWater
		RandomClusterCount: 5
		Dimensions: 3,3
		Footprint: xxx x_x xxx
		ValidTargets: Water, Air

MineSpawner:
	Range: 5c0
	ValidTargets: Ground
	Projectile: BulletCA
		Image: minvs
		Sequences: idle
		Palette: player
		IsPlayerPalette: true
		Blockable: false
		Shadow: true
		Speed: 50
		LaunchAngle: 90
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Damage: 5000
		Versus:
			None: 90
			Wood: 75
			Light: 60
			Heavy: 25
			Concrete: 50
			Brick: 100
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		ValidTargets: Structure
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: mineblo1.aud
		ValidTargets: Structure
	Warhead@1Spa: SpawnActor
		Actors: proxyminespawn
		Range: 1
		ForceGround: false
		ValidTargets: Ground, Air
		InvalidTargets: Structure

MineSpawnerWater:
	Range: 5c0
	ValidTargets: Water
	Projectile: BulletCA
		Image: minsf
		Sequences: idle
		Palette: player
		IsPlayerPalette: true
		Shadow: true
		Blockable: false
		Speed: 50
		LaunchAngle: 90
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Damage: 5000
		Versus:
			None: 90
			Wood: 75
			Light: 60
			Heavy: 25
			Concrete: 50
			Brick: 100
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
		ValidTargets: Structure
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: mineblo1.aud
		ValidTargets: Structure
	Warhead@1Spa: SpawnActor
		Actors: proxyminespawnwater
		Range: 1
		ForceGround: false
		ValidTargets: Water, Air
		InvalidTargets: Structure

Cryostorm:
	ReloadDelay: 5
	Range: 1c0
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 4c512
		Damage: 50
		Falloff: 100, 0
		Versus:
			Wood: 0
			Light: 10
			Heavy: 0
			Concrete: 0
			Brick: 0
		DamageTypes: FrozenDeath
	Warhead@1Eff: CreateEffect
		Explosions: cryoblast, cryohit, cryohit, cryohit
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		Inaccuracy: 3c512
		Delay: 7
	Warhead@2Eff: CreateEffect
		Explosions: cryoblast, cryohit, cryohit, cryohit
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		Inaccuracy: 3c512
		Delay: 8

CryostormInit:
	ReloadDelay: 25
	Range: 1c0
	Projectile: InstantHit
	Warhead@1Eff: CreateEffect
		Explosions: cryohit
		ExplosionPalette: ra2effect-ignore-lighting-alpha50
		Inaccuracy: 3c512
	Warhead@2Eff: CreateEffect
		Explosions: cryohit
		ExplosionPalette: ra2effect-ignore-lighting-alpha50
		Inaccuracy: 3c512
		Delay: 5

OverlordsWrath:
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 4c0
		Damage: 75000
		Falloff: 100, 0
		Versus:
			None: 20
			Light: 65
			Heavy: 90
			Concrete: 85
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@3Eff_impact: CreateEffect
		Image: tibmeteor
		Explosions: hit
		ExplosionPalette: caneon
		ImpactSounds: owrath-impact.aud
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		Size: 2
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
	Warhead@3Res: CreateResource
		AddsResourceType: Tiberium
		Size: 1
	Warhead@4Res: CreateResource
		AddsResourceType: Tiberium
		Size: 2
		Delay: 5
	Warhead@Shrap: FireShrapnel
		Weapon: ChemDebris
		Amount: 5
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 350
		Falloff: 100, 75, 52, 37, 24, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.weak
	Warhead@22FlashEffect: FlashEffect
		Duration: 10
		FlashType: OverlordsWrath

SubterraneanStrikeSpawner:
	ReloadDelay: 500
	Projectile: InstantExplode
	Warhead@Spawn: SpawnActor
		Actors: mole
		Range: 3
		ValidTargets: Ground, Water
		ImpactActors: false
		AvoidActors: true
		Prerequisites: !tmpp
	Warhead@SpawnUpg: SpawnActor
		Actors: mole.upg
		Range: 3
		ValidTargets: Ground, Water
		ImpactActors: false
		AvoidActors: true
		Prerequisites: tmpp
	Warhead@Shake: ShakeScreen
		Duration: 10
		Intensity: 2
		Multiplier: 0.5,0.5

SubterraneanAPCUnburrow:
	ReloadDelay: 500
	Projectile: InstantExplode
	Warhead@3Eff: CreateEffect
		Explosions: unburrow
		Image: substrikehole
		ImpactSounds: subdril1.aud
	Warhead@Dirt1: FireShrapnel
		Weapon: TinyDebris
		Amount: 50
		AimChance: 0
		ValidTargets: Ground, Infantry, Vehicle
		ThrowWithoutTarget: true
	Warhead@Dirt2: FireShrapnel
		Weapon: BurrowDebris
		Delay: 10
		Amount: 25
		AimChance: 0
		ValidTargets: Ground, Infantry, Vehicle
		ThrowWithoutTarget: true

SubterraneanAPCBurrow:
	Inherits: SubterraneanAPCUnburrow
	Warhead@3Eff: CreateEffect
		Explosions: burrow
	Warhead@Dirt1: FireShrapnel
		Weapon: BurrowDebris

BurrowDebris:
	Inherits: TinyDebris
	Range: 1c512
	Projectile: Bullet
		BounceCount: 1
