^ScrinCommonDeaths:
	die5:
		Filename: scrininfdeaths.shp
		Length: 19
		Tick: 80
	die6:
		Filename: scrininfdeaths.shp
		Length: 14
		Start: 19
	die8:
		Filename: chronozap.shp
		Length: *
		BlendMode: Alpha
	die9:
		Filename: scrininfdeaths.shp
		Start: 33
		Length: 15
		Tick: 80
	die10:
		Filename: scrininfdeaths.shp
		Start: 33
		Length: 15
		Tick: 80
	die11:
		Filename: scrininfdeaths.shp
		Start: 48
		Length: 13
		Tick: 80
	die12:
		Filename: scrininfdeaths.shp
		Length: 12
		Start: 67
		Tick: 80
		BlendMode: Additive
	die-crushed:
		Filename: scrininfdeaths.shp
		Start: 61
		Length: 6
		Tick: 1600
		ZOffset: -511
	mind-overlay:
		Filename: mindanim.shp
		Length: *
		Tick: 200
		BlendMode: Alpha
		Offset: 0, 0

s1:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: s1.shp
		Facings: 8
	stand2:
		Filename: s1.shp
		Facings: 8
	run:
		Filename: s1.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: s1.shp
		Start: 40
		Length: 4
		Facings: 8
	idle1:
		Filename: s1.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: s1.shp
		Start: 2
		Length: 1
		Tick: 120
	idle3:
		Filename: s1.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: s1.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: s1.shp
		Start: 3
	die1:
		Filename: s1.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: s1.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: s1.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: s1.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: s1.shp
		Start: 72
		Length: 8
		Tick: 80
	muzzle:
		Filename: scrinmuzz1.shp
		Length: 6
		Facings: 16
		BlendMode: Additive
		IgnoreWorldTint: true
	garrison-muzzle:
		Filename: scrinmuzz1.shp
		Length: 6
		Facings: 16
		BlendMode: Additive
		IgnoreWorldTint: true
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: s1icon.shp

s2:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: s2.shp
		Facings: 8
	stand2:
		Filename: s2.shp
		Facings: 8
	run:
		Filename: s2.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: s2.shp
		Start: 40
		Length: 4
		Facings: 8
		Tick: 80
	idle1:
		Filename: s2.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: s2.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: s2.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: s2.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: s2.shp
		Start: 3
	die1:
		Filename: s2.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: s2.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: s2.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: s2.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: s2.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: s2icon.shp

s3:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: s3.shp
		Facings: 8
	stand2:
		Filename: s3.shp
		Facings: 8
	run:
		Filename: s3.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: s3.shp
		Start: 40
		Length: 4
		Facings: 8
	idle1:
		Filename: s3.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: s3.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: s3.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: s3.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: s3.shp
		Start: 3
	die1:
		Filename: s3.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: s3.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: s3.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: s3.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: s3.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: s3icon.shp

s4:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: s4.shp
		Facings: 8
	stand2:
		Filename: s4.shp
		Facings: 8
	run:
		Filename: s4.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: s4.shp
		Start: 40
		Length: 4
		Facings: 8
	idle1:
		Filename: s4.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: s4.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: s4.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: s4.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: s4.shp
		Start: 3
	die1:
		Filename: s4.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: s4.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: s4.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: s4.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: s4.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: s4icon.shp

s6:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: s6.shp
		Facings: 8
	stand2:
		Filename: s6.shp
		Facings: 8
	run:
		Filename: s6.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	idle1:
		Filename: s6.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: s6.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: s6.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: s6.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: s6.shp
		Start: 3
	die1:
		Filename: s6.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: s6.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: s6.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: s6.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: s6.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: s6icon.shp

mrdr:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: mrdr.shp
		Facings: 8
	stand2:
		Filename: mrdr.shp
		Facings: 8
	run:
		Filename: mrdr.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: mrdr.shp
		Frames: 43,44,48,49,53,54,58,59,63,64,68,69,73,74,78,79
		Length: 2
		Facings: 8
		Tick: 80
	idle1:
		Filename: mrdr.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: mrdr.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: mrdr.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: mrdr.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: mrdr.shp
		Start: 3
	die1:
		Filename: mrdr.shp
		Start: 80
		Length: 8
		Tick: 80
	die2:
		Filename: mrdr.shp
		Start: 88
		Length: 9
		Tick: 80
	die3:
		Filename: mrdr.shp
		Start: 88
		Length: 9
		Tick: 80
	die4:
		Filename: mrdr.shp
		Start: 88
		Length: 9
		Tick: 80
	die5:
		Filename: gscr.shp
		Start: 97
		Length: 11
		Tick: 80
	die6:
		Filename: gscr.shp
		Length: 13
		Start: 116
	die7:
		Filename: mrdr.shp
		Start: 80
		Length: 8
		Tick: 80
	die8:
		Filename: chronozap.shp
		Length: *
		BlendMode: Alpha
	die9:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die10:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die11:
		Filename: mrdr.shp
		Start: 80
		Length: 8
		Tick: 80
	die12:
		Filename: mrdr.shp
		Start: 80
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: mrdricon.shp

evis:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: evis.shp
		Facings: 8
	stand2:
		Filename: evis.shp
		Facings: 8
	run:
		Filename: evis.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: evis.shp
		Frames: 43,44,48,49,53,54,58,59,63,64,68,69,73,74,78,79
		Length: 2
		Facings: 8
		Tick: 80
	idle1:
		Filename: evis.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: evis.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: evis.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: evis.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: evis.shp
		Start: 3
	die1:
		Filename: evis.shp
		Start: 80
		Length: 8
		Tick: 80
	die2:
		Filename: evis.shp
		Start: 88
		Length: 9
		Tick: 80
	die3:
		Filename: evis.shp
		Start: 88
		Length: 9
		Tick: 80
	die4:
		Filename: evis.shp
		Start: 88
		Length: 9
		Tick: 80
	die5:
		Filename: gscr.shp
		Start: 97
		Length: 11
		Tick: 80
	die6:
		Filename: gscr.shp
		Length: 13
		Start: 116
	die7:
		Filename: evis.shp
		Start: 80
		Length: 8
		Tick: 80
	die8:
		Filename: chronozap.shp
		Length: *
		BlendMode: Alpha
	die9:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die10:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die11:
		Filename: evis.shp
		Start: 80
		Length: 8
		Tick: 80
	die12:
		Filename: evis.shp
		Start: 80
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: evisicon.shp

impl:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: impl.shp
		Facings: 8
	stand2:
		Filename: impl.shp
		Facings: 8
	run:
		Filename: impl.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: impl.shp
		Frames: 43,44,48,49,53,54,58,59,63,64,68,69,73,74,78,79
		Length: 2
		Facings: 8
		Tick: 80
	idle1:
		Filename: impl.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: impl.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: impl.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: impl.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: impl.shp
		Start: 3
	die1:
		Filename: impl.shp
		Start: 80
		Length: 8
		Tick: 80
	die2:
		Filename: impl.shp
		Start: 88
		Length: 9
		Tick: 80
	die3:
		Filename: impl.shp
		Start: 88
		Length: 9
		Tick: 80
	die4:
		Filename: impl.shp
		Start: 88
		Length: 9
		Tick: 80
	die5:
		Filename: gscr.shp
		Start: 97
		Length: 11
		Tick: 80
	die6:
		Filename: gscr.shp
		Length: 13
		Start: 116
	die7:
		Filename: impl.shp
		Start: 80
		Length: 8
		Tick: 80
	die8:
		Filename: chronozap.shp
		Length: *
		BlendMode: Alpha
	die9:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die10:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die11:
		Filename: impl.shp
		Start: 80
		Length: 8
		Tick: 80
	die12:
		Filename: impl.shp
		Start: 80
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: implicon.shp

stlk:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: stlk.shp
		Facings: 8
	stand2:
		Filename: stlk.shp
		Facings: 8
	run:
		Filename: stlk.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: stlk.shp
		Frames: 43,44,48,49,53,54,58,59,63,64,68,69,73,74,78,79
		Length: 2
		Facings: 8
		Tick: 80
	idle1:
		Filename: stlk.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: stlk.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: stlk.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: stlk.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: stlk.shp
		Start: 3
	die1:
		Filename: stlk.shp
		Start: 80
		Length: 8
		Tick: 80
	die2:
		Filename: stlk.shp
		Start: 88
		Length: 9
		Tick: 80
	die3:
		Filename: stlk.shp
		Start: 88
		Length: 9
		Tick: 80
	die4:
		Filename: stlk.shp
		Start: 88
		Length: 9
		Tick: 80
	die5:
		Filename: gscr.shp
		Start: 97
		Length: 11
		Tick: 80
	die6:
		Filename: gscr.shp
		Length: 13
		Start: 116
	die7:
		Filename: stlk.shp
		Start: 80
		Length: 8
		Tick: 80
	die8:
		Filename: chronozap.shp
		Length: *
		BlendMode: Alpha
	die9:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die10:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die11:
		Filename: stlk.shp
		Start: 80
		Length: 8
		Tick: 80
	die12:
		Filename: stlk.shp
		Start: 80
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: stlkicon.shp

wchr:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: wchr.shp
		Facings: 8
	stand2:
		Filename: wchr.shp
		Facings: 8
	run:
		Filename: wchr.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: wchr.shp
		Start: 40
		Length: 4
		Facings: 8
	idle1:
		Filename: wchr.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: wchr.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: wchr.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: wchr.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: wchr.shp
		Start: 3
	die1:
		Filename: wchr.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: wchr.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: wchr.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: wchr.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: wchr.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: wchricon.shp

brst:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: brst.shp
		Facings: 8
	stand2:
		Filename: brst.shp
		Facings: 8
	run:
		Filename: brst.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: brst.shp
		Start: 40
		Length: 4
		Facings: 8
		Tick: 120
	idle1:
		Filename: brst.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: brst.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: brst.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: brst.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: brst.shp
		Start: 3
	die1:
		Filename: brst.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: brst.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: brst.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: brst.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: brst.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	idle-overlay:
		Filename: empty.shp
	spawn-overlay:
		Filename: leechhitlg.shp
		Frames: 7,8,9,10,11,12,13,14,15,16,17,18
		BlendMode: Additive
		Tick: 60
		Length: *
	icon:
		Filename: brsticon.shp

smedi:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: smedi.shp
		Facings: 8
	stand2:
		Filename: smedi.shp
		Facings: 8
	heal:
		Filename: smedi.shp
		Start: 40
		Length: 4
		Facings: 8
	run:
		Filename: smedi.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	idle1:
		Filename: smedi.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: smedi.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: smedi.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: smedi.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: smedi.shp
		Start: 3
	die1:
		Filename: smedi.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: smedi.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: smedi.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: smedi.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: smedi.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: smediicon.shp

arti:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: arti.shp
		Facings: 8
	stand2:
		Filename: arti.shp
		Facings: 8
	repair:
		Filename: arti.shp
		Start: 40
		Length: 4
		Facings: 8
	run:
		Filename: arti.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	idle1:
		Filename: arti.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: arti.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: arti.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: arti.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: arti.shp
		Start: 3
	die1:
		Filename: arti.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: arti.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: arti.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: arti.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: arti.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: artiicon.shp

cscr:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: cscr.shp
		Facings: 8
	stand2:
		Filename: cscr.shp
		Facings: 8
	run:
		Filename: cscr.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: cscr.shp
		Start: 40
		Length: 4
		Facings: 8
		Tick: 80
	idle1:
		Filename: cscr.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: cscr.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: cscr.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: cscr.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: cscr.shp
		Start: 3
	die1:
		Filename: cscr.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: cscr.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: cscr.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: cscr.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: cscr.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	muzzle:
		Filename: lasermuzzle.shp
		Frames: 0,1,2,1,2,1,2,1,2,1,2,1,2
		Length: *
		IgnoreWorldTint: true
	icon:
		Filename: cscricon.shp

gscr:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: gscr.shp
		Facings: 8
	run:
		Filename: gscr.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	bash:
		Filename: gscr.shp
		Start: 40
		Length: 5
		Facings: 8
		Tick: 80
	make:
		Filename: gscr.shp
		Start: 129
		Length: 11
		Tick: 80
	idle1:
		Filename: gscr.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: gscr.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: gscr.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: gscr.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: gscr.shp
		Start: 3
	die1:
		Filename: gscr.shp
		Start: 80
		Length: 8
		Tick: 80
	die2:
		Filename: gscr.shp
		Start: 88
		Length: 9
		Tick: 80
	die3:
		Filename: gscr.shp
		Start: 88
		Length: 9
		Tick: 80
	die4:
		Filename: gscr.shp
		Start: 88
		Length: 9
		Tick: 80
	die5:
		Filename: gscr.shp
		Start: 97
		Length: 11
		Tick: 80
	die6:
		Filename: gscr.shp
		Length: 13
		Start: 116
	die8:
		Filename: chronozap.shp
		Length: *
		BlendMode: Alpha
	die9:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die10:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die11:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	die12:
		Filename: gscr.shp
		Start: 108
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	icon:
		Filename: s2icon.shp

mast:
	Inherits: ^ScrinCommonDeaths
	stand:
		Filename: mast.shp
		Facings: 8
	stand2:
		Filename: mast.shp
		Facings: 8
	run:
		Filename: mast.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	shoot:
		Filename: mast.shp
		Start: 40
		Length: 4
		Facings: 8
	idle1:
		Filename: mast.shp
		Length: 2
		Tick: 120
	idle2:
		Filename: mast.shp
		Start: 2
		Length: 2
		Tick: 120
	idle3:
		Filename: mast.shp
		Start: 4
		Length: 2
		Tick: 120
	idle4:
		Filename: mast.shp
		Start: 6
		Length: 2
		Tick: 120
	parachute:
		Filename: mast.shp
		Start: 3
	die1:
		Filename: mast.shp
		Start: 72
		Length: 8
		Tick: 80
	die2:
		Filename: mast.shp
		Start: 80
		Length: 8
		Tick: 80
	die3:
		Filename: mast.shp
		Start: 88
		Length: 8
		Tick: 80
	die4:
		Filename: mast.shp
		Start: 88
		Length: 8
		Tick: 80
	die7:
		Filename: mast.shp
		Start: 72
		Length: 8
		Tick: 80
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	mc:
		Filename: mindcontroller.shp
		Length: *
		Tick: 120
		BlendMode: Alpha
		Offset: 0, 0
	icon:
		Filename: masticon.shp
	pdgy-stand:
		Filename: pdgy.shp
		Facings: 8
	pdgy-run:
		Filename: pdgy.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 120
	pdgy-shoot:
		Filename: pdgy.shp
		Start: 40
		Length: 5
		Facings: 8
		Tick: 80
	pdgy-make:
		Filename: pdgy.shp
		Start: 129
		Length: 11
		Tick: 80
	pdgy-idle1:
		Filename: pdgy.shp
		Length: 2
		Tick: 120
	pdgy-idle2:
		Filename: pdgy.shp
		Start: 2
		Length: 2
		Tick: 120
	pdgy-idle3:
		Filename: pdgy.shp
		Start: 4
		Length: 2
		Tick: 120
	pdgy-idle4:
		Filename: pdgy.shp
		Start: 6
		Length: 2
		Tick: 120
	pdgy-parachute:
		Filename: pdgy.shp
		Start: 3
	pdgy-die1:
		Filename: pdgy.shp
		Start: 80
		Length: 8
		Tick: 80
	pdgy-die2:
		Filename: pdgy.shp
		Start: 88
		Length: 9
		Tick: 80
	pdgy-die3:
		Filename: pdgy.shp
		Start: 88
		Length: 9
		Tick: 80
	pdgy-die4:
		Filename: pdgy.shp
		Start: 88
		Length: 9
		Tick: 80
	pdgy-die5:
		Filename: pdgy.shp
		Start: 97
		Length: 11
		Tick: 80
	pdgy-die6:
		Filename: pdgy.shp
		Length: 13
		Start: 116
	pdgy-die7:
		Filename: pdgy.shp
		Start: 80
		Length: 8
		Tick: 80
	pdgy-die8:
		Filename: chronozap.shp
		Length: *
		BlendMode: Alpha
	pdgy-die9:
		Filename: pdgy.shp
		Start: 108
		Length: 8
		Tick: 80
	pdgy-die10:
		Filename: pdgy.shp
		Start: 108
		Length: 8
		Tick: 80
	pdgy-die11:
		Filename: pdgy.shp
		Start: 80
		Length: 8
		Tick: 80
	pdgy-die12:
		Filename: pdgy.shp
		Start: 88
		Length: 9
		Tick: 80
	empty:
		Filename: empty.shp

harv.scrin:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harvscrin.shp
		Facings: 32
		UseClassicFacings: True
	harvest:
		Filename: scrinharvest.shp
		Length: *
		ZOffset: -128
		BlendMode: Additive
	icon:
		Filename: harvscrinicon.shp

harv.scrin.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harvscrin.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

gunw:
	Inherits: ^VehicleOverlays
	idle:
		Filename: gunw.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: gunw.shp
		Start: 32
		Facings: 32
		Length: 2
		Tick: 80
		UseClassicFacings: True
	muzzle:
		Filename: scrinmuzz1.shp
		Length: 6
		Facings: 16
		BlendMode: Additive
		IgnoreWorldTint: true
	icon:
		Filename: gunwicon.shp

gunw.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: gunw.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

shrw:
	Inherits: ^VehicleOverlays
	idle:
		Filename: shrw.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: shrw.shp
		Start: 32
		Facings: 32
		Length: 2
		Tick: 80
		UseClassicFacings: True
	muzzle:
		Filename: scrinmuzz5.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: 6
		Facings: 16
	icon:
		Filename: shrwicon.shp

shrw.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: shrw.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

seek:
	Inherits: ^VehicleOverlays
	idle:
		Filename: seek.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: seek.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: seekicon.shp

seek.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: seek.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: seek.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

lace:
	Inherits: ^VehicleOverlays
	idle:
		Filename: lace.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: lace.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: laceicon.shp

intl:
	Inherits: ^VehicleOverlays
	idle:
		Filename: intl.shp
		Facings: 1
		Start: 32
	turret:
		Filename: intl.shp
		Facings: 32
		UseClassicFacings: True
	shield:
		Filename: intlshield.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 1022
		BlendMode: Additive
	muzzle:
		Filename: scrinmuzz4.shp
		Length: *
		Tick: 40
		ZOffset: 2049
		BlendMode: Additive
		IgnoreWorldTint: true
	icon:
		Filename: intlicon.shp

intl.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: intl.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

corr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: corr.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: corr.shp
		Start: 32
		Facings: 32
		Length: 2
		Tick: 150
		UseClassicFacings: True
	icon:
		Filename: corricon.shp

corr.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: corr.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

lchr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: lchr.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: lchr.shp
		Start: 32
		Facings: 32
		Length: 2
		Tick: 120
		UseClassicFacings: True
	muzzle:
		Filename: leechhit.shp
		Frames: 14,15,16,17,18
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		Tick: 80
	icon:
		Filename: lchricon.shp

lchr.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: lchr.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

lchr.orb:
	Inherits: ^VehicleOverlays
	Defaults:
		Filename: lchrorb.shp
		Alpha: 0.75
		ZOffset: 512
	idle:
		Length: *
	warped:

stcr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: stcr.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: scrinmuzz2.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: 2
	shield:
		Filename: intlshield.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 1022
		BlendMode: Additive
		Offset: 0, -4
	icon:
		Filename: stcricon.shp

stcr.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: stcr.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

devo:
	Inherits: ^VehicleOverlays
	idle:
		Filename: devo.shp
		Facings: 1
		Start: 32
	turret:
		Filename: devo.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: scrinmuzz2.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		Frames: 0,1,2,3,0,1,2,3,0,1,2,3,0,1,2,3,0,1,2,3,4
	icon:
		Filename: devoicon.shp

devo.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: devo.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

dark:
	Inherits: ^VehicleOverlays
	idle:
		Filename: dark.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: dark.shp
		Facings: 32
		Start: 32
		UseClassicFacings: True
	muzzle:
		Filename: scrinmuzz3.shp
		Length: 5
		BlendMode: Subtractive
		IgnoreWorldTint: true
	icon:
		Filename: darkicon.shp

ruin:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ruin.shp
		Facings: 1
		Start: 32
	turret:
		Filename: ruin.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: scrinmuzz3.shp
		Length: 5
		BlendMode: Additive
		IgnoreWorldTint: true
	icon:
		Filename: ruinicon.shp

ruin.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ruin.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

atmz:
	Inherits: ^VehicleOverlays
	idle:
		Filename: atmz.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: atmz.shp
		Facings: 32
		Start: 32
		UseClassicFacings: True
	icon:
		Filename: atmzicon.shp

tpod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tpod.shp
		Facings: 1
		Start: 32
	turret:
		Filename: tpod.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: tpod.shp
		Start: 33
		Facings: 8
		Length: 14
		Tick: 60
	muzzle:
		Filename: scrinmuzz2.shp
		Length: *
		Frames: 0,1,2,0,1,2,3,4
		BlendMode: Additive
		IgnoreWorldTint: true
	icon:
		Filename: tpodicon.shp

tpod.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tpod.shp
		Facings: 1
		Start: 32
		ZOffset: -512
	turret:
		Filename: tpod.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

rtpd:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tpod.shp
		Facings: 1
		Start: 32
	turret:
		Filename: rtpd.shp
		Facings: 32
		UseClassicFacings: True
		Offset: 0,-25
	move:
		Filename: tpod.shp
		Start: 33
		Facings: 8
		Length: 14
		Tick: 60
	muzzle:
		Filename: scrinmuzz3.shp
		Length: *
		Frames: 0,1,2,0,1,2,3,4
		BlendMode: Additive
		IgnoreWorldTint: true
	icon:
		Filename: rtpdicon.shp

rtpd.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tpod.shp
		Facings: 1
		Start: 32
		ZOffset: -512
	turret:
		Filename: rtpd.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

etpd:
	Inherits: ^VehicleOverlays
	idle:
		Filename: etpd.shp
		Facings: 1
		Start: 32
	turret:
		Filename: etpd.shp
		Facings: 32
		UseClassicFacings: True
	move:
		Filename: etpd.shp
		Start: 33
		Facings: 8
		Length: 14
		Tick: 90
	muzzle:
		Filename: leechhit.shp
		Length: *
		Frames: 4,3,2,1,0,4,3,2,1,0
		BlendMode: Additive
	icon:
		Filename: tpodicon.shp
	shield:
		Filename: etpdshield.shp
		Facings: 1
		ZOffset: 1022
		BlendMode: Additive
		Offset: 0,-12

etpd.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: etpd.shp
		Facings: 1
		Start: 32
		ZOffset: -512
	turret:
		Filename: etpd.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

oblt:
	Inherits: ^VehicleOverlays
	idle:
		Filename: empty.shp
		Facings: 1
	turret:
		Filename: oblt.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: scrinmuzz2.shp
	icon:
		Filename: oblticon.shp

oblt.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: oblt.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

null:
	Inherits: ^VehicleOverlays
	idle:
		Filename: empty.shp
	turret:
		Filename: null.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 2
	icon:
		Filename: nullicon.shp

null.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: null.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

smcv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: smcv.shp
		Facings: 1
		Offset: 0, -4
	icon:
		Filename: smcvicon.shp

smcv.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: smcv.shp
		Facings: 1
		ZOffset: -512

stmr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: stmr.shp
		Facings: 32
		UseClassicFacings: True
	shield:
		Filename: stmrshield.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 1022
		BlendMode: Additive
	muzzle:
		Filename: scrinmuzz7.shp
		Length: *
		BlendMode: Additive
		IgnoreWorldTint: true
	icon:
		Filename: stmricon.shp

torm:
	Inherits: ^VehicleOverlays
	idle:
		Filename: torm.shp
		Facings: 32
		UseClassicFacings: True
	shield:
		Filename: intlshield.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 1022
		BlendMode: Additive
		Scale: 0.8
		Offset: 0, -3
	muzzle:
		Filename: scrinmuzz7.shp
		Length: *
		BlendMode: Additive
		IgnoreWorldTint: true
	icon:
		Filename: tormicon.shp

enrv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: enrv.shp
		Facings: 32
		UseClassicFacings: True
	shield:
		Filename: enrvshield.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 1022
		BlendMode: Additive
	icon:
		Filename: enrvicon.shp

deva:
	Inherits: ^VehicleOverlays
	idle:
		Filename: deva.shp
		Facings: 32
		UseClassicFacings: True
	shield:
		Filename: scrinshield.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 1022
		BlendMode: Additive
	charge1:
		Filename: plasmatorp1.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: 5
		Tick: 40
		ZOffset: 2048
	charge2:
		Filename: plasmatorp2.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: 5
		Tick: 40
		ZOffset: 2048
	charge3:
		Filename: plasmatorp3.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: 5
		Tick: 40
		ZOffset: 2048
	icon:
		Filename: devaicon.shp

pac:
	Inherits: ^VehicleOverlays
	idle:
		Filename: pac.shp
		Facings: 32
		UseClassicFacings: True
	half-deployed:
		Filename: pac.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	fully-deployed:
		Filename: pac.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	shield:
		Filename: scrinshield.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 1022
		BlendMode: Additive
	icon:
		Filename: pacicon.shp

inva:
	Inherits: ^VehicleOverlays
	idle:
		Filename: inva.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: wipicon.shp

mshp:
	Inherits: ^VehicleOverlays
	emp-overlay:
		Filename: emp_fx01.shp
		ZOffset: 3073
	chrono-overlay:
		Filename: chronofade.shp
		ZOffset: 3073
	mind-overlay:
		Filename: mindanim.shp
		ZOffset: 3073
	idle:
		Filename: mshp.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 3071
	ring:
		Filename: mshpring.shp
		Length: *
		Tick: 200
		ZOffset: 3072
	wormholebeam:
		Filename: wormholebeam.shp
		Length: *
		Tick: 40
		Offset: 0, 40, 0
		BlendMode: Additive
	shield:
		Filename: mshpshield.shp
		Facings: 1
		ZOffset: 3073
		BlendMode: Additive
	muzzle:
		Filename: scrinmuzz2.shp
		Frames: 5,5,5,5,5,0,1,2,3,4,0,1,2,3,4,0,1,2,3,4
		Facings: 4
		Length: 5
		ZOffset: 3071
		BlendMode: Additive
		IgnoreWorldTint: true
	empty:
		Filename: empty.shp
	icon:
		Filename: mshpicon.shp

#
# ---- buildings
#

swal:
	idle:
		Filename: swal.shp
		Length: 16
	scratched-idle:
		Filename: swal.shp
		Start: 16
		Length: 16
	damaged-idle:
		Filename: swal.shp
		Start: 32
		Length: 16
	icon:
		Filename: swalicon.shp

sfac:
	Inherits: ^StructureOverlays
	idle:
		Filename: sfac.shp
		Length: 1
		Tick: 160
		Offset: 0, -13
	build:
		Filename: sfac.shp
		Length: 1
		Tick: 160
		Offset: 0, -13
	pdox:
		Filename: sfac.shp
		Length: 1
		Tick: 160
		Offset: 0, -13
	damaged-idle:
		Filename: sfac.shp
		Length: 1
		Start: 1
		Tick: 160
		Offset: 0, -13
	damaged-build:
		Filename: sfac.shp
		Length: 1
		Start: 1
		Tick: 160
		Offset: 0, -13
	damaged-pdox:
		Filename: sfac.shp
		Length: 1
		Start: 1
		Tick: 160
		Offset: 0, -13
	make:
		Filename: sfacmake.shp
		Length: *
		Tick: 40
		Offset: 0, -13
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
			JUNGLE: bib2.jun
			WINTER: bib2.win
			BARREN: bib2.bar
		Length: *
	icon:
		Filename: afacicon.shp

reac:
	Inherits: ^StructureOverlays
	idle:
		Filename: reac.shp
	damaged-idle:
		Filename: reac.shp
		Start: 1
	make:
		Filename: reacmake.shp
		Length: *
		Tick: 80
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
			JUNGLE: bib3.jun
			WINTER: bib3.win
			BARREN: bib3.bar
		Length: *
	icon:
		Filename: reacicon.shp

proc.scrin:
	Inherits: ^StructureOverlays
	idle:
		Filename: procscrin.shp
		Offset: -2,0
	damaged-idle:
		Filename: procscrin.shp
		Start: 1
		Offset: -2,0
	make:
		Filename: procscrinmake.shp
		Length: *
		Tick: 80
		Offset: -2,0
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
			JUNGLE: bib2.jun
			WINTER: bib2.win
			BARREN: bib2.bar
		Length: *
	icon:
		Filename: procscrinicon.shp

port:
	Inherits: ^StructureOverlays
	idle:
		Filename: port.shp
		Length: 1
	idle-overlay:
		Filename: port.shp
		Start: 2
		Length: 13
		BlendMode: Additive
		ZOffset: -512
	damaged-idle:
		Filename: port.shp
		Start: 1
		Length: 1
	make:
		Filename: portmake.shp
		Length: *
		Tick: 80
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
			JUNGLE: bib3.jun
			WINTER: bib3.win
			BARREN: bib3.bar
		Length: *
	icon:
		Filename: porticon.shp

wsph:
	Inherits: ^StructureOverlays
	idle:
		Filename: wsph.shp
		ZOffset: -512
	idle-overlay:
		Filename: wsph.shp
		Start: 2
		Length: 13
		BlendMode: Additive
		ZOffset: -256
	damaged-idle:
		Filename: wsph.shp
		Start: 1
		ZOffset: -512
	make:
		Filename: wsphmake.shp
		Length: *
		Tick: 80
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
			JUNGLE: bib2.jun
			WINTER: bib2.win
			BARREN: bib2.bar
		Length: *
	emp-overlay:
		Filename: emp_fx01.shp
		ZOffset: 2048
	chrono-overlay:
		Filename: chronofade.shp
		ZOffset: 2048
	icon:
		Filename: wsphicon.shp

grav:
	Inherits: ^StructureOverlays
	idle:
		Filename: grav.shp
		ZOffset: -512
	idle-overlay:
		Filename: grav.shp
		Start: 3
		Length: 13
		BlendMode: Additive
		Offset: 0,-1
		ZOffset: -256
	idle-overlay-activate:
		Filename: wormhole.shp
		Length: 15
		Frames: 21, 21, 21, 21, 21, 21, 21, 21, 20, 19, 18, 17, 16, 15, 14
		BlendMode: Additive
		Offset: -3,-6
		ZOffset: -256
	idle-overlay-deactivate:
		Filename: wormhole.shp
		Start: 14
		Length: 7
		BlendMode: Additive
		Offset: -3,-6
		ZOffset: -256
		Tick: 60
	idle-pylon:
		Filename: grav.shp
		Start: 2
		Length: 1
		ZOffset: 2048
	damaged-idle:
		Filename: grav.shp
		Start: 1
		ZOffset: -512
	muzzle:
		Filename: playerzapmuzzle.shp
		ZOffset: 3072
		Length: *
		BlendMode: Additive
		IgnoreWorldTint: true
	make:
		Filename: gravmake.shp
		Length: *
		Tick: 80
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
			JUNGLE: bib2.jun
			WINTER: bib2.win
			BARREN: bib2.bar
		Length: *
	icon:
		Filename: gravicon.shp

nerv:
	Inherits: ^StructureOverlays
	idle:
		Filename: nerv.shp
		Offset: 0, -4
	active:
		Filename: nerv.shp
		Offset: 0, -4
	damaged-idle:
		Filename: nerv.shp
		Start: 1
		Offset: 0, -4
	make:
		Filename: nervmake.shp
		Length: *
		Tick: 80
		Offset: 0, -4
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
			JUNGLE: bib3.jun
			WINTER: bib3.win
			BARREN: bib3.bar
		Length: *
	icon:
		Filename: nervicon.shp

srep:
	Inherits: ^StructureOverlays
	idle:
		Filename: srep.shp
		Offset: 0,4
		ZOffset: -1c511
	damaged-idle:
		Filename: srep.shp
		Start: 10
		Offset: 0,4
		ZOffset: -1c511
	active:
		Filename: srep.shp
		Length: 10
		Offset: 0,4
		Tick: 80
		ZOffset: -1c511
	damaged-active:
		Filename: srep.shp
		Length: 10
		Start: 10
		Offset: 0,4
		Tick: 80
		ZOffset: -1c511
	make:
		Filename: srepmake.shp
		Length: *
		Offset: 0,4
		Tick: 80
	bib:
		Filename: mbFIX.tem
		TilesetFilenames:
			SNOW: mbFIX.sno
			INTERIOR: mbFIX.int
			DESERT: mbFIX.des
			JUNGLE: mbFIX.jun
			WINTER: mbFIX.win
		Length: *
		ZOffset: -1c511
		Offset: 0,-4
	icon:
		Filename: srepicon.shp

rea2:
	Inherits: ^StructureOverlays
	idle:
		Filename: rea2.shp
		Offset: 2,0
	damaged-idle:
		Filename: rea2.shp
		Start: 1
		Offset: 2,0
	make:
		Filename: rea2make.shp
		Length: *
		Offset: 2,0
		Tick: 80
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
			JUNGLE: bib2.jun
			WINTER: bib2.win
			BARREN: bib2.bar
		Length: *
	icon:
		Filename: rea2icon.shp

scrt:
	Inherits: ^StructureOverlays
	idle:
		Filename: scrt.shp
		Offset: 3,0
	active:
		Filename: scrt.shp
	damaged-idle:
		Filename: scrt.shp
		Offset: 3,0
		Start: 1
	make:
		Filename: scrtmake.shp
		Length: *
		Offset: 3,0
		Tick: 80
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
			JUNGLE: bib3.jun
			WINTER: bib3.win
			BARREN: bib3.bar
		Length: *
	icon:
		Filename: scrticon.shp

silo.scrin:
	Inherits: ^StructureOverlays
	idle:
		Filename: siloscrin.shp
		Offset: 0,-1
	damaged-idle:
		Filename: siloscrin.shp
		Offset: 0,-1
		Start: 5
	stages:
		Filename: siloscrin.shp
		Length: 5
		Offset: 0,-1
	damaged-stages:
		Filename: siloscrin.shp
		Start: 5
		Length: 5
		Offset: 0,-1
	make:
		Filename: siloscrinmake.shp
		Length: *
		Offset: 0,-1
	bib:
		Filename: mbSILO.tem
		TilesetFilenames:
			SNOW: mbSILO.sno
			DESERT: mbSILO.des
			JUNGLE: mbSILO.jun
			WINTER: mbSILO.win
		Length: *
	icon:
		Filename: siloscrinicon.shp

silo.scrinblue:
	Inherits: ^StructureOverlays
	idle:
		Filename: siloscrinblue.shp
		Offset: 0,-1
	damaged-idle:
		Filename: siloscrinblue.shp
		Offset: 0,-1
		Start: 5
	stages:
		Filename: siloscrinblue.shp
		Length: 5
		Offset: 0,-1
	damaged-stages:
		Filename: siloscrinblue.shp
		Start: 5
		Length: 5
		Offset: 0,-1
	make:
		Filename: siloscrinmake.shp
		Length: *
		Offset: 0,-1
	bib:
		Filename: mbSILO.tem
		TilesetFilenames:
			SNOW: mbSILO.sno
			DESERT: mbSILO.des
			JUNGLE: mbSILO.jun
			WINTER: mbSILO.win
		Length: *
	icon:
		Filename: siloscrinicon.shp

sign:
	Inherits: ^StructureOverlays
	idle:
		Filename: sign.shp
	damaged-idle:
		Filename: sign.shp
		Start: 1
	bib:
		Filename: bib2.tem
		TilesetFilenames:
			SNOW: bib2.sno
			DESERT: bib2.des
			JUNGLE: bib2.jun
			WINTER: bib2.win
			BARREN: bib2.bar
		Length: *
	make:
		Filename: signmake.shp
		Length: *
		Tick: 80
	icon:
		Filename: signicon.shp

mani:
	Inherits: ^StructureOverlays
	idle:
		Filename: mani.shp
		Offset: 0, -11
	active:
		Filename: mani.shp
		Offset: 0, -11
	damaged-idle:
		Filename: mani.shp
		Start: 1
		Offset: 0, -11
	make:
		Filename: manimake.shp
		Length: *
		Tick: 80
		Offset: 0, -11
	bib:
		Filename: bib3.tem
		TilesetFilenames:
			SNOW: bib3.sno
			DESERT: bib3.des
			JUNGLE: bib3.jun
			WINTER: bib3.win
			BARREN: bib3.bar
		Length: *
	icon:
		Filename: maniicon.shp

rfgn:
	Inherits: ^StructureOverlays
	idle:
		Filename: rfgn.shp
	damaged-idle:
		Filename: rfgn.shp
		Start: 1
	active:
		Filename: rfgn.shp
	damaged-active:
		Filename: rfgn.shp
		Start: 1
	make:
		Filename: rfgnmake.shp
		Length: *
		Tick: 80
	active-overlay:
		Filename: rfgn.shp
		Start: 2
		Length: 13
		BlendMode: Subtractive
		ZOffset: 511
		Offset: 0,-30
	bib:
		Filename: mbIRON.tem
		TilesetFilenames:
			SNOW: mbIRON.sno
			INTERIOR: mbIRON.int
			DESERT: mbIRON.des
			WINTER: mbIRON.win
		Length: *
		Offset: 0,2
	icon:
		Filename: rfgnicon.shp

ptur:
	Inherits: ^StructureOverlays
	idle:
		Filename: empty.shp
	damaged-idle:
		Filename: empty.shp
	turret:
		Filename: ptur.shp
		Facings: 32
		UseClassicFacings: True
		Offset: 1,-6
	make:
		Filename: pturmake.shp
		Length: *
		Offset: 1,-6
	damaged-turret:
		Filename: ptur.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		Offset: 1,-6
	muzzle:
		Filename: scrinmuzz1.shp
		Length: 6
		Facings: 16
		BlendMode: Additive
		IgnoreWorldTint: true
	bib:
		Filename: mbGUN.tem
		TilesetFilenames:
			SNOW: mbGUN.sno
			INTERIOR: mbGUN.int
			DESERT: mbGUN.des
			JUNGLE: mbGUN.jun
			WINTER: mbGUN.win
		Length: *
		Offset: -1,-1
	icon:
		Filename: pturicon.shp

shar:
	Inherits: ^StructureOverlays
	idle:
		Filename: empty.shp
	damaged-idle:
		Filename: empty.shp
	turret:
		Filename: shar.shp
		Facings: 32
		UseClassicFacings: True
		Offset: 1,-11
	make:
		Filename: sharmake.shp
		Length: *
		Offset: 1,-11
	damaged-turret:
		Filename: shar.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		Offset: 1,-11
	muzzle:
		Filename: scrinmuzz5.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: 6
		Facings: 16
	bib:
		Filename: mbTSLA.tem
		TilesetFilenames:
			SNOW: mbTSLA.sno
			INTERIOR: mbTSLA.int
			DESERT: mbTSLA.des
			JUNGLE: mbTSLA.jun
			WINTER: mbTSLA.win
		Length: *
	icon:
		Filename: sharicon.shp

scol:
	Inherits: ^StructureOverlays
	idle:
		Filename: scol.shp
		Length: 1
		Offset: 0,-12
	damaged-idle:
		Filename: scol.shp
		Start: 1
		Length: 1
		Offset: 0,-12
	make:
		Filename: scolmake.shp
		Length: *
		Offset: 0,-12
	bib:
		Filename: mbTSLA.tem
		TilesetFilenames:
			SNOW: mbTSLA.sno
			INTERIOR: mbTSLA.int
			DESERT: mbTSLA.des
			JUNGLE: mbTSLA.jun
			WINTER: mbTSLA.win
		Length: *
	icon:
		Filename: scolicon.shp

vspk:
	Inherits: ^StructureOverlays
	idle:
		Filename: vspk.shp
		Length: *
		Frames: 0,1,2,3,4,0,0,0,0,0,0,0,0,0,0,0
		Offset: 0,-15
		Tick: 80
		ZOffset: 512
	damaged-idle:
		Filename: vspk.shp
		Frames: 0,1,2,3,4,0,0,0,0,0,0,0,0,0,0,0
		Length: *
		Offset: 0,-15
		Tick: 80
		ZOffset: 512
	make:
		Filename: vspk.shp
		Length: 14
		Start: 5
		Offset: 0,-15
		ZOffset: 512
	overlay:
		Filename: voidpulse.shp
		Length: *
		BlendMode: Subtractive
		ZOffset: -1
		Alpha: 0.3
		Offset: 1, 0
		Scale: 3
		IgnoreWorldTint: true
	bib:
		Filename: mbTSLA.tem
		TilesetFilenames:
			SNOW: mbTSLA.sno
			INTERIOR: mbTSLA.int
			DESERT: mbTSLA.des
			JUNGLE: mbTSLA.jun
			WINTER: mbTSLA.win
		Length: *
	icon:
		Filename: vspkicon.shp

#
# ---- animations
#

plasdiscsm:
	idle:
		Filename: plasdiscsm.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Tick: 50

plasdiscmd:
	idle:
		Filename: plasdiscmd.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Tick: 50

plasdisclg:
	idle:
		Filename: plasdisclg.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Tick: 50

mrdrdisc:
	idle:
		Filename: mrdrdisc.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: 2
		Facings: 8
		ZOffset: 2047
		Tick: 50
		InterpolatedFacings: 32
		Alpha: 0.8

tibglob:
	idle:
		Filename: tibglob.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Tick: 40

tibgloblg:
	idle:
		Filename: tibgloblg.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Tick: 40

atmzbolt:
	idle:
		Filename: atmzbolt.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Tick: 40

atmzbolttrail:
	idle:
		Filename: atmzbolttrail.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047

atmzbolthit:
	idle:
		Filename: atmzbolthit.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047

enrvbolt:
	idle:
		Filename: enrvbolt.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Tick: 50

enrvbolthit:
	idle:
		Filename: enrvbolthit.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047

tibexplode:
	idle:
		Filename: tibexplode.shp
		Length: *
		ZOffset: 2047

tibexplodesm:
	idle:
		Filename: tibexplodesm.shp
		Length: *
		ZOffset: 2047

tibexplodeair:
	idle:
		Filename: tibexplodeair.shp
		Length: *
		ZOffset: 2047

tibspew1:
	idle:
		Filename: tibspew1.shp
		Length: *
		ZOffset: 2047

tibspew2:
	idle:
		Filename: tibspew2.shp
		Facings: 32
		ZOffset: 2047

scrinzap:
	idle:
		Filename: scrinzap.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		ZOffset: 2047
		Facings: 32

scrinzapdown:
	idle:
		Filename: scrinzapdown.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		ZOffset: 2047
		Facings: 32

scrinzapup:
	idle:
		Filename: scrinzapup.shp
		ZOffset: 2047
		BlendMode: Additive
		IgnoreWorldTint: true
		Facings: 32

scrinzaphit:
	idle:
		Filename: scrinmuzz2.shp
		ZOffset: 2047
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: 5

scrinzaphitsm:
	idle:
		Filename: scrinzaphitsm.shp
		ZOffset: 2047
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: 3

shardhit:
	Defaults:
		ZOffset: 2047
		IgnoreWorldTint: true
		BlendMode: Additive
	idle:
		Filename: shardhit.shp
		Length: 3
	idle2:
		Filename: shardhit.shp
		Start: 3
		Length: 3
	idle3:
		Filename: shardhit.shp
		Start: 6
		Length: 3
	idle4:
		Filename: shardhit.shp
		Start: 9
		Length: 3

shard:
	idle:
		Filename: shard.shp
		Facings: 32
		ZOffset: 2047
		IgnoreWorldTint: true
		BlendMode: Additive

wchrdart:
	idle:
		Filename: wchrdart.shp
		Facings: 32
		ZOffset: 2047
		IgnoreWorldTint: true

bshardhit:
	Defaults:
		ZOffset: 2047
		IgnoreWorldTint: true
		BlendMode: Additive
	idle:
		Filename: bshardhit.shp
		Length: 3
	idle2:
		Filename: bshardhit.shp
		Start: 3
		Length: 3
	idle3:
		Filename: bshardhit.shp
		Start: 6
		Length: 3
	idle4:
		Filename: bshardhit.shp
		Start: 9
		Length: 3

bshard:
	Defaults:
		ZOffset: 2047
		IgnoreWorldTint: true
		BlendMode: Additive
	idle:
		Filename: bshard.shp
		Facings: 32
		ZOffset: 1023

leechhit:
	idle:
		Filename: leechhit.shp
		BlendMode: Additive
		Length: *
		ZOffset: 2047
		IgnoreWorldTint: true

leechhitlg:
	idle:
		Filename: leechhitlg.shp
		BlendMode: Additive
		Length: *
		ZOffset: 2047
		IgnoreWorldTint: true

mindcontrol:
	idle:
		Filename: mindcontrol.shp
		Tick: 40
		ZOffset: 2048
		Length: *
		Offset: 0,-8
		IgnoreWorldTint: true
	reverse:
		Filename: mindcontrol.shp
		Frames: 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
		Tick: 40
		ZOffset: 2048
		Length: *
		Offset: 0,-8
		IgnoreWorldTint: true

riftanim:
	Defaults:
		Filename: riftloop.shp
		Tick: 40
		Length: *
		ZOffset: 2048
		BlendMode: Subtractive
		IgnoreWorldTint: true
		Scale: 2
	idle:
	spawn:
		Filename: riftstart.shp
	die:
		Filename: riftend.shp
	fade:
		Filename: riftend.shp
	dead:
		Alpha: 0

riftanimmd:
	idle:
		Filename: riftloopmd.shp
		Tick: 40
		Length: *
		ZOffset: 2048
		BlendMode: Subtractive
		IgnoreWorldTint: true

mspk:
	idle:
		Filename: mindspark.shp
		Tick: 40
		Length: 15
		ZOffset: 2048
		BlendMode: Additive
		IgnoreWorldTint: true
	die:
		Filename: mindspark.shp
		Start: 15
		Length: 7
		ZOffset: 2048
		BlendMode: Additive
		IgnoreWorldTint: true

buzz:
	Defaults:
		Length: *
		ZOffset: 2048
		BlendMode: Additive
		IgnoreWorldTint: true
	idle:
		Filename: buzz.shp
		Tick: 60
	make:
		Filename: buzzmake.shp
	die:
		Filename: buzzmake.shp
		Frames: 5, 4, 3, 2, 1, 0

grcl:
	Defaults:
		Alpha: 0.75
		Length: *
		ZOffset: 512
	idle:
		Filename: grcl.shp
	make:
		Filename: grclmake.shp

tbcl:
	Defaults:
		Alpha: 0.75
		Length: *
		ZOffset: 512
	idle:
		Filename: tbcl.shp
	make:
		Filename: grclmake.shp

ionsurge:
	Defaults:
		Filename: ionsurgeloop.shp
		Tick: 40
		Length: *
		ZOffset: 2048
		BlendMode: Additive
		IgnoreWorldTint: true
	idle:
	make:
		Alpha: 0.08, 0.16, 0.24, 0.32, 0.40, 0.48, 0.56, 0.64, 0.72, 0.80, 0.88, 0.96, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00, 1.00
	fade:
		Alpha: 1.00, 0.96, 0.92, 0.88, 0.84, 0.80, 0.76, 0.72, 0.68, 0.64, 0.60, 0.56, 0.52, 0.48, 0.44, 0.40, 0.36, 0.32, 0.28, 0.24, 0.20, 0.16, 0.12, 0.08
	dead:
		Alpha: 0

ionsurge-overlay:
	vehicle:
		Filename: emp_fx01.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		IgnoreWorldTint: true
	infantry:
		Filename: emp_fx02.shp
		Length: *
		BlendMode: Additive
		Offset: 0, -5
		ZOffset: 512
		IgnoreWorldTint: true

atomize-overlay:
	vehicle:
		Filename: atomizeoverlay.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		IgnoreWorldTint: true
	infantry:
		Filename: atomizeoverlaysm.shp
		Length: *
		BlendMode: Additive
		Offset: 0, -5
		ZOffset: 512
		IgnoreWorldTint: true

siphon-overlay-sm:
	idle:
		Filename: leechhit.shp
		Frames: 4,5,6,7,8,9,10,11,12,13,14,15,14,13,12,11,10,9,8,7,6,5,4
		BlendMode: Alpha
		Length: *
		ZOffset: 2047
		Offset: 0, -5
		IgnoreWorldTint: true

siphon-overlay-lg:
	idle:
		Filename: leechhitlg.shp
		Frames: 4,5,6,7,8,9,10,11,12,13,14,15,14,13,12,11,10,9,8,7,6,5,4
		BlendMode: Alpha
		Length: *
		ZOffset: 2047
		IgnoreWorldTint: true

scrinwipicon:
	icon:
		Filename: wipicon.shp

ichorseed:
	idle:
		Filename: ichorseed.shp
		Tick: 80
		Length: 16
		Start: 15
		BlendMode: Additive
	die:
		Filename: ichorseed.shp
		Tick: 80
		Length: 15
		BlendMode: Additive
	fade:
		Filename: ichorseed.shp
		Tick: 80
		Length: 15
		BlendMode: Additive

resconv:
	Defaults:
		ZOffset: 1023
		BlendMode: Additive
		Tick: 80
	green:
		Filename: resconv.shp
		Length: 17
	yellow:
		Filename: resconv.shp
		Length: 17
		Start: 17
	blue:
		Filename: resconv.shp
		Length: 17
		Start: 34
	red:
		Filename: resconv.shp
		Length: 17
		Start: 51
	greensm:
		Filename: resconvsm.shp
		Length: 10
	yellowsm:
		Filename: resconvsm.shp
		Length: 10
		Start: 10
	bluesm:
		Filename: resconvsm.shp
		Length: 10
		Start: 20
	redsm:
		Filename: resconvsm.shp
		Length: 10
		Start: 30

lchrheal:
	vehicle:
		Filename: resconv.shp
		Length: 17
		Start: 51
		ZOffset: 1023
		Tick: 80
		Alpha: 0.8
	infantry:
		Filename: resconvsm.shp
		Length: 10
		Start: 30
		ZOffset: 1023
		Tick: 80
		Alpha: 0.8

strm:
	Defaults:
		Alpha: 0.85
		Offset: 1, -72, 48
		ZRamp: 1
		ZOffset: 1023
		BlendMode: Screen
		Tick: 90
		Length: *
		IgnoreWorldTint: true
	idle:
		Filename: ioncloud1.shp
	make:
		Filename: ioncloud1d.shp
		Frames: 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
	die:
		Filename: ioncloud1d.shp
	make2:
		Filename: ioncloud2d.shp
		Frames: 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
	idle2:
		Filename: ioncloud2.shp
	die2:
		Filename: ioncloud2d.shp
	idle3:
		Filename: ioncloud1.shp
		FlipX: True
	make3:
		Filename: ioncloud1d.shp
		Frames: 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
		FlipX: True
	die3:
		Filename: ioncloud1d.shp
		FlipX: True
	make4:
		Filename: ioncloud2d.shp
		Frames: 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0
		FlipX: True
	idle4:
		Filename: ioncloud2.shp
		FlipX: True
	die4:
		Filename: ioncloud2d.shp
		FlipX: True

strm2:
	Inherits: strm
	Defaults:
		Alpha: 0.7
		Offset: 1, -24, 48
		IgnoreWorldTint: true

wormhole:
	Defaults:
		Filename: wormhole.shp
		ZOffset: 1023
		BlendMode: Additive
		IgnoreWorldTint: true
	idle:
		Length: 13
	die:
		Length: 8
		Start: 13
	make:
		Frames: 20, 19, 18, 17, 16, 15, 14, 13
		Length: 8
	faded:
		Length: 13
		Alpha: 0.6
		Scale: 0.9

wormholelg:
	Inherits: wormhole
	Defaults:
		Filename: wormholelg.shp

wormholexl:
	Inherits: wormhole
	Defaults:
		Filename: wormholexl.shp
	-die:
	-make:

wormholexxl:
	Inherits: wormhole
	Defaults:
		Filename: wormholexxl.shp
	-die:
	-make:

plasmatorp1:
	Defaults:
		IgnoreWorldTint: true
		Alpha: 0.95
	idle:
		Filename: plasmatorp1.shp
		Length: 5
		Tick: 40
		ZOffset: 2048

plasmatorp2:
	Defaults:
		IgnoreWorldTint: true
		Alpha: 0.95
	idle:
		Filename: plasmatorp2.shp
		Length: 5
		Tick: 40
		ZOffset: 2048

plasmatorp3:
	Defaults:
		IgnoreWorldTint: true
		Alpha: 0.95
	idle:
		Filename: plasmatorp3.shp
		Length: 5
		Tick: 40

mshpshockwave:
	Defaults:
		IgnoreWorldTint: true
		Length: *
		Tick: 40
		ZOffset: 2047
	idle1:
		Filename: mshpshockwave.shp
		Alpha: 0.9
	idle2:
		Filename: mshpshockwave.shp
		FlipX: true
		Alpha: 0.8
	idle3:
		Filename: mshpshockwave.shp
		FlipY: true
		Alpha: 0.7
	idle4:
		Filename: mshpshockwave.shp
		FlipX: true
		FlipY: true
		Alpha: 0.6
	inner1:
		Filename: enrvbolthit.shp
		ZOffset: 2048
		Alpha: 0.9
		BlendMode: Additive
	inner2:
		Filename: enrvbolthit.shp
		FlipX: true
		ZOffset: 2048
		Alpha: 0.8
		BlendMode: Additive
	inner3:
		Filename: enrvbolthit.shp
		FlipY: true
		ZOffset: 2048
		Alpha: 0.7
		BlendMode: Additive
	inner4:
		Filename: enrvbolthit.shp
		FlipX: true
		FlipY: true
		ZOffset: 2048
		Alpha: 0.6
		BlendMode: Additive
	empty:
		Filename: empty.shp

fleetrecall:
	Defaults:
		Filename: fleetrecall.shp
		Length: *
		ZOffset: 2047
		BlendMode: Additive
		AlphaFade: True
	idle:

obltbolt:
	Defaults:
		IgnoreWorldTint: true
	idle:
		Filename: obltbolt.shp
		Length: *
		Tick: 40
		ZOffset: 3072
		BlendMode: Additive
	idle2:
		Filename: obltbolt.shp
		Length: *
		Tick: 40
		ZOffset: 3072
		BlendMode: Additive
		FlipX: true
		FlipY: true
	trail:
		Filename: obltbolttrail.shp
		Frames: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19
		Start: 0
		Length: 10
		Tick: 50
		Facings: 4
		ZOffset: 2046
		InterpolatedFacings: 32
		Alpha: 0.75
	trail2:
		Filename: obltbolttrail.shp
		Frames: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19
		Start: 0
		Length: 10
		Tick: 50
		Facings: 4
		ZOffset: 2046
		FlipX: true
		FlipY: true
		InterpolatedFacings: 32
		Alpha: 0.75
	trail3:
		Filename: obltbolttrail.shp
		Frames: 1, 0, 3, 2, 5, 4, 6, 7, 8, 9, 11, 10, 13, 12, 15, 14, 16, 17, 18, 19, 1, 0, 3, 2, 5, 4, 6, 7, 8, 9, 11, 10, 13, 12, 15, 14, 16, 17, 18, 19
		Start: 0
		Length: 10
		Tick: 50
		Facings: 4
		ZOffset: 2046
		InterpolatedFacings: 32
		Alpha: 0.75

obltboltcharge:
	Defaults:
		Filename: obltboltcharge.shp
		Length: 7
		Tick: 80
		ZOffset: 2046
		IgnoreWorldTint: true
		Alpha: 0.3
	idle:
	idle2:
		FlipX: true
	idle3:
		FlipY: true
	idle4:
		FlipX: true
		FlipY: true
	idle5:
		Frames: 6, 5, 4, 3, 2, 1, 0
	idle6:
		Frames: 6, 5, 4, 3, 2, 1, 0
		FlipX: true
	idle7:
		Frames: 6, 5, 4, 3, 2, 1, 0
		FlipY: true
	idle8:
		Frames: 6, 5, 4, 3, 2, 1, 0
		FlipX: true
		FlipY: true

nullbolt:
	idle:
		Filename: nullbolt.shp
		Length: *
		BlendMode: Additive
		ZOffset: 2047
		IgnoreWorldTint: true

nulltrail:
	idle1:
		Filename: nulltrail.shp
		Frames: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13
		Start: 0
		Length: 7
		Tick: 60
		Facings: 4
		ZOffset: 2046
		InterpolatedFacings: 32
		BlendMode: Additive
		Alpha: 0.4
	idle2:
		Filename: nulltrail.shp
		Frames: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13
		Start: 0
		Length: 7
		Tick: 60
		Facings: 4
		ZOffset: 2046
		FlipX: true
		FlipY: true
		InterpolatedFacings: 32
		BlendMode: Additive
		Alpha: 0.4

nullhit:
	idle:
		Filename: nullhit.shp
		BlendMode: Additive
		IgnoreWorldTint: true
		Length: *
		ZOffset: 2047
		Alpha: 0.5

tibmeteor:
	up:
		Filename: empty.shp
	down:
		Filename: tibmeteor.shp
		Length: *
		ZOffset: 2047
	hit:
		Filename: tibmeteorhit.shp
		Length: *
		ZOffset: 2047
		BlendMode: Alpha
		Alpha: 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0.95, 0.9, 0.85, 0.8, 0.75, 0.7, 0.65, 0.6, 0.55, 0.5, 0.45, 0.4, 0.35, 0.3 ,0.25 ,0.2 ,0.15 ,0.1, 0.1
		Offset: 0, -10

scrinpurifier:
	Inherits: ^StructureOverlays
	idle:
		Filename: scrinpurifier.shp
	purification:
		Filename: scrinpurification.shp
		Length: *
		BlendMode: Additive
		ZOffset: 2047
