# Dokumentationsquellen für die Entwicklung der „serious“-AI

- **OpenRA-Dokumentation:**  
  [https://docs.openra.net/](https://docs.openra.net/)  
  Enthält Referenzen zu Traits, Bot-Modulen und YAML-Struktur.

- **Lokale Dateien:**  
  - `mods/combined-arms/rules/ai.yaml` (KI-Definitionen)
  - `mods/combined-arms/rules/bot-modules.yaml` (verwendete Module)
  - C#-<PERSON>ien in `mods/combined-arms/logic/` (BotModule-Implementierung)

- **Debugging:**  
  Hinweise zu `BotDebug` und Debug-Optionen finden sich in der Doku und im Quellcode.

- **Community:**  
  Bei Fragen: OpenRA-Forum oder Combined Arms Discord.
