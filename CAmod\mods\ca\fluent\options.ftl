## Player options
options-tech-level =
    .infantry-only = Infantry Only
    .low = Low
    .medium = Medium
    .high = High • Superweapons Off
    .unrestricted = High • Superweapons On

checkbox-kill-bounties =
    .label = Kill Bounties
    .description = Players receive cash bonuses when killing enemy units

checkbox-redeployable-mcvs =
    .label = Redeployable MCVs
    .description = Allow undeploying Construction Yard

checkbox-force-shield =
    .label = Force Shield
    .description = Grants all factions the Force Shield support power

checkbox-naval-units =
    .label = Naval Units
    .description = Enables naval units

checkbox-reveal-on-fire =
    .label = Reveal on Fire
    .description = Units reveal themselves when firing

checkbox-balanced-harvesting =
    .label = Balanced Harvesting
    .description = Enables dynamic harvester speed to account for the direction of resources relative to refineries

checkbox-fast-regrowth =
    .label = Fast Regrowth
    .description = Resources regrow at a faster rate

dropdown-queuetype =
    .label = Production Type
    .description = Single-Queue:\n  • TD / RA1 / TS / RA2 style\n  • One queue per production type\n  • Units created at primary building\n\nMulti-Queue:\n  • C&C3 / RA3 style\n  • One queue per production structure\n  • Upgrades remain single-queue\n\nCompetitive:\n  • Multi-Queue for units\n  • Single-Queue for structures & upgrades\n  • Multiple production structures of the same type have increased cost,\n    which is reduced after building radar/tech center

options-queuetype =
    .singlequeue = Single-Queue
    .multiqueuefull = Multi-Queue
    .multiqueuescaled = Competitive

## World options
options-starting-units =
    .mcv-only = MCV Only
    .light-support = Light Support
    .heavy-support = Heavy Support

dropdown-difficulty =
    .label = Difficulty
    .description = The difficulty of the mission

options-difficulty =
    .easy = Easy
    .normal = Normal
    .hard = Hard
