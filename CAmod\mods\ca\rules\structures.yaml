MSLO:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x1Shape
	Inherits@ABOMBPOWER: ^ABombPower
	Selectable:
		Bounds: 2048, 1024
	Valued:
		Cost: 2500
	Tooltip:
		Name: Missile Silo
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 140
		Prerequisites: stek, ~structures.soviet, ~techlevel.unrestricted
		BuildLimit: 1
		Description: Provides an atomic bomb.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Atom Bomb
	Building:
		Footprint: xx
		Dimensions: 2,1
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	SupportPowerChargeBar:
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false
	WithSupportPowerActivationAnimation:
		RequiresCondition: !build-incomplete
	ProvidesPrerequisite@buildingname:
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	ProductionCostMultiplier@IRAQBONUS:
		Multiplier: 60
		Prerequisites: player.iraq
	Encyclopedia:
		Category: Soviets/Buildings

GAP:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@AUTOTARGET: ^AutoTargetAll
	Valued:
		Cost: 1000
	Tooltip:
		Name: Gap Generator
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 110
		Prerequisites: atek, ~structures.allies, ~techlevel.high
		Description: Regenerates the shroud nearby, obscuring enemy vision. Can channel the effect to reduce enemy weapon range and vision.
	TooltipExtras:
		Attributes: • Requires power to operate
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 1024, 2048, 0, -512
	WithSpriteBody:
		PauseOnCondition: disabled || empdisable || being-warped
	Health:
		HP: 50000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Armament:
		Weapon: GapBeam
		LocalOffset: 0,0,1150
		MuzzleSequence: muzzle
	WithMuzzleOverlay:
	AttackTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	Turreted:
		TurnSpeed: 512
	WithBuildingBib:
		HasMinibib: true
	CreatesShroud:
		Range: 8c0
		RequiresCondition: !disabled && !empdisable && !being-warped
	RenderShroudCircle:
	-RenderRangeCircle:
	Power:
		Amount: -60
	MustBeDestroyed:
		RequiredForShortGame: false
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	HitShape:
		Type: Rectangle
			TopLeft: -512, -512
			BottomRight: 512, 512
	Encyclopedia:
		Category: Allies/Buildings

SPEN:
	Inherits: ^Building
	Inherits@WATERSTRUCTURE: ^WaterStructure
	Inherits@NAV: ^ProducesNaval
	Selectable:
		Bounds: 3072, 2048
	RenderSprites:
		PlayerPalette: playernavy
	InfiltrateToCreateProxyActor:
		Proxy: powerproxy.sonarpulse
		Types: GrantSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Valued:
		Cost: 800
	Tooltip:
		Name: Sub Pen
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 220
		Prerequisites: anypower, ~structures.soviet, ~techlevel.navy, ~techlevel.low
		Description: Produces and repairs\nsubmarines and transports.
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	RequiresBuildableArea:
		Adjacent: 8
	-GivesBuildableArea:
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-213,0
		Facing: 384
		ExitCell: -1,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-213,0
		Facing: 640
		ExitCell: 3,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: 0,0,0
		Facing: 128
		ExitCell: 0,0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: 0,0,0
		Facing: 896
		ExitCell: 2,0
	Exit@b1:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,1024,0
		Facing: 640
		ExitCell: 0,2
	Exit@b2:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,1024,0
		Facing: 896
		ExitCell: 2,2
	Exit@b3:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,-1024,0
		Facing: 384
		ExitCell: 0,0
	Exit@b4:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,-1024,0
		Facing: 128
		ExitCell: 2,0
	-SpawnActorsOnSellCA:
	RepairsUnits:
		HpPerStep: 1000
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: !forceshield && !invulnerability && !being-warped
	RallyPoint:
	Power:
		Amount: -30
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 10c0
		RequiresCondition: !(empdisable || being-warped)
	RenderDetectionCircle:
	ProvidesPrerequisite@buildingname:
	MapEditorData:
		ExcludeTilesets: INTERIOR
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -598
			BottomRight: 1536, 598
	HitShape@TOPANDBOTTOM:
		TargetableOffsets: 811,0,0, -811,0,0
		Type: Rectangle
			TopLeft: -555, -1110
			BottomRight: 555, 1110
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Encyclopedia:
		Category: Soviets/Buildings

SYRD:
	Inherits: ^Building
	Inherits@WATERSTRUCTURE: ^WaterStructure
	Inherits@NAV: ^ProducesNaval
	Selectable:
		Bounds: 3072, 2048
	RenderSprites:
		PlayerPalette: playernavy
	InfiltrateToCreateProxyActor:
		Proxy: powerproxy.sonarpulse
		Types: GrantSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 210
		Prerequisites: anypower, ~structures.allies, ~techlevel.navy, ~techlevel.low
		Description: Produces and repairs\nships and transports.
	Valued:
		Cost: 1000
	Tooltip:
		Name: Naval Yard
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	RequiresBuildableArea:
		Adjacent: 8
	-GivesBuildableArea:
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,1024,0
		Facing: 640
		ExitCell: 0,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,1024,0
		Facing: 896
		ExitCell: 2,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,-1024,0
		Facing: 384
		ExitCell: 0,0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,-1024,0
		Facing: 128
		ExitCell: 2,0
	-SpawnActorsOnSellCA:
	RepairsUnits:
		HpPerStep: 1000
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: !forceshield && !invulnerability && !being-warped
	RallyPoint:
	Power:
		Amount: -30
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 10c0
		RequiresCondition: !(empdisable || being-warped)
	RenderDetectionCircle:
	ProvidesPrerequisite@buildingname:
	MapEditorData:
		ExcludeTilesets: INTERIOR
	HitShape:
		TargetableOffsets: 768,0,0, 768,-1024,0, 768,1024,0
		Type: Rectangle
			TopLeft: -1536, -1152
			BottomRight: 1536, 598
	HitShape@BOTTOM:
		TargetableOffsets: -768,0,0
		Type: Rectangle
			TopLeft: -512, 598
			BottomRight: 512, 1110
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Encyclopedia:
		Category: Allies/Buildings

IRON:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x1Shape
	Inherits@IRONCURTAINPOWER: ^IronCurtainPower
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 130
		Prerequisites: stek, ~structures.soviet, ~techlevel.high
		BuildLimit: 1
		Description: Makes a group of units invulnerable for a short time.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Iron Curtain
	Valued:
		Cost: 1500
	Tooltip:
		Name: Iron Curtain
	Building:
		Footprint: xx
		Dimensions: 2,1
	Selectable:
		Bounds: 2048, 2048, 0, -512
		DecorationBounds: 2133, 2133, 0, -512
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
		HasMinibib: true
	SupportPowerChargeBar:
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	Encyclopedia:
		Category: Soviets/Buildings

PDOX:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x2Shape
	Inherits@CHRONOSHIFTPOWER: ^ChronoshiftPower
	Inherits@TIMEWARPPOWER: ^TimeWarpPower
	Selectable:
		Bounds: 2048, 2048
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 130
		Prerequisites: atek, ~structures.allies, ~techlevel.high
		BuildLimit: 1
		Description: Can be used to teleports units anywhere on the map.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Chronoshift
	Valued:
		Cost: 1500
	Tooltip:
		Name: Chronosphere
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
		HasMinibib: true
	ValidFactions:
		Factions: allies, england, france, germany, usa
	ProvidesPrerequisiteValidatedFaction@germany:
		Factions: germany
		Prerequisite: pdox.germany
	WithChronosphereOverlay:
		Image: chronobubble
		WarpInSequence: warpin
		WarpOutSequence: warpout
		Palette: ra2effect-ignore-lighting-alpha75
	SupportPowerChargeBar:
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false
	ProvidesPrerequisite@buildingname:
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	ProductionCostMultiplier@GermanyBonus:
		Multiplier: 80
		Prerequisites: player.germany
	Encyclopedia:
		Category: Allies/Buildings

TSLA:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 80
		Prerequisites: vehicles.any, ~structures.soviet, ~techlevel.medium
		Description: Advanced base defense.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Requires power to operate\n• Can detect cloaked units\n• Can be buffed or made work during low power by Shock Troopers
	Valued:
		Cost: 1200
	Tooltip:
		Name: Tesla Coil
	Selectable:
		DecorationBounds: 1024, 1706, 0, -341
	Health:
		HP: 37500
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Targetable@TeslaBoost:
		TargetTypes: TeslaBoost
		RequiresCondition: !powerdown
	WithBuildingBib:
		HasMinibib: true
	WithTeslaChargeAnimation:
	Armament:
		Weapon: TeslaZap
		LocalOffset: 0,0,896
		RequiresCondition: (!lowpower && !charged) || (lowpower && charged == 1)
	Armament@1:
		Weapon: TeslaZapBoost1
		LocalOffset: 0,0,896
		RequiresCondition: (!lowpower && charged == 1) || (lowpower && charged == 2)
	Armament@2:
		Weapon: TeslaZapBoost2
		LocalOffset: 0,0,896
		RequiresCondition: (!lowpower && charged == 2) || (lowpower && charged >= 3)
	Armament@3:
		Weapon: TeslaZapBoost3
		LocalOffset: 0,0,896
		RequiresCondition: !lowpower && charged >= 3
	AttackTesla:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
		ChargeAudio: tslachg2.aud
		MaxCharges: 3
		ReloadDelay: 120
	Power:
		Amount: -75
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@TeslaCoilOrSovietTechCenter:
		Prerequisite: tslaorstek
	DetectCloaked:
		RequiresCondition: !(disabled || empdisable || being-warped)
	ProductionCostMultiplier@RussiaBonus:
		Multiplier: 90
		Prerequisites: player.russia
	GrantConditionOnPowerState@LOWPOWER:
		RequiresCondition: !charged
	ExternalCondition@CHARGED:
		Condition: charged
	WithDecoration@CHARGED:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		Position: BottomLeft
		ValidRelationships: Ally
		RequiresCondition: charged
		BlinkInterval: 64
		RequiresSelection: true
	Encyclopedia:
		Category: Soviets/Defenses

AGUN:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetAirICBM
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@AntiAirDefense: ^AntiAirDefense
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 90
		Prerequisites: vehicles.any, ~structures.allies, ~techlevel.medium
		Description: Anti-aircraft base defense.
	TooltipExtras:
		Strengths: • Strong vs Aircraft
		Weaknesses: • Cannot attack ground units
		Attributes: • Requires power to operate\n• Can detect cloaked aircraft
	Valued:
		Cost: 800
	Tooltip:
		Name: AA Gun
	Selectable:
		DecorationBounds: 1024, 1365, 0, -170
	Health:
		HP: 50000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 60
		InitialFacing: 832
		RealignDelay: -1
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
		Recoils: false
	WithTurretAttackAnimation:
		Sequence: recoil
	Armament:
		Weapon: ZSU-23
		LocalOffset: 520,100,450, 520,-150,450
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	WithMuzzleOverlay:
	RenderRangeCircle:
		RangeCircleType: DefenseRangeAA
	Power:
		Amount: -40
	ClassicFacingBodyOrientation:
	DetectCloaked:
		Range: 7c0
		DetectionTypes: AirCloak
		RequiresCondition: !(disabled || empdisable || being-warped)
	Encyclopedia:
		Category: Allies/Defenses

DOME:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@SOVIETRADARPOWERS: ^SovietRadarPowers
	Inherits@VEILOFWARPOWER: ^VeilOfWarPower
	Inherits@CLUSTERMINESPOWER: ^ClusterMinesPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer1: ^ProductionOptimizer1
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-384,0, 630,384,0, -700,-512,0, -700,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 90
		Prerequisites: anyrefinery, ~structures.ra, ~techlevel.medium
		Description: Provides an overview of the battlefield.
	TooltipExtras:
		Attributes: • Requires power to operate\n• Detects nearby enemy vehicles, aircraft and structures in fog of war
	Valued:
		Cost: 1800
	Tooltip:
		Name: Radar Dome
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetShroudInfiltrate
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
	ProvidesRadar:
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	RangedGpsRadarProvider:
		Range: 12c0
		TargetTypes: Vehicle, Air, Structure
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	WithRangeCircle:
		Type: RadarDetection
		Range: 12c0
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	SupportPowerChargeBar:
	InfiltrateForExploration:
		Types: ResetShroudInfiltrate
		PlayerExperience: 15
	Power:
		Amount: -40
	ValidFactions:
		Factions: allies, england, france, germany, usa, soviet, russia, ukraine, iraq, yuri
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@anyradar:
		Prerequisite: anyradar
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@radarorrepair:
		Prerequisite: radarorrepair
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@FlameTowerOrRadar:
		Prerequisite: fturorradar
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@allrad:
		Factions: allies, england, france, germany, usa
		Prerequisite: radar.allies
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@engrad:
		Factions: england
		Prerequisite: radar.england
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@frarad:
		Factions: france
		Prerequisite: radar.france
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@sovrad:
		Factions: soviet, russia, ukraine, iraq, yuri
		Prerequisite: radar.soviet
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@rusrad:
		Factions: russia
		Prerequisite: radar.russia
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@ukrrad:
		Factions: ukraine
		Prerequisite: radar.ukraine
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@irarad:
		Factions: iraq
		Prerequisite: radar.iraq
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@yurirad:
		Factions: yuri
		Prerequisite: radar.yuri
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active
		RequiresCondition: !jammed && !disabled && !being-warped
	ExternalCondition@JAMMED:
		Condition: jammed
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

PBOX:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	Tooltip:
		Name: Pillbox
	Building:
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 40
		Prerequisites: infantry.any, ~structures.pbox, ~techlevel.low
		Description: Anti-infantry base defense.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		Attributes: • Can detect cloaked units
	Valued:
		Cost: 600
	CustomSellValue:
		Value: 200
	Health:
		HP: 40000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 512
	-QuantizeFacingsFromSequence:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 8
	Armament:
		Weapon: vulcan
		LocalOffset: 341,0,86
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	RenderRangeCircle:
		FallbackRange: 6c0
	Power:
		Amount: -15
	WithMuzzleOverlay:
	DetectCloaked:
		Range: 5c0
	Encyclopedia:
		Category: Allies/Defenses

HBOX:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	Tooltip:
		Name: Camo Pillbox
	Building:
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 50
		Prerequisites: infantry.any, ~structures.england, ~techlevel.low
		Description: Anti-infantry base defense.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		Attributes: • Can detect cloaked units\n• Camouflaged when idle
	Valued:
		Cost: 650
	CustomSellValue:
		Value: 220
	Health:
		HP: 40000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Cloak:
		InitialDelay: 125
		CloakDelay: 60
		IsPlayerPalette: true
		PauseOnCondition: cloak-force-disabled || being-warped
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Turreted:
		TurnSpeed: 512
	-QuantizeFacingsFromSequence:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 8
	RenderRangeCircle:
		FallbackRange: 6c0
	Armament:
		Weapon: vulcan
		LocalOffset: 341,0,86
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	Power:
		Amount: -15
	-MustBeDestroyed:
	WithMuzzleOverlay:
	DetectCloaked:
		Range: 5c0
	Encyclopedia:
		Category: Allies/Defenses
	EncyclopediaExtras:
		Subfaction: england

GUN:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 70
		Prerequisites: infantry.any, ~structures.allies, ~techlevel.low
		Description: Basic anti-tank base defense.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor
		Weaknesses: • Weak vs Infantry\n• Cannot attack Aircraft
		Attributes: • Can detect cloaked units
	Valued:
		Cost: 800
	CustomSellValue:
		Value: 300
	Tooltip:
		Name: Turret
	Building:
	Health:
		HP: 46500
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 48
		InitialFacing: 192
		RealignDelay: -1
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: !build-incomplete
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
		Recoils: false
	WithTurretAttackAnimation:
		Sequence: recoil
	Armament:
		Weapon: TurretGun
		LocalOffset: 512,0,112
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	WithMuzzleOverlay:
	Power:
		Amount: -25
	ClassicFacingBodyOrientation:
	DetectCloaked:
		Range: 5c0
	Encyclopedia:
		Category: Allies/Defenses

FTUR:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 60
		Prerequisites: infantry.any, ~structures.ftur, ~techlevel.low
		Description: Anti-infantry base defense.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft\n• Has difficulty hitting moving targets
		Attributes: • Can detect cloaked units
	Valued:
		Cost: 600
	CustomSellValue:
		Value: 200
	Tooltip:
		Name: Flame Tower
	Building:
	Health:
		HP: 40000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 512
		Offset: 0,0,112
	Armament:
		Weapon: FireballLauncher
		LocalOffset: 512,0,0
	AttackTurreted:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	-QuantizeFacingsFromSequence:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 8
	Power:
		Amount: -15
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@FlameTowerOrRadar:
		Prerequisite: fturorradar
	FireWarheadsOnDeath:
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode
	DetectCloaked:
		Range: 5c0
	Encyclopedia:
		Category: Soviets/Defenses

SAM:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetAirICBM
	Inherits@SHAPE: ^2x1Shape
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@AntiAirDefense: ^AntiAirDefense
	Selectable:
		Bounds: 2048, 1024
	HitShape:
		Type: Rectangle
			TopLeft: -768,-512
			BottomRight: 768,512
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 100
		Prerequisites: anyradar, ~structures.soviet, ~techlevel.medium
		Description: Anti-aircraft base defense.
	TooltipExtras:
		Strengths: • Strong vs Aircraft
		Weaknesses: • Cannot attack ground units
		Attributes: • Requires power to operate\n• Can detect cloaked aircraft
	Valued:
		Cost: 750
	Tooltip:
		Name: SAM Site
	Building:
		Footprint: xx
		Dimensions: 2,1
	Health:
		HP: 50000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 120
		InitialFacing: 0
		RealignDelay: -1
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
	Armament:
		Weapon: Nike
		LocalOffset: 0,0,320
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	WithMuzzleOverlay:
	RenderRangeCircle:
		RangeCircleType: DefenseRangeAA
	Power:
		Amount: -40
	ClassicFacingBodyOrientation:
	DetectCloaked:
		Range: 7c0
		DetectionTypes: AirCloak
		RequiresCondition: !(disabled || empdisable || being-warped)
	Encyclopedia:
		Category: Soviets/Defenses

ATEK:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^2x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@GPSPOWER: ^GpsPower
	Inherits@FORCESHIELDPOWER: ^ForceShieldPower
	Inherits@TEMPINCPOWER: ^TemporalIncursionPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer2: ^ProductionOptimizer2
	Inherits@DevelopmentPolicyDiscount: ^DevelopmentPolicyDiscount
	Selectable:
		Bounds: 2048, 2048
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 140
		Prerequisites: vehicles.any, anyradar, ~structures.allies, ~techlevel.high
		Description: Provides Allied advanced technologies.
	TooltipExtras:
		Attributes: • Requires power to operate\n• Special Ability: GPS Satellite
	Valued:
		Cost: 1800
	Tooltip:
		Name: Allied Tech Center
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	SupportPowerChargeBar:
	Power:
		Amount: -150
	GrantConditionOnFogEnabled@FOGENABLED:
		Condition: fogenabled
	ExternalCondition@DUMMY:
		Condition: gps-launched
	GrantConditionOnPrerequisite@PREVGPS:
		Condition: gps-launched
		Prerequisites: gps.satellite
	GrantConditionOnPrerequisite@PREVGPSSCAN:
		Condition: gps-firstscan
		Prerequisites: gps.satellite.firstscan
	SpawnActorOnDeath@GPS:
		Actor: gps.satellite.firstscan
		SpawnAfterDefeat: false
		RequiresCondition: gps-launched && !gps-firstscan && fogenabled
	SpawnActorOnSell@GPS:
		Actor: gps.satellite.firstscan
		RequiresCondition: gps-launched && !gps-firstscan && fogenabled
	ProvidesPrerequisite@FOGENABLED:
		Prerequisite: fogenabled
		RequiresCondition: fogenabled
	ValidFactions:
		Factions: allies, england, germany, france, usa
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite:
		Prerequisite: techcenter.any
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@AtekOrHq:
		Prerequisite: atekoralhq
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@germanyatek:
		Factions: germany
		Prerequisite: atek.germany
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@franceatek:
		Factions: france
		Prerequisite: atek.france
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@usaatek:
		Factions: usa
		Prerequisite: atek.usa
		RequiresCondition: !tech-locked
	Production@UPG:
		Produces: Upgrade, SpySatellite
	Exit@UPG:
		ProductionTypes: Upgrade, SpySatellite
	GrantExternalConditionPowerCA@FSHIELD:
		ActiveSequence: false-active
	Encyclopedia:
		Category: Allies/Buildings

ALHQ:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ForceShieldPower: ^ForceShieldPower
	Inherits@CryostormPower: ^CryostormPower
	Inherits@HeliosBombPower: ^HeliosBombPower
	Inherits@BlackSkyStrikePower: ^BlackSkyStrikePower
	Inherits@ProductionOptimizer2: ^ProductionOptimizer2
	Selectable:
		Bounds: 2048, 3072, 0, -512
		DecorationBounds: 2133, 3157, 0, -512
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 120000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	SupportPowerChargeBar:
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		Prerequisites: vehicles.any, influence.level2, ~structures.allies, ~techlevel.high
		Description: Provides access to coalitions and other advanced technologies.
		BuildLimit: 1
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Valued:
		Cost: 1800
	Tooltip:
		Name: Allied HQ
	Power:
		Amount: -150
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	SpawnRandomActorOnDeath:
		RequiresCondition: !being-warped
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@AtekOrHq:
		Prerequisite: atekoralhq
		RequiresCondition: !tech-locked
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	Production@UPG:
		Produces: Upgrade, Coalition
	Exit@UPG:
		ProductionTypes: Upgrade, Coalition
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	FreeActor@ProductionOptimizer2:
		RequiresCondition: optimized-production1
	GrantConditionOnPrerequisite@ProductionOptimizer1:
		Condition: optimized-production1
		Prerequisites: optimized.production1
	Encyclopedia:
		Category: Allies/Buildings
		Scale: 1.8

WEAP:
	Inherits: ^Building
	Inherits@SHAPE: ^3x2Shape
	Inherits@NOMCV: ^UnlocksMcvIfNoneOwned
	Inherits@VEH: ^ProducesVehicles
	Selectable:
		Bounds: 3072, 2048
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 80
		Prerequisites: anyrefinery, ~structures.ra, ~techlevel.low
		Description: Produces vehicles.
	Valued:
		Cost: 2000
	Tooltip:
		Name: War Factory
	Building:
		Footprint: xxx xxx +++
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	WithProductionDoorOverlayCA:
		RequiresCondition: !build-incomplete
		Sequence: build-top
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 213,-128,0
		ExitCell: 1,2
	TracksCapturedFaction:
	ValidFactions:
		Factions: allies, england, france, germany, usa, soviet, russia, ukraine, iraq, yuri
	ProvidesPrerequisite@any:
		Prerequisite: vehicles.any
	ProvidesPrerequisite@human:
		Prerequisite: vehicles.human
	ProvidesPrerequisiteValidatedFaction@allies:
		Factions: allies, england, france, germany, usa
		Prerequisite: vehicles.allies
	ProvidesPrerequisite@ra:
		Prerequisite: vehicles.ra
	ProvidesPrerequisiteValidatedFaction@england:
		Factions: england
		Prerequisite: vehicles.england
	ProvidesPrerequisiteValidatedFaction@france:
		Factions: france
		Prerequisite: vehicles.france
	ProvidesPrerequisiteValidatedFaction@germany:
		Factions: germany
		Prerequisite: vehicles.germany
	ProvidesPrerequisiteValidatedFaction@usa:
		Factions: usa
		Prerequisite: vehicles.usa
	ProvidesPrerequisiteValidatedFaction@soviet:
		Factions: soviet, russia, ukraine, iraq, yuri
		Prerequisite: vehicles.soviet
	ProvidesPrerequisiteValidatedFaction@russia:
		Factions: russia
		Prerequisite: vehicles.russia
	ProvidesPrerequisiteValidatedFaction@ukraine:
		Factions: ukraine
		Prerequisite: vehicles.ukraine
	ProvidesPrerequisiteValidatedFaction@iraq:
		Factions: iraq
		Prerequisite: vehicles.iraq
	ProvidesPrerequisiteValidatedFaction@yuri:
		Factions: yuri
		Prerequisite: vehicles.yuri
	ProvidesPrerequisiteValidatedFaction@1tnk:
		Factions: allies, france, germany, usa
		Prerequisite: vehicles.1tnk
	ProvidesPrerequisiteValidatedFaction@qtnk:
		Factions: soviet, russia, ukraine, iraq
		Prerequisite: vehicles.qtnk
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	InfiltrateToCreateProxyActor:
		Proxy: vehicles.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.weap
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		UseTargetFaction: true
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.weap
		OwnerType: Master
	-ActorPreviewPlaceBuildingPreview:
	SequencePlaceBuildingPreview:
		Sequence: place
	GrantExternalConditionToProduced@DRONEEXIT:
		Condition: radarenabled
		Duration: 100
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

FACT:
	Inherits: ^Building
	Inherits@BOTI: ^BotFallbackInsurance
	Inherits@BLD: ^ProducesBuildings
	Selectable:
		Bounds: 3072, 3072
	Building:
		Footprint: xxX xxx XxX ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 1000
		Prerequisites: ~disabled
		Description: Produces structures.
	TracksCapturedFaction:
	ValidFactions:
		Factions: allies, england, france, germany, usa, soviet, russia, ukraine, iraq, yuri
	ProvidesPrerequisite@ra:
		Prerequisite: structures.ra
	ProvidesPrerequisiteValidatedFaction@allies:
		Factions: allies, england, france, germany, usa
		Prerequisite: structures.allies
	ProvidesPrerequisiteValidatedFaction@england:
		Factions: england
		Prerequisite: structures.england
	ProvidesPrerequisiteValidatedFaction@france:
		Factions: france
		Prerequisite: structures.france
	ProvidesPrerequisiteValidatedFaction@germany:
		Factions: germany
		Prerequisite: structures.germany
	ProvidesPrerequisiteValidatedFaction@usa:
		Factions: usa
		Prerequisite: structures.usa
	ProvidesPrerequisiteValidatedFaction@soviet:
		Factions: soviet, russia, ukraine, iraq, yuri
		Prerequisite: structures.soviet
	ProvidesPrerequisiteValidatedFaction@russia:
		Factions: russia
		Prerequisite: structures.russia
	ProvidesPrerequisiteValidatedFaction@ukraine:
		Factions: ukraine
		Prerequisite: structures.ukraine
	ProvidesPrerequisiteValidatedFaction@iraq:
		Factions: iraq
		Prerequisite: structures.iraq
	ProvidesPrerequisiteValidatedFaction@yuri:
		Factions: yuri
		Prerequisite: structures.yuri
	ProvidesPrerequisite@wall:
		Prerequisite: structures.wall
	ProvidesPrerequisiteValidatedFaction@sandbag:
		Factions: allies, england, france, germany, usa
		Prerequisite: structures.sandbag
	ProvidesPrerequisiteValidatedFaction@apwr:
		Factions: allies, england, france, germany, usa, soviet, ukraine, iraq, yuri
		Prerequisite: structures.apwr
	ProvidesPrerequisiteValidatedFaction@pbox:
		Factions: allies, france, germany, usa
		Prerequisite: structures.pbox
	ProvidesPrerequisiteValidatedFaction@pris:
		Factions: allies, england, germany, usa
		Prerequisite: structures.pris
	ProvidesPrerequisiteValidatedFaction@ftur:
		Factions: soviet, russia, ukraine, yuri
		Prerequisite: structures.ftur
	ProvidesPrerequisite@anyconyard:
		Prerequisite: anyconyard
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Valued:
		Cost: 3000
	Tooltip:
		Name: Construction Yard
	BaseBuilding:
	Transforms:
		RequiresCondition: factundeploy
		PauseOnCondition: chrono-vortex || being-captured || c4 || tnt || build-incomplete || being-warped || hacked
		IntoActor: mcv
		Offset: 1,1
		Facing: 384
	TransformsIntoMobile:
		RequiresCondition: factundeploy
		Locomotor: heavywheeled
		RequiresForceMove: true
	TransformsIntoPassenger:
		RequiresCondition: factundeploy
		CargoType: Vehicle
		RequiresForceMove: true
	TransformsIntoRepairable:
		RequiresCondition: factundeploy
		RepairActors: fix, rep
		RequiresForceMove: true
	TransformsIntoTransforms:
		RequiresCondition: factundeploy && build-incomplete
	Sellable:
		RequiresCondition: !build-incomplete && !chrono-vortex && !being-captured && !c4 && !being-warped && !hacked
	GrantConditionOnPrerequisite@GLOBALFACTUNDEPLOY:
		Condition: factundeploy
		Prerequisites: global-factundeploy
	BaseProvider:
		Range: 16c0
		PauseOnCondition: being-captured
		RequiresCondition: !economy-policy2
	BaseProvider@EconomyPolicy2:
		Range: 20c0
		PauseOnCondition: being-captured
		RequiresCondition: economy-policy2
	GrantConditionOnPrerequisite@EconomyPolicy2:
		Condition: economy-policy2
		Prerequisites: economy.policy, influence.level2
	WithBuildingBib:
	WithBuildingPlacedAnimation:
		RequiresCondition: !build-incomplete && !chrono-vortex
	Power:
		Amount: 0
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
	HitShape:
		TargetableOffsets: 1273,939,0, -980,-640,0, -980,640,0
		Type: Rectangle
			TopLeft: -1536, -1536
			BottomRight: 1536, 1536
	ConyardChronoReturn:
		ReturnOriginalActorOnCondition: build-incomplete
		Condition: chrono-vortex
		Damage: 950
	TransferTimedExternalConditionOnTransform:
		Condition: invulnerability
	TransferTimedExternalConditionOnTransform@INVIS:
		Condition: invisibility
	SpawnActorsOnSellCA:
		ActorTypes: e1,e1,e1,e1,e6
	-UpdatesBuildOrder:
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

PROC:
	Inherits: ^Building
	Inherits@BOTREF: ^BotRefinery
	Inherits@ResourceDrainable: ^ResourceDrainable
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	Inherits@EconomyPolicyTimeReduction: ^EconomyPolicyTimeReduction
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 60
		Prerequisites: anypower, ~structures.ra
		Description: Processes raw Tiberium, Ore and Gems into credits.
		BuildDuration: 1400
	Valued:
		Cost: 1800
	Tooltip:
		Name: Ore Refinery
	Building:
		Footprint: _X_ xxx X++ ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	Selectable:
		Bounds: 3072, 2512, 0, 256
		DecorationBounds: 3072, 2986, 0, -85
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: StealCreditsInfiltrate
	Health:
		HP: 90000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Refinery:
	DockHost:
		Type: Unload
		DockAngle: 256
		DockOffset: 0, 1c0, 0
	StoresPlayerResourcesCA:
		Capacity: 2000
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 17
		FullSequence: pip-yellow
	CustomSellValue:
		Value: 400
	GrantConditionOnPrerequisite@CHARVUPG:
		Prerequisites: charv.upgrade
		Condition: charv-upgraded
	GrantConditionOnPrerequisite@NOCHARVUPG:
		Prerequisites: !charv.upgrade
		Condition: charv-notupgraded
	GrantCondition@SPAWNHARV:
		Condition: spawn-harv
		GrantPermanently: true
		RequiresCondition: charv-notupgraded
	GrantCondition@SPAWNCHARV:
		Condition: spawn-charv
		GrantPermanently: true
		RequiresCondition: charv-upgraded
	FreeActor:
		Actor: HARV
		SpawnOffset: 1,2
		Facing: 256
		RequiresCondition: spawn-harv && !spawn-charv && !build-incomplete
	FreeActor@CHARV:
		Actor: HARV.Chrono
		SpawnOffset: 1,2
		Facing: 256
		RequiresCondition: spawn-charv && !spawn-harv && !build-incomplete
	InfiltrateForCash:
		Percentage: 50
		Types: StealCreditsInfiltrate
		InfiltrationNotification: CreditsStolen
		PlayerExperience: 15
	WithBuildingBib:
	WithIdleOverlay@TOP:
		RequiresCondition: !build-incomplete
		Sequence: idle-top
	Power:
		Amount: -30
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@anyrefinery:
		Prerequisite: anyrefinery
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -512
			BottomRight: 1536, 598
	HitShape@TOP:
		TargetableOffsets: 1680,0,0
		Type: Rectangle
			TopLeft: -512, -1536
			BottomRight: 512, -512
	HitShape@BOTTOMLEFT:
		TargetableOffsets: -1260,-1024,0
		Type: Rectangle
			TopLeft: -1536, 598
			BottomRight: -512, 1280
	-ActorPreviewPlaceBuildingPreview:
	SequencePlaceBuildingPreview:
		Sequence: idle
	CashHackable:
	UpdatesCount:
		Type: Refineries
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

SILO:
	Inherits: ^Building
	Selectable:
		Bounds: 1024, 1024
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 35
		Prerequisites: anyrefinery, ~structures.ra
		Description: Stores excess refined\nTiberium, Ore and Gems.
	Valued:
		Cost: 150
	Tooltip:
		Name: Silo
	-GivesBuildableArea:
	Health:
		HP: 30000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: true
	-WithSpriteBody:
	WithResourceLevelSpriteBody:
		Sequence: stages
	StoresPlayerResourcesCA:
		Capacity: 3000
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 5
		FullSequence: pip-yellow
	-MustBeDestroyed:
	-SpawnActorsOnSellCA:
	Power:
		Amount: -10
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	-UpdatesBuildOrder:
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

HPAD:
	Inherits: ^Building
	Inherits@SHAPE: ^2x2Shape
	Inherits@AIRCRAFTREPAIR: ^RepairsAircraftWithRepairBay
	Inherits@AIR: ^ProducesHelicopters
	Inherits@SOVIETBOMBINGPOWERS: ^SovietBombingPowers
	Inherits@STRAFINGRUNPOWER: ^StrafingRunPower
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 768,-512,0, 768,512,0, -281,-512,0, -630,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 120
		Prerequisites: radarorrepair, ~structures.allies, ~techlevel.medium
		Description: Produces and reloads helicopters and VTOL aircraft.
	Valued:
		Cost: 500
	Tooltip:
		Name: Helipad
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-256,0
		ExitCell: 0,0
		Facing: 896
	RallyPoint:
	Reservable:
	Power:
		Amount: -20
	ValidFactions:
		Factions: allies, england, france, germany, usa, russia, ukraine, iraq, yuri
	ProvidesPrerequisiteValidatedFaction@allies:
		Factions: allies, england, france, germany, usa
		Prerequisite: aircraft.allies
	ProvidesPrerequisiteValidatedFaction@usa:
		Factions: usa
		Prerequisite: aircraft.usa
	ProvidesPrerequisiteValidatedFaction@soviets:
		Factions: soviet, russia, ukraine, iraq, yuri
		Prerequisite: aircraft.soviet
	ProvidesPrerequisiteValidatedFaction@russia:
		Factions: russia
		Prerequisite: aircraft.russia
	ProvidesPrerequisiteValidatedFaction@ukraine:
		Factions: ukraine
		Prerequisite: aircraft.ukraine
	ProvidesPrerequisiteValidatedFaction@iraq:
		Factions: iraq
		Prerequisite: aircraft.iraq
	ProvidesPrerequisiteValidatedFaction@yuri:
		Factions: yuri
		Prerequisite: aircraft.yuri
	ProvidesPrerequisiteValidatedFaction@kiro:
		Factions: soviet, russia, ukraine, iraq
		Prerequisite: aircraft.kiro
	ProvidesPrerequisiteValidatedFaction@chinookvisible:
		Factions: allies, england, france, germany, gdi, zocom, eagle, talon, arc, nod, blackh, legion, marked, shadow
		Prerequisite: aircraft.chinookvisible
	ProvidesPrerequisiteValidatedFaction@chinook:
		Factions: allies, england, france, germany, gdi, zocom, eagle, talon, arc, nod, blackh, legion, marked, shadow
		Prerequisite: aircraft.chinook
	ProvidesPrerequisiteValidatedFaction@nhaw:
		Factions: usa
		Prerequisite: aircraft.nhaw
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate, StealTechInfiltrate
	InfiltrateToCreateProxyActor:
		Types: GrantSupportPowerInfiltrate
		Proxy: powerproxy.paratroopers.allies
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.hpad
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		UseTargetFaction: true
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.hpad
		OwnerType: Master
	Encyclopedia:
		Category: Allies/Buildings

AFLD:
	Inherits: ^Building
	Inherits@SHAPE: ^3x2Shape
	Inherits@AIRCRAFTREPAIR: ^RepairsAircraftWithRepairBay
	Inherits@AIR: ^ProducesAircraft
	Inherits@SOVIETBOMBINGPOWERS: ^SovietBombingPowers
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 420,0,0, 420,-1024,0, 420,1024,0, -777,0,0, -777,-1024,0, -777,1024,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 130
		Prerequisites: radarorrepair, ~structures.soviet, ~techlevel.medium
		Description: Produces and reloads aircraft.
	Valued:
		Cost: 500
	Tooltip:
		Name: Airfield
	Selectable:
		Bounds: 3072, 2176, 0, -256
	Building:
		Footprint: xxx xxx
		Dimensions: 3,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		ExitCell: 1,1
		Facing: 768
	RallyPoint:
	Reservable:
	ValidFactions:
		Factions: soviet, russia, ukraine, iraq, yuri
	ProvidesPrerequisite@soviet:
		Prerequisite: aircraft.soviet
	ProvidesPrerequisiteValidatedFaction@russia:
		Factions: russia
		Prerequisite: aircraft.russia
	ProvidesPrerequisiteValidatedFaction@ukraine:
		Factions: ukraine
		Prerequisite: aircraft.ukraine
	ProvidesPrerequisiteValidatedFaction@iraq:
		Factions: iraq
		Prerequisite: aircraft.iraq
	ProvidesPrerequisiteValidatedFaction@yuri:
		Factions: yuri
		Prerequisite: aircraft.yuri
	ProvidesPrerequisiteValidatedFaction@kiro:
		Factions: soviet, russia, ukraine, iraq
		Prerequisite: aircraft.kiro
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
	SupportPowerChargeBar:
	Power:
		Amount: -20
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate, StealTechInfiltrate
	InfiltrateToCreateProxyActor:
		Proxy: powerproxy.airstrike
		Types: GrantSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.afld
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.afld
		OwnerType: Master
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	Encyclopedia:
		Category: Soviets/Buildings

POWR:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@SHAPE: ^2x2Shape
	Inherits@POWERDRAIN: ^PowerDrainable
	Inherits@OVERLOAD: ^HackableOverloadable
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 640,-384,0, 640,512,0, -710,-512,0, -710,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 10
		Prerequisites: ~structures.ra
		Description: Provides power for other structures.
	Valued:
		Cost: 300
	Tooltip:
		Name: Power Plant
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 40000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: 100
	ScalePowerWithHealth:
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	UpdatesBuildOrder:
		Limit: 2
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

APWR:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@SHAPE: ^3x2Shape
	Inherits@POWERDRAIN: ^PowerDrainable
	Inherits@OVERLOAD: ^HackableOverloadable
	HitShape:
		TargetableOffsets: -355,-1024,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 110
		Prerequisites: anyradar, ~structures.apwr, ~techlevel.medium
		Description: Provides double the power of a standard Power Plant.
	Valued:
		Cost: 500
	Tooltip:
		Name: Advanced Power Plant
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xxx Xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Selectable:
		Bounds: 3072, 2048
		DecorationBounds: 3072, 2901, 0, -426
	Health:
		HP: 70000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 5c0
	WithBuildingBib:
	Power:
		Amount: 200
	ScalePowerWithHealth:
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	UpdatesBuildOrder:
		Limit: 1
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

NPWR:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@SHAPE: ^3x2Shape
	Inherits@POWERDRAIN: ^PowerDrainable
	Inherits@OVERLOAD: ^HackableOverloadable
	Inherits@UPG: ^ProducesUpgrades
	Inherits@ATOMICAMMO: ^AtomicAmmoPower
	HitShape:
		TargetableOffsets: -355,-1024,0
		Type: Rectangle
			TopLeft: -1792, -1280
			BottomRight: 1792, 1024
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 165
		Prerequisites: stek, ~structures.soviet, ~techlevel.high
		Description: Provides ten times the power of a standard Power Plant.
	TooltipExtras:
		Weaknesses: • Explodes when destroyed
	Valued:
		Cost: 1000
	Tooltip:
		Name: Atomic Reactor
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xxxx Xxxx ====
		Dimensions: 4,3
		LocalCenterOffset: 0,-512,0
	Selectable:
		Bounds: 4096,3072
		DecorationBounds: 4096,3926,0,-427
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 5c0
	WithBuildingBib:
	Power:
		Amount: 1000
	ScalePowerWithHealth:
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	WithIdleOverlay@SMOKE1:
		Offset: 3400,-1400,0
		Image: smokestack
		Sequence: idle1
		Palette: effect-ignore-lighting-alpha50
		RequiresCondition: !(empdisable || being-warped || build-incomplete)
	WithIdleOverlay@SMOKE2:
		Offset: 2600,1300,0
		Image: smokestack
		Sequence: idle2
		Palette: effect-ignore-lighting-alpha50
		RequiresCondition: !(empdisable || being-warped || build-incomplete)
	WithIdleOverlay@SMOKE3:
		Offset: 4800,600,0
		Image: smokestack
		Sequence: idle3
		Palette: effect-ignore-lighting-alpha50
		RequiresCondition: !(empdisable || being-warped || build-incomplete)
	FireWarheadsOnDeath:
		Type: CenterPosition
		Weapon: CrateNuke
		EmptyWeapon: CrateNuke
	ProvidesPrerequisite@buildingname:
	ValidFactions:
		Factions: soviet, russia, ukraine, iraq, yuri
	ProvidesPrerequisiteValidatedFaction@iraq:
		Factions: iraq
		Prerequisite: npwr.iraq
	SupportPowerChargeBar:
	Encyclopedia:
		Category: Soviets/Buildings
		Scale: 1.3

TPWR:
	Inherits@AUTOTARGET: ^AutoTargetGround
	Inherits: APWR
	Buildable:
		Prerequisites: anyradar, ~techlevel.medium, ~structures.russia
		Description: Provides triple the power of a standard Power Plant.
	TooltipExtras:
		Strengths: • Power output unaffected by damage
		Weaknesses: • Unstable when damaged
	Valued:
		Cost: 600
	Tooltip:
		Name: Tesla Reactor
	WithIdleOverlay@POWER:
		Offset: -20,-70,0
		Sequence: power
		RequiresCondition: !(empdisable || being-warped || build-incomplete)
	GrantConditionOnDamageState@VOLATILE:
		Condition: volatile
		ValidDamageStates: Critical
	AttackOmni:
	PeriodicExplosion:
		Weapon: TPWRZap
		RequiresCondition: volatile
	SoundOnDamageTransition:
		DamagedSounds: bpowdiea.aud
		DestroyedSounds: bpowdieb.aud
	Power:
		Amount: 300
	-ScalePowerWithHealth:
	UpdatesBuildOrder:
		Limit: 1
	Encyclopedia:
		Category: Soviets/Buildings
	EncyclopediaExtras:
		Subfaction: russia

STEK:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@FORCESHIELDPOWER: ^ForceShieldPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer2: ^ProductionOptimizer2
	Selectable:
		Bounds: 3072, 2048
	HitShape:
		TargetableOffsets: 420,-768,0, 420,768,0, -770,-768,0, -770,768,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 150
		Prerequisites: vehicles.any, anyradar, ~structures.soviet, ~techlevel.high
		Description: Provides Soviet advanced technologies.
	Valued:
		Cost: 1800
	Tooltip:
		Name: Soviet Tech Center
	Building:
		Footprint: XxX XxX ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: -150
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite:
		Prerequisite: techcenter.any
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@TeslaCoilOrSovietTechCenter:
		Prerequisite: tslaorstek
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@ChemTowerOrSovietTechCenter:
		Prerequisite: tturorstek
		RequiresCondition: !tech-locked
	ValidFactions:
		Factions: soviet, russia, ukraine, iraq, yuri
	ProvidesPrerequisiteValidatedFaction@russia:
		Factions: russia
		Prerequisite: stek.russia
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@ukraine:
		Factions: ukraine
		Prerequisite: stek.ukraine
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@iraq:
		Factions: iraq
		Prerequisite: stek.iraq
		RequiresCondition: !tech-locked
	Encyclopedia:
		Category: Soviets/Buildings

BARR:
	Inherits: ^Building
	Inherits@SHAPE: ^2x2Shape
	Inherits@INF: ^ProducesInfantry
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 490,-470,0, 355,512,0, -355,-512,0, -630,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 30
		Prerequisites: anypower, ~structures.soviet
		Description: Trains infantry.
	Valued:
		Cost: 500
	Tooltip:
		Name: Soviet Barracks
	Building:
		Footprint: xx xx ++
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -170,810,0
		ExitCell: 1,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 0,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: -170,810,0
		ExitCell: -1,2
		Priority: 0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 2,2
		Priority: 0
	ValidFactions:
		Factions: soviet, russia, ukraine, iraq, yuri
	ProvidesPrerequisite@infantryany:
		Prerequisite: infantry.any
	ProvidesPrerequisite@human:
		Prerequisite: infantry.human
	ProvidesPrerequisite@infantryra:
		Prerequisite: infantry.ra
	ProvidesPrerequisiteValidatedFaction@russia:
		Factions: russia
		Prerequisite: infantry.russia
	ProvidesPrerequisiteValidatedFaction@ukraine:
		Factions: ukraine
		Prerequisite: infantry.ukraine
	ProvidesPrerequisiteValidatedFaction@iraq:
		Factions: iraq
		Prerequisite: infantry.iraq
	ProvidesPrerequisiteValidatedFaction@yuri:
		Factions: yuri
		Prerequisite: infantry.yuri
	ProvidesPrerequisiteValidatedFaction@e2:
		Factions: soviet, russia, iraq, yuri
		Prerequisite: infantry.e2
	ProvidesPrerequisite@dog:
		Prerequisite: infantry.dog
	ProvidesPrerequisiteValidatedFaction@boris:
		Factions: soviet, russia, iraq, ukraine
		Prerequisite: infantry.boris
	ProvidesPrerequisiteValidatedFaction@shok:
		Factions: soviet, russia, ukraine, yuri
		Prerequisite: infantry.shok
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	InfiltrateToCreateProxyActor@spy:
		Proxy: barracks.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.barr
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.barr
		OwnerType: Master
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	Encyclopedia:
		Category: Soviets/Buildings

KENN:
	Inherits: ^Building
	Inherits@DOG: ^ProducesDogs
	Selectable:
		Bounds: 1024, 1024
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 205
		Prerequisites: anypower, ~structures.soviet
		Description: Trains Attack Dogs.
	Valued:
		Cost: 150
	Tooltip:
		Name: Kennel
	-GivesBuildableArea:
	Health:
		HP: 30000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: True
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: 0,1
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -280,400,0
		ExitCell: -1,0
	ProvidesPrerequisite@dog:
		Prerequisite: infantry.dog
	-SpawnActorsOnSellCA:
	Power:
		Amount: -10
	ProvidesPrerequisite@buildingname:
	Encyclopedia:
		Category: Soviets/Buildings

TENT:
	Inherits: ^Building
	Inherits@SHAPE: ^2x2Shape
	Inherits@INF: ^ProducesInfantry
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-512,0, 355,512,0, -281,-512,0, -630,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 20
		Prerequisites: anypower, ~structures.allies
		Description: Trains infantry.
	Valued:
		Cost: 500
	Tooltip:
		Name: Allied Barracks
	Building:
		Footprint: xx xx ++
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -42,810,0
		ExitCell: 1,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 0,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: -42,810,0
		ExitCell: -1,2
		Priority: 0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 2,2
		Priority: 0
	ValidFactions:
		Factions: allies, england, germany, france, usa
	ProvidesPrerequisite@infantryany:
		Prerequisite: infantry.any
	ProvidesPrerequisite@human:
		Prerequisite: infantry.human
	ProvidesPrerequisite@infantryra:
		Prerequisite: infantry.ra
	ProvidesPrerequisiteValidatedFaction@england:
		Factions: england
		Prerequisite: infantry.england
	ProvidesPrerequisiteValidatedFaction@france:
		Factions: france
		Prerequisite: infantry.france
	ProvidesPrerequisiteValidatedFaction@germany:
		Factions: germany
		Prerequisite: infantry.germany
	ProvidesPrerequisiteValidatedFaction@usa:
		Factions: usa
		Prerequisite: infantry.usa
	ProvidesPrerequisite@mech:
		Prerequisite: infantry.mech
	ProvidesPrerequisite@medi:
		Prerequisite: infantry.medi
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	InfiltrateToCreateProxyActor@spy:
		Proxy: barracks.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.tent
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.tent
		OwnerType: Master
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	Encyclopedia:
		Category: Allies/Buildings

FIX:
	Inherits: ^Building
	Inherits@ProductionOptimizer1: ^ProductionOptimizer1
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 100
		Prerequisites: vehicles.any, ~structures.ra, ~techlevel.low
		Description: Repairs vehicles and aircraft.
	TooltipExtras:
		Attributes: • Nearby helipads/airfields will repair landed aircraft\n• Can retrofit existing units with upgrades
	Valued:
		Cost: 1000
	Tooltip:
		Name: Service Depot
	Building:
		Footprint: _+_ +++ _+_
		Dimensions: 3,3
	Selectable:
		Bounds: 2901, 1450, 0, 128
		DecorationBounds: 3072, 2048
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Reservable:
	RallyPoint:
	RepairsUnits:
		ValuePercentage: 0
		HpPerStep: 1000
		Interval: 7
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: !forceshield && !invulnerability && !being-warped
	ProximityExternalCondition@UNITSELL:
		Condition: unit-sellable
		Range: 1c0
	GrantConditionOnResupplying@Resupplying:
		Condition: resupplying
	Sellable:
		RequiresCondition: !resupplying && !build-incomplete && !c4 && !being-captured && !being-warped && !hacked
	WithBuildingBib:
		HasMinibib: true
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@repair:
		Prerequisite: repair
	ProvidesPrerequisite@radarorrepair:
		Prerequisite: radarorrepair
	ProvidesPrerequisite@allowsmcv:
		Prerequisite: vehicles.mcv
	HitShape:
		TargetableOffsets: 840,0,0, 598,-640,0, 598,640,0, -1060,0,0, -768,-640,0, -768,640,0
		Type: Polygon
			Points: -1536,-300, -640,-811, 640,-811, 1536,-300, 1536,555, 640,1110, -640,1110, -1536,555
	ProximityExternalCondition@AIRCRAFTREPAIR:
		Condition: aircraft-repair
		Range: 10c0
	WithRangeCircle@AIRCRAFTREPAIR:
		Type: AircraftRepair
		Color: FFD000AA
		Range: 10c0
	Encyclopedia:
		Category: Allies/Buildings; Soviets/Buildings

SBAG:
	Inherits: ^Wall
	RenderSprites:
		Palette: player
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 10
		Prerequisites: ~structures.sandbag
		Description: • Stops infantry and light vehicles\n• Can be crushed by tanks
	Valued:
		Cost: 30
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: Sandbag Wall
	Health:
		HP: 15000
	Armor:
		Type: Brick
	LineBuild:
		NodeTypes: sandbag
	LineBuildNode:
		Types: sandbag
	WithWallSpriteBody:
		Type: sandbag
	Encyclopedia:
		Category: Allies/Defenses

FENC:
	Inherits: ^Wall
	RenderSprites:
		Palette: player
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 20
		Prerequisites: ~structures.soviet
		Description: • Stops infantry and light vehicles\n• Can be crushed by tanks
	Valued:
		Cost: 30
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: Wire Fence
	Health:
		HP: 15000
	Armor:
		Type: Brick
	LineBuild:
		NodeTypes: fence
	LineBuildNode:
		Types: fence
	WithWallSpriteBody:
		Type: fence
	Encyclopedia:
		Category: Soviets/Defenses

CHAIN:
	Inherits: ^Wall
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 20
		Prerequisites: ~structures.nod
		Description: • Stops infantry and light vehicles\n• Can be crushed by tanks
	Valued:
		Cost: 30
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: Chain-link Fence
	Health:
		HP: 15000
	Armor:
		Type: Brick
	LineBuild:
		NodeTypes: chain
	LineBuildNode:
		Types: chain
	WithWallSpriteBody:
		Type: chain
	Encyclopedia:
		Category: Nod/Defenses

BRIK:
	Inherits: ^Wall
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 30
		Prerequisites: anypower, ~structures.wall
		Description: • Stops units\n• Blocks enemy fire
	Valued:
		Cost: 200
	CustomSellValue:
		Value: 0
	Tooltip:
		Name: Concrete Wall
	SoundOnDamageTransition:
		DamagedSounds: crmble2.aud
		DestroyedSounds: kaboom30.aud
	Health:
		HP: 40000
	Armor:
		Type: Brick
	Crushable:
		CrushClasses: heavywall
	LineBuild:
		NodeTypes: concrete
	LineBuildNode:
		Types: concrete
	WithWallSpriteBody:
		Type: concrete
	BlocksProjectiles:
	Encyclopedia:
		Category: Allies/Defenses; Soviets/Defenses; GDI/Defenses; Nod/Defenses

CYCL:
	Inherits: ^Wall
	Tooltip:
		Name: Chain-Link Barrier
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: chain
	LineBuildNode:
		Types: chain
	WithWallSpriteBody:
		Type: chain

BARB:
	Inherits: ^Wall
	Tooltip:
		Name: Barbed-Wire Fence
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: barbwire
	LineBuildNode:
		Types: barbwire
	WithWallSpriteBody:
		Type: barbwire

WOOD:
	Inherits: ^Wall
	Tooltip:
		Name: Wooden Fence
	Armor:
		Type: Wood
	LineBuild:
		NodeTypes: woodfence
	LineBuildNode:
		Types: woodfence
	WithWallSpriteBody:
		Type: woodfence

INFANTRY.ANY:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Infantry Production
	Buildable:
		Description: Infantry Production

INFANTRY.RA:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Infantry Production
	Buildable:
		Description: Infantry Production

INFANTRY.TD:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Infantry Production
	Buildable:
		Description: Infantry Production

VEHICLES.ANY:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Vehicle Production
	Buildable:
		Description: Vehicle Production

VEHICLES.TD:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Vehicle Production
	Buildable:
		Description: Vehicle Production

VEHICLES.NOD:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Vehicle Production
	Buildable:
		Description: Vehicle Production

AIRCRAFT.CHINOOK:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Helipad
	Buildable:
		Description: Helipad

TECHCENTER.ANY:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Tech Center
	Buildable:
		Description: Tech Center

TECHCENTER.TD:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Tech Center
	Buildable:
		Description: Tech Center

ANYPOWER:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Power Plant
	Buildable:
		Description: Power Plant

ANYREFINERY:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Refinery
	Buildable:
		Description: Refinery

ANYRADAR:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Radar
	Buildable:
		Description: Radar

REPAIR:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Service Depot/Repair Facility
	Buildable:
		Description: Service Depot/Repair Facility

RADARORREPAIR:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Radar or Service Depot/Repair Facility
	Buildable:
		Description: Radar or Service Depot/Repair Facility

RADARORAIRCRAFT:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Radar or Aircraft Production
	Buildable:
		Description: Radar or Aircraft Production

TSLAORSTEK:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Tesla Coil or Soviet Tech Center
	Buildable:
		Description: Tesla Coil or Soviet Tech Center

OBLIORTMPL:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Obelisk or Temple of Nod
	Buildable:
		Description: Obelisk or Temple of Nod

TTURORSTEK:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Chem Tower or Soviet Tech Center
	Buildable:
		Description: Chem Tower or Soviet Tech Center

FTURORRADAR:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Flame Tower or Radar
	Buildable:
		Description: Flame Tower or Radar

ATEKORALHQ:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Allied Tech Center or Allied HQ
	Buildable:
		Description: Allied Tech Center or Allied HQ

VEHICLES.MCV:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Service Depot/Repair Facility OR no MCV/Conyard
	Buildable:
		Description: Service Depot/Repair Facility OR no MCV/Conyard

INFANTRY.MEDI:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Barracks
	Buildable:
		Description: Barracks

INFANTRY.MECH:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Barracks
	Buildable:
		Description: Barracks

PYLE:
	Inherits: ^BuildingTD
	Inherits@SHAPE: ^2x2Shape
	Inherits@INF: ^ProducesInfantry
	Selectable:
		Bounds: 2048, 2048
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 840,-256,0, 840,512,0, 210,-512,0, -71,512,0
		Type: Rectangle
			TopLeft: -1024, -1024
			BottomRight: 1024, 640
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 20
		Prerequisites: anypower, ~structures.gdi
		IconPalette: chrometd
		Description: Trains infantry.
	Valued:
		Cost: 500
	Tooltip:
		Name: GDI Barracks
	Building:
		Footprint: xx xx ++
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -170,810,0
		ExitCell: 1,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 0,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: -170,810,0
		ExitCell: -1,2
		Priority: 0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: -725,640,0
		ExitCell: 2,2
		Priority: 0
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc
	ProvidesPrerequisite@infantryany:
		Prerequisite: infantry.any
	ProvidesPrerequisite@human:
		Prerequisite: infantry.human
	ProvidesPrerequisite@infantrytd:
		Prerequisite: infantry.td
	ProvidesPrerequisiteValidatedFaction@talon:
		Factions: talon
		Prerequisite: infantry.talon
	ProvidesPrerequisiteValidatedFaction@zocom:
		Factions: zocom
		Prerequisite: infantry.zocom
	ProvidesPrerequisiteValidatedFaction@eagle:
		Factions: eagle
		Prerequisite: infantry.eagle
	ProvidesPrerequisiteValidatedFaction@arc:
		Factions: arc
		Prerequisite: infantry.arc
	ProvidesPrerequisite@medi:
		Prerequisite: infantry.medi
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.pyle
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.pyle
		OwnerType: Master
	InfiltrateToCreateProxyActor@spy:
		Proxy: barracks.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Encyclopedia:
		Category: GDI/Buildings

HAND:
	Inherits: ^BuildingTD
	Inherits@SHAPE: ^2x2Shape
	Inherits@INF: ^ProducesInfantry
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-512,0, 355,512,0, -281,-512,0, -630,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 20
		Prerequisites: anypower, ~structures.nod
		IconPalette: chrometd
		Description: Trains infantry.
	Valued:
		Cost: 500
	Tooltip:
		Name: Hand of Nod
	Building:
		Footprint: xx xx ++
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 512,1024,0
		ExitCell: 1,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,768,0
		ExitCell: -2,2
		Facing: 384
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: 512,1024,0
		ExitCell: 0,2
		Priority: 0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: 512,1024,0
		ExitCell: 2,2
		Priority: 0
	Production@SQINF:
		Produces: InfantrySQ, Soldier, Cyborg, ParadropInfantry
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	ValidFactions:
		Factions: nod, blackh, marked, legion, shadow
	ProvidesPrerequisite@infantryany:
		Prerequisite: infantry.any
	ProvidesPrerequisite@human:
		Prerequisite: infantry.human
	ProvidesPrerequisite@infantrytd:
		Prerequisite: infantry.td
	ProvidesPrerequisite@nod:
		Prerequisite: infantry.nod
	ProvidesPrerequisiteValidatedFaction@blackh:
		Factions: blackh
		Prerequisite: infantry.blackh
	ProvidesPrerequisiteValidatedFaction@marked:
		Factions: marked
		Prerequisite: infantry.marked
	ProvidesPrerequisiteValidatedFaction@legion:
		Factions: legion
		Prerequisite: infantry.legion
	ProvidesPrerequisiteValidatedFaction@shadow:
		Factions: shadow
		Prerequisite: infantry.shadow
	ProvidesPrerequisite@mech:
		Prerequisite: infantry.mech
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.hand
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.hand
		OwnerType: Master
	Power:
		Amount: -20
	ProvidesPrerequisite@buildingname:
	InfiltrateToCreateProxyActor@spy:
		Proxy: barracks.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Encyclopedia:
		Category: Nod/Buildings

WEAP.TD:
	Inherits: ^BuildingTD
	Inherits@SHAPE: ^3x2Shape
	Inherits@NOMCV: ^UnlocksMcvIfNoneOwned
	Inherits@VEH: ^ProducesVehicles
	RenderSprites:
		Image: awep
	Selectable:
		Bounds: 3072, 3072
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 80
		Prerequisites: anyrefinery, ~structures.weap.td, ~structures.td, ~techlevel.low
		IconPalette: chrometd
		Description: Produces vehicles.
	Valued:
		Cost: 2000
	Tooltip:
		Name: Weapons Factory
	Building:
		Footprint: xxx xxx +++
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	TracksCapturedFaction:
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc, nod, legion
	ProvidesPrerequisite@any:
		Prerequisite: vehicles.any
	ProvidesPrerequisite@human:
		Prerequisite: vehicles.human
	ProvidesPrerequisite@td:
		Prerequisite: vehicles.td
	ProvidesPrerequisiteValidatedFaction@gdi:
		Factions: gdi, talon, zocom, eagle, arc
		Prerequisite: vehicles.gdi
	ProvidesPrerequisiteValidatedFaction@talon:
		Factions: talon
		Prerequisite: vehicles.talon
	ProvidesPrerequisiteValidatedFaction@zocom:
		Factions: zocom
		Prerequisite: vehicles.zocom
	ProvidesPrerequisiteValidatedFaction@eagle:
		Factions: eagle
		Prerequisite: vehicles.eagle
	ProvidesPrerequisiteValidatedFaction@arc:
		Factions: arc
		Prerequisite: vehicles.arc
	ProvidesPrerequisiteValidatedFaction@nod:
		Factions: nod, legion
		Prerequisite: vehicles.nod
	ProvidesPrerequisiteValidatedFaction@legion:
		Factions: legion
		Prerequisite: vehicles.legion
	ProvidesPrerequisiteValidatedFaction@hmmv:
		Factions: gdi, talon, zocom, eagle
		Prerequisite: vehicles.hmmv
	ProvidesPrerequisiteValidatedFaction@ftnk:
		Factions: legion
		Prerequisite: vehicles.ftnk
	ProvidesPrerequisite@apc2:
		Prerequisite: vehicles.apc2
	ProvidesPrerequisiteValidatedFaction@mtnk:
		Factions: gdi, talon, zocom, eagle, arc, legion
		Prerequisite: vehicles.mtnk
	ProvidesPrerequisiteValidatedFaction@msam:
		Factions: gdi, talon, zocom, arc
		Prerequisite: vehicles.msam
	ProvidesPrerequisiteValidatedFaction@htnk:
		Factions: gdi, zocom, eagle, arc
		Prerequisite: vehicles.htnk
	ProvidesPrerequisiteValidatedFaction@mlrs:
		Factions: legion
		Prerequisite: vehicles.mlrs
	WithBuildingBib:
	WithProductionDoorOverlayCA:
		RequiresCondition: !build-incomplete
		Sequence: build-top
	RallyPoint:
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -341,-341,0
		ExitCell: 0,2
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	InfiltrateToCreateProxyActor@spy:
		Proxy: vehicles.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.weap.td
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		UseTargetFaction: true
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.weap.td
		OwnerType: Master
	-ActorPreviewPlaceBuildingPreview:
	SequencePlaceBuildingPreview:
		Sequence: place
	GrantExternalConditionToProduced@DRONEEXIT:
		Condition: radarenabled
		Duration: 100
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

AIRS:
	Inherits: ^BuildingTD
	Inherits@NOMCV: ^UnlocksMcvIfNoneOwned
	Inherits@VEH: ^ProducesVehicles
	Inherits@NODAIRDROPPOWERS: ^NodAirdropPowers
	RenderSprites:
		Image: astrip
	HitShape:
		TargetableOffsets: 0,0,0, 0,-512,256, 0,-1451,384, 0,512,128, 0,1536,85
		Type: Rectangle
			TopLeft: -2048, -1024
			BottomRight: 2048, 1024
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 80
		Prerequisites: anyrefinery, ~structures.airs, ~techlevel.low
		IconPalette: chrometd
		Description: Provides a dropzone for vehicle reinforcements
	Valued:
		Cost: 2000
	Tooltip:
		Name: Airstrip
	Building:
		Footprint: XXX+ xxx+ ++++
		Dimensions: 4,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 200000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
	WithIdleOverlay@DISH:
		Sequence: idle-dish
		RequiresCondition: !build-incomplete
	RallyPoint:
	Exit@1:
		SpawnOffset: -1024,0,0
		ExitCell: 3,1
	-Production@SQVEH:
	-Production@MQVEH:
	ProductionAirdropCA@SQVEH:
		Produces: VehicleSQ, ParadropVehicle
		PauseOnCondition: forceshield || invulnerability || being-warped
		ActorType: c17.cargo
		ReadyAudio: ReinforcementsArrived
		SpawnType: ClosestEdgeToDestination
		ProportionalSpeed: true
		RequiresCondition: !global-multiqueue
	ProductionAirdropCA@MQVEH:
		Produces: VehicleMQ, ParadropVehicle
		PauseOnCondition: forceshield || invulnerability || being-warped
		ActorType: c17.cargo
		ReadyAudio: ReinforcementsArrived
		SpawnType: ClosestEdgeToDestination
		ProportionalSpeed: true
		RequiresCondition: global-multiqueue
	TracksCapturedFaction:
	ValidFactions:
		Factions: nod, blackh, marked, shadow
	ProvidesPrerequisite@any:
		Prerequisite: vehicles.any
	ProvidesPrerequisite@human:
		Prerequisite: vehicles.human
	ProvidesPrerequisite@td:
		Prerequisite: vehicles.td
	ProvidesPrerequisite@nod:
		Prerequisite: vehicles.nod
	ProvidesPrerequisite@ltnk:
		Prerequisite: vehicles.ltnk
	ProvidesPrerequisiteValidatedFaction@blackh:
		Factions: blackh
		Prerequisite: vehicles.blackh
	ProvidesPrerequisiteValidatedFaction@marked:
		Factions: marked
		Prerequisite: vehicles.marked
	ProvidesPrerequisiteValidatedFaction@shadow:
		Factions: shadow
		Prerequisite: vehicles.shadow
	ProvidesPrerequisiteValidatedFaction@ftnk:
		Factions: nod, marked, shadow
		Prerequisite: vehicles.ftnk
	ProvidesPrerequisiteValidatedFaction@mlrs:
		Factions: nod, blackh, marked
		Prerequisite: vehicles.mlrs
	WithDeliveryAnimation:
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: VetInfiltrate, StealTechInfiltrate
	Power:
		Amount: -30
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.airs
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		UseTargetFaction: true
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.airs
		OwnerType: Master
	ProvidesPrerequisite@buildingname:
	InfiltrateToCreateProxyActor@spy:
		Proxy: vehicles.upgraded
		Types: VetInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	ProductionCostMultiplier@FirstSQ:
		Multiplier: 90
		Prerequisites: !vehicles.any, global.singlequeue
	ProductionCostMultiplier@MQF:
		Multiplier: 90
		Prerequisites: global.multiqueuefull
	ProductionCostMultiplier@MQS1:
		Multiplier: 160
		Prerequisites: global.multiqueuescaled, vehicles.any, !optimized.production1, !optimized.production2
	ProductionCostMultiplier@MQS2:
		Multiplier: 125
		Prerequisites: global.multiqueuescaled, vehicles.any, optimized.production1, !optimized.production2
	ProductionCostMultiplier@MQS3:
		Multiplier: 90
		Prerequisites: global.multiqueuescaled, vehicles.any, optimized.production1, optimized.production2
	ProductionCostMultiplier@MQS4:
		Multiplier: 90
		Prerequisites: global.multiqueuescaled, !vehicles.any
	Encyclopedia:
		Category: Nod/Buildings
		Scale: 1.6

NUKE:
	Inherits: ^BuildingTD
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@SHAPE: ^2x2Shape
	Inherits@POWERDRAIN: ^PowerDrainable
	Inherits@OVERLOAD: ^HackableOverloadable
	HitShape:
		TargetableOffsets: 630,299,0
	RenderSprites:
		FactionImages:
			nod: nodnuke
			blackh: nodnuke
			marked: nodnuke
			legion: nodnuke
			shadow: nodnuke
	WithSpriteBody:
		PauseOnCondition: disabled
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 10
		Prerequisites: ~structures.td
		IconPalette: chrometd
		Description: Provides power for other structures.
	Valued:
		Cost: 300
	Tooltip:
		Name: Nuclear Power Plant
	Building:
		Footprint: xX xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 40000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: 100
	ScalePowerWithHealth:
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite:
		Prerequisite: anypower
	UpdatesBuildOrder:
		Limit: 2
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

NUK2:
	Inherits: ^BuildingTD
	Inherits@POWER_OUTAGE: ^DisabledByPowerOutage
	Inherits@SHAPE: ^2x2Shape
	Inherits@POWERDRAIN: ^PowerDrainable
	Inherits@OVERLOAD: ^HackableOverloadable
	HitShape:
		TargetableOffsets: 630,299,0
	RenderSprites:
		FactionImages:
			nod: nodnuk2
			blackh: nodnuk2
			marked: nodnuk2
			legion: nodnuk2
			shadow: nodnuk2
	WithSpriteBody:
		PauseOnCondition: disabled
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 110
		Prerequisites: anyradar, ~structures.td, ~techlevel.medium
		IconPalette: chrometd
		Description: Provides double the power of a standard Nuclear Power Plant.
	Valued:
		Cost: 500
	Tooltip:
		Name: Advanced Nuclear Power Plant
	ProvidesPrerequisite:
		Prerequisite: anypower
	Building:
		Footprint: xX xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 70000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 5c0
	WithBuildingBib:
	Power:
		Amount: 200
	ProvidesPrerequisite@buildingname:
	ScalePowerWithHealth:
	UpdatesBuildOrder:
		Limit: 1
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

AFAC:
	Inherits: ^BuildingTD
	Inherits@SHAPE: ^3x2Shape
	Inherits@BOTI: ^BotFallbackInsurance
	Inherits@BLD: ^ProducesBuildings
	RenderSprites:
		FactionImages:
			nod: nodfact
			blackh: nodfact
			marked: nodfact
			legion: nodfact
			shadow: nodfact
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 1000
		Prerequisites: ~disabled
		IconPalette: chrometd
		Description: Produces structures.
	Health:
		HP: 150000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Valued:
		Cost: 3000
	Tooltip:
		Name: Construction Yard
	BaseBuilding:
	Transforms:
		RequiresCondition: factundeploy
		PauseOnCondition: chrono-vortex || being-captured || c4 || tnt || build-incomplete || being-warped || hacked
		IntoActor: amcv
		Offset: 1,1
		Facing: 432
	TransformsIntoMobile:
		RequiresCondition: factundeploy
		Locomotor: heavywheeled
		RequiresForceMove: true
	TransformsIntoPassenger:
		RequiresCondition: factundeploy
		CargoType: Vehicle
		RequiresForceMove: true
	TransformsIntoRepairable:
		RequiresCondition: factundeploy
		RepairActors: fix, rep
		RequiresForceMove: true
	TransformsIntoTransforms:
		RequiresCondition: factundeploy && build-incomplete
	Sellable:
		RequiresCondition: !build-incomplete && !chrono-vortex && !being-captured && !c4 && !being-warped && !hacked
	GrantConditionOnPrerequisite@GLOBALFACTUNDEPLOY:
		Condition: factundeploy
		Prerequisites: global-factundeploy
	TracksCapturedFaction:
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc, nod, blackh, marked, legion, shadow
	ProvidesPrerequisite@td:
		Prerequisite: structures.td
	ProvidesPrerequisiteValidatedFaction@gdi:
		Factions: gdi, talon, zocom, eagle, arc
		Prerequisite: structures.gdi
	ProvidesPrerequisiteValidatedFaction@talon:
		Factions: talon
		Prerequisite: structures.talon
	ProvidesPrerequisiteValidatedFaction@zocom:
		Factions: zocom
		Prerequisite: structures.zocom
	ProvidesPrerequisiteValidatedFaction@eagle:
		Factions: eagle
		Prerequisite: structures.eagle
	ProvidesPrerequisiteValidatedFaction@arc:
		Factions: arc
		Prerequisite: structures.arc
	ProvidesPrerequisiteValidatedFaction@nod:
		Factions: nod, blackh, marked, legion, shadow
		Prerequisite: structures.nod
	ProvidesPrerequisiteValidatedFaction@blackh:
		Factions: blackh
		Prerequisite: structures.blackh
	ProvidesPrerequisiteValidatedFaction@marked:
		Factions: marked
		Prerequisite: structures.marked
	ProvidesPrerequisiteValidatedFaction@legion:
		Factions: legion
		Prerequisite: structures.legion
	ProvidesPrerequisiteValidatedFaction@shadow:
		Factions: shadow
		Prerequisite: structures.shadow
	<EMAIL>:
		Factions: gdi, talon, zocom, eagle, arc, legion
		Prerequisite: structures.weap.td
	ProvidesPrerequisiteValidatedFaction@airs:
		Factions: nod, blackh, marked, shadow
		Prerequisite: structures.airs
	ProvidesPrerequisite@wall:
		Prerequisite: structures.wall
	ProvidesPrerequisiteValidatedFaction@sandbag:
		Factions: gdi, talon, zocom, eagle, arc
		Prerequisite: structures.sandbag
	ProvidesPrerequisiteValidatedFaction@atwr:
		Factions: gdi, talon, eagle, arc
		Prerequisite: structures.atwr
	ProvidesPrerequisite@anyconyard:
		Prerequisite: anyconyard
	BaseProvider:
		Range: 16c0
		PauseOnCondition: being-captured
		RequiresCondition: !economy-policy2
	BaseProvider@EconomyPolicy2:
		Range: 20c0
		PauseOnCondition: being-captured
		RequiresCondition: economy-policy2
	GrantConditionOnPrerequisite@EconomyPolicy2:
		Condition: economy-policy2
		Prerequisites: economy.policy, influence.level2
	WithBuildingPlacedAnimation:
		RequiresCondition: !build-incomplete && !chrono-vortex
	Power:
		Amount: 0
	ProvidesPrerequisite@buildingname:
	ConyardChronoReturn:
		ReturnOriginalActorOnCondition: build-incomplete
		Condition: chrono-vortex
		Damage: 950
	TransferTimedExternalConditionOnTransform:
		Condition: invulnerability
	TransferTimedExternalConditionOnTransform@INVIS:
		Condition: invisibility
	SpawnActorsOnSellCA:
		ActorTypes: n1,n1,n1,n1,n6
	-UpdatesBuildOrder:
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

NSAM:
	Inherits: ^DefenseTD
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetAirICBM
	Inherits@SHAPE: ^2x1Shape
	Inherits@A2GPROTECTION: ^AirToGroundProtection
	Inherits@AntiAirDefense: ^AntiAirDefense
	Selectable:
		Bounds: 2048, 1024
	HitShape:
		Type: Rectangle
			TopLeft: -768,-512
			BottomRight: 768,512
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 100
		Prerequisites: anyradar, ~structures.nod, ~techlevel.medium
		Description: Anti-aircraft base defense.
	TooltipExtras:
		Strengths: • Strong vs Aircraft
		Weaknesses: • Cannot attack ground units
		Attributes: • Requires power to operate\n• Can detect cloaked aircraft
	Valued:
		Cost: 750
	Tooltip:
		Name: SAM Site
	Building:
		Footprint: xx
		Dimensions: 2,1
	Health:
		HP: 50000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 120
		InitialFacing: 0
		RealignDelay: -1
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	-WithSpriteBody:
	WithEmbeddedTurretSpriteBody:
		QuantizedFacings: 32
		PauseOnCondition: disabled || empdisable || being-warped
	AttackPopupTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	Armament:
		Weapon: Nike
		MuzzleSequence: muzzle
	WithMuzzleOverlay:
	Power:
		Amount: -40
	ClassicFacingBodyOrientation:
	-ActorPreviewPlaceBuildingPreview:
	SequencePlaceBuildingPreview:
		Sequence: place
	DetectCloaked:
		Range: 7c0
		DetectionTypes: AirCloak
		RequiresCondition: !(disabled || empdisable || being-warped)
	RenderRangeCircle:
		RangeCircleType: DefenseRangeAA
	Encyclopedia:
		Category: Nod/Defenses

GTWR:
	Inherits: ^DefenseTD
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 50
		Prerequisites: infantry.any, ~structures.gdi, ~techlevel.low
		IconPalette: chrometd
		Description: Anti-infantry base defense.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		Attributes: • Can detect cloaked units
	Valued:
		Cost: 600
	CustomSellValue:
		Value: 200
	Tooltip:
		Name: Guard Tower
	Building:
	Health:
		HP: 40000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Armament:
		Weapon: gtchaingun
		LocalOffset: 341,0,128
		MuzzleSequence: muzzle
	AttackTurreted:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	ClassicFacingBodyOrientation:
		QuantizedFacings: 8
	WithMuzzleOverlay:
	Turreted:
		TurnSpeed: 512
	Power:
		Amount: -15
	DetectCloaked:
		Range: 5c0
	Encyclopedia:
		Category: GDI/Defenses

ATWR:
	Inherits: ^DefenseTD
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 80
		Prerequisites: vehicles.any, ~structures.atwr, ~techlevel.medium
		IconPalette: chrometd
		Description: Advanced base defense.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Requires power to operate\n• Can detect cloaked units
	Valued:
		Cost: 1100
	Tooltip:
		Name: Advanced Guard Tower
	Health:
		HP: 50000
	Armor:
		Type: Concrete
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 938, 2048, 0, -512
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 512
		Offset: 128,128,384
	Armament@primary:
		Weapon: StingerGTWR
		LocalOffset: 256,128,0, 256,-128,0
		LocalYaw: -100,100
	AttackTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	ClassicFacingBodyOrientation:
		QuantizedFacings: 8
	Power:
		Amount: -70
	DetectCloaked:
		RequiresCondition: !(disabled || empdisable || being-warped)
	Encyclopedia:
		Category: GDI/Defenses

STWR:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 80
		Prerequisites: vehicles.any, ~structures.zocom, ~techlevel.medium
		Description: Advanced base defense that emits devastating sonic shockwaves.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Requires power to operate\n• Can detect cloaked units
	Valued:
		Cost: 1450
	Tooltip:
		Name: Sonic Tower
	Building:
	Health:
		HP: 47500
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 20
		InitialFacing: 192
		RealignDelay: -1
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
	Armament:
		Weapon: SonicPulse
		RequiresCondition: !sonic-upgrade
	Armament@Upgrade:
		Weapon: SonicPulse.UPG
		RequiresCondition: sonic-upgrade
	AttackTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	WithMuzzleOverlay:
	Power:
		Amount: -85
	ClassicFacingBodyOrientation:
	DetectCloaked:
		RequiresCondition: !(disabled || empdisable || being-warped)
	Selectable:
		DecorationBounds: 1024, 1706, 0, -341
	GrantConditionOnPrerequisite@SONIC:
		Condition: sonic-upgrade
		Prerequisites: sonic.upgrade
	Encyclopedia:
		Category: GDI/Defenses
	EncyclopediaExtras:
		Subfaction: zocom

OBLI:
	Inherits: ^DefenseTD
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 80
		Prerequisites: anyradar, ~structures.nod, ~techlevel.medium
		IconPalette: chrometd
		Description: Advanced experimental base defense.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Requires power to operate\n• Can detect cloaked units
	Valued:
		Cost: 1500
	Tooltip:
		Name: Obelisk of Light
	Selectable:
		DecorationBounds: 938, 1792, 0, -341
	Health:
		HP: 64000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	GrantConditionOnPrerequisite@QUANTUM:
		Condition: quantum-upgrade
		Prerequisites: quantum.upgrade
	-WithSpriteBody:
	WithChargeSpriteBody:
		Sequence: active
	Armament@PRIMARY:
		Weapon: Laser
		LocalOffset: 0,-85,1280
		RequiresCondition: !quantum-upgrade
		MuzzleSequence: muzzle
		MuzzlePalette: caneon
	Armament@QUANTUM:
		Weapon: Laser.Adv
		LocalOffset: 0,-85,1280
		RequiresCondition: quantum-upgrade
		MuzzleSequence: muzzle2
		MuzzlePalette: caneon
	WithMuzzleOverlay:
	AttackCharges:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
		ChargeLevel: 50
		ChargingCondition: charging
	AmbientSound@1:
		RequiresCondition: charging && !quantum-upgrade
		SoundFiles: obelpowr.aud
		Interval: 20
	AmbientSound@2:
		RequiresCondition: charging && quantum-upgrade
		SoundFiles: obelpowr2.aud
		Interval: 20
	Power:
		Amount: -90
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@ObeliskOrTemple:
		Prerequisite: obliortmpl
	DetectCloaked:
		RequiresCondition: !(disabled || empdisable || being-warped)
	Encyclopedia:
		Category: Nod/Defenses

HQ:
	Inherits: ^BuildingTD
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@UPG: ^ProducesUpgrades
	Inherits@GDIRADARPOWERS: ^GDIRadarPowers
	Inherits@SATHACKPOWER: ^SatHackPower
	Inherits@SHADOWTEAMPOWER: ^ShadowTeamPower
	Inherits@@INFERNOBOMBPOWER: ^InfernoBombPower
	Inherits@CASHHACKPOWER: ^CashHackPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer1: ^ProductionOptimizer1
	Selectable:
		Bounds: 2048, 2304
	HitShape:
		TargetableOffsets: 0,0,0, 0,512,0, 420,-598,256
		Type: Rectangle
			TopLeft: -1024, -384
			BottomRight: 1024, 1024
	RenderSprites:
		FactionImages:
			nod: nodhq
			blackh: nodhq
			marked: nodhq
			legion: nodhq
			shadow: nodhq
			arc: hqr
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 90
		Prerequisites: anyrefinery, ~structures.td, ~techlevel.medium, ~!unity.covenant
		IconPalette: chrometd
		Description: Provides an overview of the battlefield.
	TooltipExtras:
		Attributes: • Requires power to operate\n• Detects nearby enemy vehicles, aircraft and structures in fog of war
		RequiresCondition: !arc-built
	TooltipExtras@ARC:
		Attributes: • Requires power to operate\n• Detects nearby enemy vehicles, aircraft and structures in fog of war\n• Has limited emergency backup power
		RequiresCondition: arc-built
		IsStandard: false
	Valued:
		Cost: 1800
	Tooltip:
		Name: Comm. Center
		RequiresCondition: !arc-built
	Tooltip@ARC:
		Name: Drone Comm. Center
		RequiresCondition: arc-built
	Building:
		Footprint: X_ xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	WithSpriteBody:
		PauseOnCondition: (disabled && !backup-power) || being-warped
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetShroudInfiltrate
	Targetable@HACKCAPTURABLE:
		RequiresCondition: !being-warped
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	ProvidesRadar:
		RequiresCondition: !jammed && (!disabled || backup-power) && !being-warped && !build-incomplete
	RangedGpsRadarProvider:
		Range: 12c0
		TargetTypes: Vehicle, Air, Structure
		RequiresCondition: !jammed && (!disabled || backup-power) && !being-warped && !build-incomplete
	WithRangeCircle:
		Type: RadarDetection
		Range: 12c0
		RequiresCondition: !jammed && (!disabled || backup-power) && !being-warped && !build-incomplete
	WithBuildingBib:
	SupportPowerChargeBar:
	InfiltrateForExploration:
		Types: ResetShroudInfiltrate
		PlayerExperience: 15
	Power:
		Amount: -40
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc, nod, blackh, marked, legion, shadow
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@anyradar:
		Prerequisite: anyradar
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@radarorrepair:
		Prerequisite: radarorrepair
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@FlameTowerOrRadar:
		Prerequisite: fturorradar
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@gdirad:
		Factions: gdi, talon, zocom, eagle, arc
		Prerequisite: radar.gdi
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@talrad:
		Factions: talon
		Prerequisite: radar.talon
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@zocrad:
		Factions: zocom
		Prerequisite: radar.zocom
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@eagrad:
		Factions: eagle
		Prerequisite: radar.eagle
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@arcrad:
		Factions: arc
		Prerequisite: radar.arc
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@nodrad:
		Factions: nod, blackh, marked, legion, shadow
		Prerequisite: radar.nod
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@bhrad:
		Factions: blackh
		Prerequisite: radar.blackh
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@legrad:
		Factions: legion
		Prerequisite: radar.legion
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@shadrad:
		Factions: shadow
		Prerequisite: radar.shadow
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active
		RequiresCondition: !jammed && (!disabled || backup-power) && !being-warped
	ExternalCondition@JAMMED:
		Condition: jammed
	ProductionCostMultiplier@ARCDISCOUNT:
		Multiplier: 50
		Prerequisites: anyradar, player.arc
	GrantConditionOnFaction@ARC:
		Factions: arc
		Condition: arc-built
	GrantConditionOnPrerequisite@UPG:
		Prerequisites: unity.covenant
		Condition: unity-upgrade
	TransformOnCondition@UPG:
		IntoActor: hq.upg
		SkipMakeAnims: false
		RequiresCondition: unity-upgrade && !build-incomplete
	GrantChargingCondition@ArcBackupPower:
		Condition: backup-power
		RequiresCondition: arc-built
		PauseOnCondition: disabled
		InitialCharge: 0
		MaxCharge: 3000
		ChargeRate: 1
		DischargeRate: 8
		ChargingColor: aaaa00
		DischargingColor: ffff00
		ShowSelectionBarWhenFull: false
		ShowSelectionBarWhenEmpty: false
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

HQ.UPG:
	Inherits: HQ
	RenderSprites:
		Image: nodhq.upg
		FactionImages:
			nod: nodhq.upg
			blackh: nodhq.upg
			marked: nodhq.upg
			legion: nodhq.upg
			shadow: nodhq.upg
			arc: nodhq.upg
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 90
		Prerequisites: anyrefinery, ~structures.td, ~techlevel.medium, ~unity.covenant
		IconPalette: chrometd
		Description: Provides an overview of the battlefield.
	Tooltip:
		Name: Internet Center
	TooltipExtras:
		Attributes: • Requires power to operate\n• Detects nearby enemy vehicles, aircraft and structures in fog of war\n• $25 delivered every 60 seconds (normal speed)\n• Income increased by $75 per garrisoned Hacker
	RenderSprites:
		PlayerPalette: playertd
	-Targetable@HACKCAPTURABLE:
	-TransformOnCondition@UPG:
	-GrantConditionOnPrerequisite@UPG:
	Cargo:
		Types: Hacker
		MaxWeight: 5
		LoadedCondition: cargo
	WithCargoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	CashTrickler@Hacker0:
		Interval: 375
		Amount: 25
		RequiresCondition: cargo == 0
	CashTrickler@Hacker1:
		Interval: 375
		Amount: 100
		RequiresCondition: cargo == 1
	CashTrickler@Hacker2:
		Interval: 375
		Amount: 175
		RequiresCondition: cargo == 2
	CashTrickler@Hacker3:
		Interval: 375
		Amount: 250
		RequiresCondition: cargo == 3
	CashTrickler@Hacker4:
		Interval: 375
		Amount: 325
		RequiresCondition: cargo == 4
	CashTrickler@Hacker5:
		Interval: 375
		Amount: 400
		RequiresCondition: cargo == 5
	Encyclopedia:
		Category: Nod/Buildings

EYE:
	Inherits: ^BuildingTD
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@IONCANNONPOWER: ^IonCannonPower
	Inherits@SURGICALSTRIKEPOWER: ^SurgicalStrikePower
	Selectable:
		Bounds: 2048, 2304
	HitShape:
		TargetableOffsets: 0,0,0, 0,512,128, 420,-598,213
		Type: Rectangle
			TopLeft: -1024, -1024
			BottomRight: 1024, 1024
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 140
		Prerequisites: gtek, ~structures.gdi, ~techlevel.unrestricted
		IconPalette: chrometd
		BuildLimit: 1
		Description: Provides radar and Orbital Ion Cannon support power.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Ion Cannon\n• Detects nearby enemy vehicles, aircraft and structures in fog of war
	Valued:
		Cost: 2500
	Tooltip:
		Name: Advanced Communications Center
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetShroudInfiltrate, ResetSupportPowerInfiltrate
	WithSpriteBody:
		PauseOnCondition: disabled || being-warped
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
	ProvidesRadar:
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	RangedGpsRadarProvider:
		Range: 12c0
		TargetTypes: Vehicle, Air, Structure
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	WithRangeCircle:
		Type: RadarDetection
		Range: 12c0
		RequiresCondition: !jammed && !disabled && !being-warped && !build-incomplete
	InfiltrateForExploration:
		Types: ResetShroudInfiltrate
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	SupportPowerChargeBar:
	Power:
		Amount: -200
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc
	ProvidesPrerequisiteValidatedFaction@zoceye:
		Factions: zocom
		Prerequisite: eye.zocom
	ProvidesPrerequisite@buildingname:
	ExternalCondition@JAMMED:
		Condition: jammed
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active
		RequiresCondition: !jammed && !disabled && !being-warped
	MustBeDestroyed:
		RequiredForShortGame: false
	ProductionCostMultiplier@ZocomBonus:
		Multiplier: 60
		Prerequisites: player.zocom
	Encyclopedia:
		Category: GDI/Buildings

REP:
	Inherits: ^BuildingTD
	Inherits@ProductionOptimizer1: ^ProductionOptimizer1
	HitShape:
		TargetableOffsets: 840,0,0, 598,-640,0, 598,640,0, -1060,0,0, -768,-640,0, -768,640,0
		Type: Polygon
			Points: -1536,-300, -640,-811, 640,-811, 1536,-300, 1536,555, 640,1110, -640,1110, -1536,555
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 100
		Prerequisites: vehicles.any, ~structures.td, ~techlevel.low
		IconPalette: chrometd
		Description: Repairs vehicles and aircraft.
	TooltipExtras:
		Attributes: • Nearby helipads/airfields will repair landed aircraft\n• Can retrofit existing units with upgrades
	Valued:
		Cost: 1000
	Tooltip:
		Name: Repair Facility
	Building:
		Footprint: _+_ +++ _+_
		Dimensions: 3,3
	Selectable:
		Bounds: 2901, 1450, 0, 128
		DecorationBounds: 3072, 2048
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: true
	Reservable:
	RallyPoint:
	RepairsUnits:
		ValuePercentage: 0
		HpPerStep: 1000
		Interval: 7
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: !forceshield && !invulnerability && !being-warped
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	Power:
		Amount: -30
	ProximityExternalCondition@UNITSELL:
		Condition: unit-sellable
		Range: 1c0
	GrantConditionOnResupplying@Resupplying:
		Condition: resupplying
	Sellable:
		RequiresCondition: !resupplying && !build-incomplete && !c4 && !being-captured && !being-warped && !hacked
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@repair:
		Prerequisite: repair
	ProvidesPrerequisite@radarorrepair:
		Prerequisite: radarorrepair
	ProvidesPrerequisite@allowsmcv:
		Prerequisite: vehicles.mcv
	ProximityExternalCondition@AIRCRAFTREPAIR:
		Condition: aircraft-repair
		Range: 10c0
	WithRangeCircle@AIRCRAFTREPAIR:
		Type: AircraftRepair
		Color: FFD000AA
		Range: 10c0
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

HPAD.TD:
	Inherits: ^BuildingTD
	Inherits@SHAPE: ^2x2Shape
	Inherits@AIRCRAFTREPAIR: ^RepairsAircraftWithRepairBay
	Inherits@INTERCEPTORS: ^InterceptorsPower
	Inherits@AIR: ^ProducesHelicopters
	RenderSprites:
		Image: hpad2
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 768,-512,0, 768,512,0, -281,-512,0, -630,512,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 120
		Prerequisites: radarorrepair, ~structures.nod, ~techlevel.medium
		IconPalette: chrometd
		Description: Produces and reloads helicopters and VTOL aircraft.
	Valued:
		Cost: 500
	Tooltip:
		Name: Helipad
	Building:
		Footprint: xx xx ==
		Dimensions: 2,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 80000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		SpawnOffset: 0,-256,0
		Facing: 896
	RallyPoint:
	Reservable:
	WithBuildingBib:
	Power:
		Amount: -20
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc, nod, blackh, marked, legion, shadow
	ProvidesPrerequisiteValidatedFaction@gdi:
		Factions: gdi, talon, zocom, eagle, arc
		Prerequisite: aircraft.gdi
	ProvidesPrerequisiteValidatedFaction@talon:
		Factions: talon
		Prerequisite: aircraft.talon
	ProvidesPrerequisiteValidatedFaction@eagle:
		Factions: eagle
		Prerequisite: aircraft.eagle
	ProvidesPrerequisiteValidatedFaction@arc:
		Factions: arc
		Prerequisite: aircraft.arc
	ProvidesPrerequisiteValidatedFaction@nod:
		Factions: nod, blackh, marked, legion, shadow
		Prerequisite: aircraft.nod
	ProvidesPrerequisiteValidatedFaction@marked:
		Factions: marked
		Prerequisite: aircraft.marked
	ProvidesPrerequisiteValidatedFaction@shadow:
		Factions: shadow
		Prerequisite: aircraft.shadow
	ProvidesPrerequisiteValidatedFaction@apch:
		Factions: nod, blackh, legion, shadow
		Prerequisite: aircraft.apch
	ProvidesPrerequisite@chinookvisible:
		Prerequisite: aircraft.chinookvisible
	ProvidesPrerequisite@chinook:
		Prerequisite: aircraft.chinook
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate, StealTechInfiltrate
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	InfiltrateToCreateProxyActor:
		Types: GrantSupportPowerInfiltrate
		Proxy: powerproxy.paratroopers.allies
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.hpad.td
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		UseTargetFaction: true
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.hpad.td
		OwnerType: Master
	Encyclopedia:
		Category: Nod/Buildings

PROC.TD:
	Inherits: ^BuildingTD
	Inherits@BOTREF: ^BotRefinery
	Inherits@ResourceDrainable: ^ResourceDrainable
	Inherits@EconomyPolicyDiscount: ^EconomyPolicyDiscount
	Inherits@EconomyPolicyTimeReduction: ^EconomyPolicyTimeReduction
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -512
			BottomRight: 1536, 853
	HitShape@TOP:
		Type: Rectangle
			TopLeft: -512, -1450
			BottomRight: 896, -512
	RenderSprites:
		Image: proc2
		FactionImages:
			nod: nodproc
			blackh: nodproc
			marked: nodproc
			legion: nodproc
			shadow: nodproc
	Valued:
		Cost: 1800
	CustomSellValue:
		Value: 400
	Tooltip:
		Name: Refinery
	Buildable:
		BuildPaletteOrder: 60
		Prerequisites: anypower, ~structures.td
		Queue: BuildingSQ, BuildingMQ
		IconPalette: chrometd
		Description: Processes raw Tiberium, Ore and Gems into credits.
		BuildDuration: 1400
	Building:
		Footprint: _x_ xxx +++ ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 90000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: StealCreditsInfiltrate
	Refinery:
		TickRate: 15
	DockHost:
		Type: Unload
		DockAngle: 448
		DockOffset: -1c0, 1c0, 0
		IsDragRequired: True
		DragOffset: -554,512,0
		DragLength: 12
	StoresPlayerResourcesCA:
		Capacity: 2000
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 17
	Selectable:
		Bounds: 3072, 2512, 0, 256
		DecorationBounds: 3114, 3072
	InfiltrateForCash:
		Types: StealCreditsInfiltrate
		Percentage: 50
		InfiltrationNotification: CreditsStolen
		PlayerExperience: 15
	GrantConditionOnPrerequisite@SHARVUPG:
		Prerequisites: sharv.upgrade
		Condition: sharv-upgraded
	GrantConditionOnPrerequisite@NOSHARVUPG:
		Prerequisites: !sharv.upgrade
		Condition: sharv-notupgraded
	GrantCondition@SPAWNHARV:
		Condition: spawn-harv
		GrantPermanently: true
		RequiresCondition: sharv-notupgraded
	GrantCondition@SPAWNSHARV:
		Condition: spawn-sharv
		GrantPermanently: true
		RequiresCondition: sharv-upgraded
	FreeActor:
		Actor: HARV.TD
		SpawnOffset: 1,2
		Facing: 256
		RequiresCondition: spawn-harv && !spawn-sharv && !build-incomplete
	FreeActor@SHARV:
		Actor: HARV.TD.UPG
		SpawnOffset: 1,2
		Facing: 256
		RequiresCondition: spawn-sharv && !spawn-harv && !build-incomplete
	WithResourceLevelOverlay:
		RequiresCondition: !build-incomplete
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@anyrefinery:
		Prerequisite: anyrefinery
	CashHackable:
	UpdatesCount:
		Type: Refineries
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

SILO.TD:
	Inherits: ^BuildingTD
	Inherits@SHAPE: ^2x1Shape
	RenderSprites:
		Image: silo2
	Valued:
		Cost: 300
	Tooltip:
		Name: Silo
	Buildable:
		BuildPaletteOrder: 35
		Prerequisites: anyrefinery, ~structures.td
		Queue: DefenseSQ, DefenseMQ
		IconPalette: chrometd
		Description: Stores processed Tiberium, Ore and Gems
	Building:
		Footprint: xx
		Dimensions: 2,1
	-GivesBuildableArea:
	Health:
		HP: 60000
	Armor:
		Type: Wood
	RevealsShroud:
		Range: 4c0
	WithBuildingBib:
		HasMinibib: true
	-WithSpriteBody:
	WithResourceLevelSpriteBody:
		Sequence: stages
	StoresPlayerResourcesCA:
		Capacity: 6000
	WithResourceStoragePipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 5
	-MustBeDestroyed:
	SpawnActorsOnSellCA:
		ActorTypes: c1,c7
		-GuaranteedActorTypes:
	Power:
		Amount: -10
	Selectable:
		DecorationBounds: 2090, 1280
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	-UpdatesBuildOrder:
	Encyclopedia:
		Category: GDI/Buildings; Nod/Buildings

AFLD.GDI:
	Inherits: ^Building
	Inherits@SHAPE: ^3x2Shape
	Inherits@AIRCRAFTREPAIR: ^RepairsAircraftWithRepairBay
	Inherits@INTERCEPTORS: ^InterceptorsPower
	Inherits@AIR: ^ProducesAircraft
	RenderSprites:
		Image: afldgdi
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 420,0,0, 420,-1024,0, 420,1024,0, -777,0,0, -777,-1024,0, -777,1024,0
	Selectable:
		Bounds: 3072, 2176, 0, -256
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 130
		Prerequisites: radarorrepair, ~structures.gdi, ~techlevel.medium
		Description: Produces and reloads aircraft.
	Valued:
		Cost: 500
	Tooltip:
		Name: Airfield
	Building:
		Footprint: xxx xxx
		Dimensions: 3,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		ExitCell: 1,1
		Facing: 768
	RallyPoint:
	Reservable:
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc
	ProvidesPrerequisite@gdi:
		Prerequisite: aircraft.gdi
	ProvidesPrerequisiteValidatedFaction@eagle:
		Factions: eagle
		Prerequisite: aircraft.eagle
	ProvidesPrerequisiteValidatedFaction@arc:
		Factions: arc
		Prerequisite: aircraft.arc
	ProvidesPrerequisite@chinook:
		Prerequisite: aircraft.chinook
	ProvidesPrerequisite@chinookvisible:
		Prerequisite: aircraft.chinookvisible
	ProvidesPrerequisite@RadarOrAircraft:
		Prerequisite: radaroraircraft
	ProvidesPrerequisite@buildingname:
	SupportPowerChargeBar:
	Power:
		Amount: -20
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate, StealTechInfiltrate
	WithResupplyAnimation:
		RequiresCondition: !build-incomplete
	InfiltrateToCreateProxyActor:
		Types: GrantSupportPowerInfiltrate
		Proxy: powerproxy.airstrike
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	InfiltrateToCreateProxyActor@STOLENTECH:
		Types: StealTechInfiltrate
		Proxy: stolentech.afld.gdi
		InfiltrationNotification: TechnologyAcquired
		InfiltratedNotification: OurTechnologyStolen
		PlayerExperience: 15
	SpawnActorOnMindControlled@STOLENTECH:
		Actor: stolentech.afld.gdi
		OwnerType: Master
	SpawnActorOnDeath:
		Actor: n1
	SpawnActorsOnSellCA:
		ActorTypes: n1,n1,n1,n1,n1,n1,n1,n1,n1,n1,c1,c1,c1,c1,c7,c7,c7,c7,c10,c10
		GuaranteedActorTypes: n1, n1
	Encyclopedia:
		Category: GDI/Buildings

GTEK:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@FORCESHIELDPOWER: ^ForceShieldPower
	Inherits@NANITEREPAIRPOWER: ^NaniteRepairPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer2: ^ProductionOptimizer2
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 150
		IconPalette: chrometd
		Prerequisites: vehicles.any, anyradar, ~structures.gdi, ~techlevel.high
		Description: Provides GDI advanced technologies.
	Valued:
		Cost: 1800
	Tooltip:
		Name: GDI Tech Center
	Building:
		Footprint: xxx =xx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
	Power:
		Amount: -150
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite:
		Prerequisite: techcenter.any
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@tdtek:
		Prerequisite: techcenter.td
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@talongtek:
		Factions: talon
		Prerequisite: gtek.talon
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@arcgtek:
		Factions: arc
		Prerequisite: gtek.arc
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@zocomgtek:
		Factions: zocom
		Prerequisite: gtek.zocom
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@eaglegtek:
		Factions: eagle
		Prerequisite: gtek.eagle
		RequiresCondition: !tech-locked
	SupportPowerChargeBar:
	ProductionCostMultiplier@TALONBONUS:
		Multiplier: 90
		Prerequisites: player.talon
	Encyclopedia:
		Category: GDI/Buildings

TMPL:
	Inherits: ^BuildingTD
	Inherits@IDISABLE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@FRENZYPOWER: ^FrenzyPower
	Inherits@TECHHACKPOWER: ^TechnologyHackPower
	Inherits@CLUSTERMISSILEPOWER: ^ClusterMissilePower
	Inherits@FORCESHIELDPOWER: ^ForceShieldPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Inherits@ProductionOptimizer2: ^ProductionOptimizer2
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 0,-896,0, 0,896,0, 840,0,0, -706,0,0, -706,-768,0, -706,640,0
	Valued:
		Cost: 1800
	Tooltip:
		Name: Temple of Nod
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 140
		Prerequisites: vehicles.any, anyradar, ~structures.nod, ~techlevel.high
		IconPalette: chrometd
		Description: Provides Nod advanced technologies.
	TooltipExtras:
		Attributes: • Requires power to operate\n• Special Ability: Cluster Missile
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Selectable:
		Bounds: 3072, 3072
	Health:
		HP: 110000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
	SupportPowerChargeBar:
	Power:
		Amount: -150
	MustBeDestroyed:
		RequiredForShortGame: false
	WithSupportPowerActivationAnimation:
		RequiresCondition: !build-incomplete
	ValidFactions:
		Factions: nod, blackh, marked, legion, shadow
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite:
		Prerequisite: techcenter.any
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@tdtek:
		Prerequisite: techcenter.td
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@ObeliskOrTemple:
		Prerequisite: obliortmpl
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@blackhtmpl:
		Factions: blackh
		Prerequisite: tmpl.blackh
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@markedtmpl:
		Factions: marked
		Prerequisite: tmpl.marked
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@legiontmpl:
		Factions: legion
		Prerequisite: tmpl.legion
		RequiresCondition: !tech-locked
	ProvidesPrerequisiteValidatedFaction@shadowtmpl:
		Factions: shadow
		Prerequisite: tmpl.shadow
		RequiresCondition: !tech-locked
	GrantExternalConditionPowerCA@FSHIELD:
		ActiveSequence: false-active
	Encyclopedia:
		Category: Nod/Buildings

PRIS:
	Inherits: ^Defense
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 80
		Prerequisites: anyradar, ~structures.pris, ~techlevel.medium
		Description: Advanced experimental base defense.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Requires power to operate\n• Can detect cloaked units\n• Beam can chain with nearby Prism Towers
	Valued:
		Cost: 1350
	Tooltip:
		Name: Prism Tower
	Building:
	Selectable:
		DecorationBounds: 1024, 1962, 0, -560
	Health:
		HP: 40000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	WithBuildingBib:
		HasMinibib: true
	WithSpriteBody:
		PauseOnCondition: disabled || empdisable || being-warped
	WithPrismChargeAnimation:
	AttackPrismSupported:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
		ChargeAudio: bpripow.aud
		MaxCharges: 1
		ReloadDelay: 40
		SupportArmament: support
		ReceiverOffset: 0,0,896
		BuffCondition: prism-stack
	FirepowerMultiplier@STACK-1:
		RequiresCondition: prism-stack == 1
		Modifier: 150
	FirepowerMultiplier@STACK-2:
		RequiresCondition: prism-stack == 2
		Modifier: 200
	FirepowerMultiplier@STACK-3:
		RequiresCondition: prism-stack == 3
		Modifier: 250
	FirepowerMultiplier@STACK-4:
		RequiresCondition: prism-stack == 4
		Modifier: 300
	Armament:
		Weapon: PrisLaser
		LocalOffset: 0,-85,956
	Armament@support:
		Name: support
		Weapon: PrisLaserSupport
		LocalOffset: 0,0,1024
	Power:
		Amount: -80
	ProvidesPrerequisite@buildingname:
	WithRangeCircle@SUPPORT:
		Type: PrismSupport
		Range: 5c0
		Color: ffffff66
	WithPrismLinkVisualization:
		Range: 5c0
		Color: 00ffffff
	Encyclopedia:
		Category: Allies/Defenses

SPEN.nod:
	Inherits: ^Building
	Inherits@WATERSTRUCTURE: ^WaterStructure
	Inherits@NAV: ^ProducesNaval
	RenderSprites:
		Image: SPENNOD
		PlayerPalette: playernavy
	InfiltrateToCreateProxyActor:
		Types: GrantSupportPowerInfiltrate
		Proxy: powerproxy.sonarpulse
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Valued:
		Cost: 800
	Tooltip:
		Name: Sub Pen
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 240
		IconPalette: chrometd
		Prerequisites: anypower, ~structures.nod, ~techlevel.navy, ~techlevel.low
		Description: Produces and repairs\nsubmarines and transports.
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	-GivesBuildableArea:
	RequiresBuildableArea:
		Adjacent: 8
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-213,0
		Facing: 384
		ExitCell: -1,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: 0,-213,0
		Facing: 640
		ExitCell: 3,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: 0,0,0
		Facing: 0
		ExitCell: 1,0
	RepairsUnits:
		HpPerStep: 1000
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: !forceshield && !invulnerability && !being-warped
	RallyPoint:
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	MapEditorData:
		ExcludeTilesets: INTERIOR
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 10c0
		RequiresCondition: !(empdisable || being-warped)
	RenderDetectionCircle:
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -598
			BottomRight: 1536, 598
	HitShape@TOPANDBOTTOM:
		TargetableOffsets: 811,0,0, -811,0,0
		Type: Rectangle
			TopLeft: -555, -1110
			BottomRight: 555, 1110
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-SpawnActorsOnSellCA:
	Encyclopedia:
		Category: Nod/Buildings

SYRD.gdi:
	Inherits: ^Building
	Inherits@WATERSTRUCTURE: ^WaterStructure
	Inherits@NAV: ^ProducesNaval
	RenderSprites:
		Image: GSYRD
		PlayerPalette: playernavy
	InfiltrateToCreateProxyActor:
		Types: GrantSupportPowerInfiltrate
		Proxy: powerproxy.sonarpulse
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 230
		Prerequisites: anypower, ~structures.gdi, ~techlevel.navy, ~techlevel.low
		Description: Produces and repairs ships\nand transports.
	Valued:
		Cost: 1000
	Tooltip:
		Name: Naval Yard
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: GrantSupportPowerInfiltrate
	Building:
		Footprint: XXX xxx XXX
		Dimensions: 3,3
		TerrainTypes: Water
	-GivesBuildableArea:
	RequiresBuildableArea:
		Adjacent: 8
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Exit@1:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,1024,0
		Facing: 640
		ExitCell: 0,2
	Exit@2:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,1024,0
		Facing: 896
		ExitCell: 2,2
	Exit@3:
		RequiresCondition: !being-captured
		SpawnOffset: -1024,-1024,0
		Facing: 384
		ExitCell: 0,0
	Exit@4:
		RequiresCondition: !being-captured
		SpawnOffset: 1024,-1024,0
		Facing: 128
		ExitCell: 2,0
	RepairsUnits:
		HpPerStep: 1000
		StartRepairingNotification: Repairing
		StartRepairingTextNotification: Repairing.
		FinishRepairingNotification: UnitRepaired
		FinishRepairingTextNotification: Unit repaired.
		RequiresCondition: !forceshield && !invulnerability && !being-warped
	RallyPoint:
	Power:
		Amount: -30
	ProvidesPrerequisite@buildingname:
	MapEditorData:
		ExcludeTilesets: INTERIOR
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 10c0
		RequiresCondition: !(empdisable || being-warped)
	RenderDetectionCircle:
	HitShape:
		TargetableOffsets: 768,0,0, 768,-1024,0, 768,1024,0
		Type: Rectangle
			TopLeft: -1536, -1152
			BottomRight: 1536, 598
	HitShape@BOTTOM:
		TargetableOffsets: -768,0,0
		Type: Rectangle
			TopLeft: -512, 598
			BottomRight: 512, 1110
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-SpawnActorsOnSellCA:
	Encyclopedia:
		Category: GDI/Buildings

HTUR:
	Inherits: ^Defense
	Inherits@AUTOTARGET: ^AutoTargetGround
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Inherits@SHAPE: ^2x2Shape
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 80
		Prerequisites: anyradar, ~structures.france, ~techlevel.medium
		Description: Artillery base defense.
	TooltipExtras:
		Strengths: • Strong vs Heavy Armor, Light Armor, Infantry
		Weaknesses: • Cannot attack Aircraft\n• Cannot hit targets at point-blank range
		Attributes: • Requires power to operate\n• Can detect cloaked units
	Valued:
		Cost: 1750
	Tooltip:
		Name: Grand Cannon
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 90000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Turreted:
		TurnSpeed: 8
		InitialFacing: 0
		RealignDelay: -1
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
	AttackTurreted:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
	Armament@1:
		Weapon: 203mm
		MuzzleSequence: muzzle
		LocalOffset: 1000,0,60
	Armament@2:
		Weapon: 203mm.Inacc
		MuzzleSequence: muzzle
		LocalOffset: 1000,200,60
		FireDelay: 10
	Armament@3:
		Weapon: 203mm.Inacc
		MuzzleSequence: muzzle
		LocalOffset: 1000,-200,60
		FireDelay: 20
	WithMuzzleOverlay:
	Power:
		Amount: -100
	ClassicFacingBodyOrientation:
	FireWarheadsOnDeath:
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode
	WithBuildingBib:
		HasMinibib: true
	Selectable:
		Bounds: 2048, 2048
	DetectCloaked:
		RequiresCondition: !(disabled || empdisable || being-warped)
	-ProductionCostMultiplier@FranceBonus:
	Encyclopedia:
		Category: Allies/Defenses
	EncyclopediaExtras:
		Subfaction: france

GUN.Nod:
	Inherits: GUN
	RenderSprites:
		Image: gun2
		PlayerPalette: playertd
	ActorPreviewPlaceBuildingPreview:
	Buildable:
		Prerequisites: infantry.any, ~structures.nod, ~techlevel.low
		IconPalette: chrometd
	Armament:
		Weapon: TurretGunTD
	Encyclopedia:
		Category: Nod/Defenses

LTUR:
	Inherits: GUN.Nod
	RenderSprites:
		Image: ltur
	Health:
		HP: 40000
	Valued:
		Cost: 600
	CustomSellValue:
		Value: 300
	Tooltip:
		Name: Laser Turret
	Buildable:
		BuildPaletteOrder: 60
		Prerequisites: infantry.any, ~structures.nod, ~techlevel.low
		Description: Anti-infantry base defense.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Weak vs Heavy Armor\n• Cannot attack Aircraft
		Attributes: • Can detect cloaked units
	Turreted:
		InitialFacing: 128
	-WithTurretAttackAnimation:
	GrantConditionOnPrerequisite@QUANTUM:
		Condition: quantum-upgrade
		Prerequisites: quantum.upgrade
	-Armament:
	Armament:
		Weapon: LaserTur
		LocalOffset: 512,0,172
		RequiresCondition: !quantum-upgrade
	Armament@Marked:
		Weapon: LaserTur.Adv
		LocalOffset: 512,0,172
		RequiresCondition: quantum-upgrade
	Power:
		Amount: -15
	DetectCloaked:
		Range: 5c0
	Encyclopedia:
		Category: Nod/Defenses

TTUR:
	Inherits: ^DefenseTD
	Inherits@AUTOTARGET: ^AutoTargetGround
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 60
		Prerequisites: infantry.any, ~structures.iraq, ~techlevel.low
		IconPalette: chrometd
		Description: Anti-infantry base defense.
	TooltipExtras:
		Strengths: • Strong vs Infantry, Light Armor
		Weaknesses: • Cannot attack Aircraft\n• Has difficulty hitting moving targets
		Attributes: • Can detect cloaked units
	Valued:
		Cost: 650
	CustomSellValue:
		Value: 220
	Tooltip:
		Name: Chemical Tower
	Building:
	Health:
		HP: 44000
	Armor:
		Type: Concrete
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithBuildingBib:
		HasMinibib: true
	Turreted:
		TurnSpeed: 512
		Offset: 0,0,112
	Armament:
		Weapon: ChemballLauncher
		LocalOffset: 512,0,0
	AttackTurreted:
		PauseOnCondition: build-incomplete || empdisable || being-warped
	-QuantizeFacingsFromSequence:
	ClassicFacingBodyOrientation:
		QuantizedFacings: 8
	Power:
		Amount: -15
	ProvidesPrerequisite@buildingname:
	ProvidesPrerequisite@ftur:
		Prerequisite: ftur
	ProvidesPrerequisite@FlameTowerOrRadar:
		Prerequisite: fturorradar
	ProvidesPrerequisite@ChemTowerOrSovietTechCenter:
		Prerequisite: tturorstek
	FireWarheadsOnDeath:
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode
	DetectCloaked:
		Range: 5c0
	Encyclopedia:
		Category: Soviets/Defenses
	EncyclopediaExtras:
		Subfaction: iraq

SGEN:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@TIBSTEALTHPOWER: ^TibStealthPower
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 130
		Prerequisites: tmpl, ~structures.nod, ~!player.shadow, ~techlevel.high
		BuildLimit: 1
		IconPalette: chrometd
		Description: Makes a group of units/structures invisible for a short time.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cloaks nearby units and structures when not active\n• Requires power to operate\n• Special Ability: Tiberium Stealth
	Valued:
		Cost: 1500
	Tooltip:
		Name: Stealth Generator
	Building:
		Footprint: xxx xx+
		Dimensions: 3,2
		LocalCenterOffset: 0,0,0
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithBuildingBib:
	ExternalCondition@ACTIVE:
		Condition: active
	SupportPowerChargeBar:
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1536, -1024
			BottomRight: 1536, 1024
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	WithRangeCircle@SGEN:
		Type: StealthGenerator
		Range: 5c512
		Color: 00aa00
	ProximityExternalCondition@SGEN:
		Range: 5c512
		Condition: sgencloak
		MaximumVerticalOffset: 512
		AffectsParent: true
		RequiresCondition: !disabled && !empdisable && !being-warped && !active && !cloak-force-disabled
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Encyclopedia:
		Category: Nod/Buildings

SGEN.Shadow:
	Inherits: SGEN
	Buildable:
		Prerequisites: tmpl, ~structures.nod, ~player.shadow, ~techlevel.high
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cloaks nearby units and structures\n• Requires power to operate\n• Special Ability: Tiberium Stealth
	WithRangeCircle@SGEN:
		Range: 9c512
	ProximityExternalCondition@SGEN:
		Range: 9c512
		RequiresCondition: !disabled && !empdisable && !being-warped
	RenderSprites:
		Image: sgen
	GrantExternalConditionPowerCA@SGEN:
		-ActiveCondition:
	-ExternalCondition@ACTIVE:
	-GrantConditionOnDamageState@UNCLOAK:
	-Encyclopedia:

CRAM:
	Inherits: AGUN
	Buildable:
		Prerequisites: vehicles.any, ~structures.gdi, ~techlevel.medium
		IconPalette: chrometd
	-Armament:
	-WithTurretAttackAnimation:
	Armament@GAT:
		Weapon: Gatt
		LocalOffset: 520,0,450
		MuzzleSequence: muzzle
		CasingWeapon: BrassDebris
		CasingSpawnLocalOffset: 0,-225,200
		CasingTargetOffset: 0, -600, 0
	FirepowerMultiplier@GAT1:
		Modifier: 115
		RequiresCondition: firing && gatling == 1
	FirepowerMultiplier@GAT2:
		Modifier: 130
		RequiresCondition: firing && gatling == 2
	FirepowerMultiplier@GAT3:
		Modifier: 145
		RequiresCondition: firing && gatling >= 3
	AmbientSoundCA@ATTACKSOUNDINITIAL:
		SoundFiles: vvullo1a.aud
		RequiresCondition: firing && gatling < 1
	AmbientSoundCA@ATTACKSOUND1:
		SoundFiles: vvullo2a.aud, vvullo2b.aud, vvullo2c.aud
		FinalSound: vvullo3a.aud
		RequiresCondition: firing && gatling == 1
	AmbientSoundCA@ATTACKSOUND2:
		InitialSound: vvullo4a.aud
		SoundFiles: vvullo5a.aud, vvullo5b.aud
		FinalSound: vvullo6a.aud
		RequiresCondition: firing && gatling == 2
	AmbientSoundCA@ATTACKSOUND3:
		InitialSound: vvullo7a.aud
		SoundFiles: vvullo8a.aud, vvullo8b.aud
		FinalSound: vvullo9a.aud
		RequiresCondition: firing && gatling >= 3
	GrantConditionOnAttackCA@FIRING:
		ArmamentNames: primary
		Condition: firing
		RevokeDelay: 6
	GrantConditionOnAttack:
		Condition: gatling
		RequiredShotsPerInstance: 1,2,4
		MaximumInstances: 3
		RevokeDelay: 55
		RevokeOnNewTarget: False
		RevokeAll: True
	Encyclopedia:
		Category: GDI/Defenses

LASP:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 30
		Prerequisites: ~structures.nod, nuk2, ~techlevel.medium
		IconPalette: chrometd
		Description: • Stops infantry\n• Blocks enemy fire\n• Cannot be crushed by tanks
	Building:
		RequiresBaseProvider: False
	Valued:
		Cost: 200
	CustomSellValue:
		Value: 0
	-GivesBuildableArea:
	RequiresBuildableArea:
		AreaTypes: building, defense
		Adjacent: 7
	Tooltip:
		Name: Laser Fence
	SoundOnDamageTransition:
		DamagedSounds: crmble2.aud
		DestroyedSounds: kaboom30.aud
	WithIdleAnimation@OFFLINE:
		Interval: 0
		Sequences: idle-offline
		RequiresCondition: disabled || empdisable || being-warped
	Health:
		HP: 100000
	Armor:
		Type: Concrete
	Sellable:
		RequiresCondition: !disabled && !empdisable && !c4 && !being-warped && !hacked
	-Capturable:
	-CaptureNotification:
	-CaptureManager:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-MustBeDestroyed:
	Power:
		Amount: -15
	RevealsShroud:
		Range: 3c0
	LineBuild:
		Range: 10
		NodeTypes: laserfencenode
		SegmentType: lasf
		SegmentsRequireNode: true
	LineBuildNode:
		Types: laserfencenode
	LineBuildSegmentExternalCondition:
		RequiresCondition: !disabled && !empdisable && !being-warped && !hacked
		Condition: active-posts
	FireWarheadsOnDeath@TEMPORAL:
		Weapon: TemporalExplode
		EmptyWeapon: TemporalExplode
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Encyclopedia:
		Category: Nod/Defenses

LASF:
	Inherits: ^Wall
	Tooltip:
		Name: Laser Fence
	LineBuild:
		NodeTypes: laserfence
	LineBuildNode:
		Types: laserfence
	-Crushable:
	-Sellable:
	-RenderSprites:
	RenderSprites:
		Palette: effect-ignore-lighting-alpha85
	-Targetable:
	-Building:
	-SoundOnDamageTransition:
	EnergyWall:
		ActiveCondition: active-posts == 2
		Weapon: LaserFence
		TerrainTypes: Clear, Rough, Road
	GrantConditionOnLineBuildDirection@X:
		Direction: X
		Condition: laserfence-direction-x
	GrantConditionOnLineBuildDirection@Y:
		Direction: Y
		Condition: laserfence-direction-y
	-WithWallSpriteBody:
	ExternalCondition@ACTIVE:
		Condition: active-posts
	WithWallSpriteBody@XENABLED:
		RequiresCondition: laserfence-direction-x && active-posts == 2
		Type: laserfence
		Sequence: enabled-x
		Name: x-enabled
	WithWallSpriteBody@YENABLED:
		RequiresCondition: laserfence-direction-y && active-posts == 2
		Type: laserfence
		Sequence: enabled-y
		Name: y-enabled
	WithSpriteBody@XDISABLED:
		RequiresCondition: laserfence-direction-x && active-posts < 2
		Sequence: disabled-x
		Name: x-disabled
	WithSpriteBody@YDISABLED:
		RequiresCondition: laserfence-direction-y && active-posts < 2
		Sequence: disabled-y
		Name: y-disabled
	BlocksProjectiles:
		RequiresCondition: active-posts >= 2
	DamageMultiplier: # Prevent all normal damage, but still allows direct kills from the post
		Modifier: 0
	Interactable:
		Bounds: 2048, 1024

PATR:
	Inherits: ^BuildingTD
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x2Shape
	Inherits@EMPMISSILEPOWER: ^EmpMissilePower
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 130
		Prerequisites: gtek, ~structures.gdi, ~techlevel.high
		BuildLimit: 1
		IconPalette: chrometd
		Description: Launches E.M. Pulse Missiles that disable vehicles & structures.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: E.M. Pulse Missile
	Valued:
		Cost: 1500
	Tooltip:
		Name: EMP Missile Launcher
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	Turreted:
		TurnSpeed: 15
		InitialFacing: 192
		RealignDelay: -1
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	-WithSpriteBody:
	WithEmbeddedTurretSpriteBody:
		PauseOnCondition: disabled || empdisable || being-warped
	WithIdleAnimation:
		Sequences: empty
		Interval: 0
		RequiresCondition: !loaded-rocket
	AttackTurretedCharged:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
		RequiresCondition: loaded-rocket > 0
		ChargeLevel: 35
		DischargeRate: 35
		ShowSelectionBar: true
		SelectionBarColor: FFFFFF
		ChargeWhileTurning: true
	Armament:
		Weapon: EMPMissileLauncher
		LocalOffset: 511,396,511
	AmmoPool:
		Ammo: 1
		ReloadCount: 1
		AmmoCondition: loaded-rocket
	ReloadAmmoPool:
		Delay: 100
		Count: 1
	SupportPowerChargeBar:
	Power:
		Amount: -200
	ClassicFacingBodyOrientation:
	WithBuildingBib:
		HasMinibib: true
	Selectable:
		Bounds: 2048, 2048
	RejectsOrders:
		Except: Stop, Sell, PowerDown
		RequiresCondition: !loaded-rocket
	RejectsOrders@LOADED:
		Except: Sell, PowerDown
		RequiresCondition: loaded-rocket
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	RenderRangeCircle:
		RangeCircleType: DefenseRange
		Color: C12900
	-WithDeathAnimation:
	Encyclopedia:
		Category: GDI/Buildings

CVAT:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^2x2Shape
	Inherits@TECHLOCKABLE: ^TechLockable
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 630,-512,0, 355,512,0, -281,-512,0, -630,512,0
	Tooltip:
		Name: Cloning Vat
	Building:
		Footprint: xx xx ++
		Dimensions: 2,3
		LocalCenterOffset: 0,-1024,0
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		Prerequisites: ~structures.soviet, stek, playerxp.level3, ~infantry.doctrine, ~techlevel.high
		Description: Clones infantry trained at a linked barracks.
		BuildLimit: 1
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked
	Valued:
		Cost: 2000
	Health:
		HP: 120000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Production:
		Produces: Clone
		PauseOnCondition: tech-locked || being-warped
	LinkedProducerTarget:
		Mode: Clone
		Types: InfantrySQ, InfantryMQ
		Produces: Clone
	Exit@1:
		SpawnOffset: -700,1000,0
		ExitCell: -2,2
		Facing: 90
	Exit@2:
		SpawnOffset: 190,-400,0
		ExitCell: 1,-1
	RallyPoint:
	Power:
		Amount: -150
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	SpawnRandomActorOnDeath:
		RequiresCondition: !being-warped
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	Encyclopedia:
		Category: Soviets/Buildings

INDP:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@TECHLOCKABLE: ^TechLockable
	Selectable:
		Bounds: 3072, 2816, 0, -256
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -1024
			BottomRight: 1536, 1280
	WithSpriteBody:
		PauseOnCondition: disabled || being-warped
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		Prerequisites: ~structures.soviet, stek, playerxp.level3, ~armor.doctrine, ~techlevel.high
		Description: Vehicles & aircraft are produced 15% faster and cost 10% less.
		BuildLimit: 1
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked
	Valued:
		Cost: 2000
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Tooltip:
		Name: Industrial Plant
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 120000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: -150
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@disabled:
		Prerequisite: indplowpower
		RequiresCondition: disabled || empdisable || being-warped
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	SpawnRandomActorOnDeath:
		RequiresCondition: !being-warped
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	Encyclopedia:
		Category: Soviets/Buildings

MUNP:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@TECHLOCKABLE: ^TechLockable
	Selectable:
		Bounds: 3072, 2816, 0, -256
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -1024
			BottomRight: 1536, 1280
	WithSpriteBody:
		PauseOnCondition: disabled || being-warped
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		Prerequisites: ~structures.soviet, stek, playerxp.level3, ~arty.doctrine, ~techlevel.high
		Description: Vehicles & aircraft are produced 15% faster. Vehicles have 15% reduced reload time.
		BuildLimit: 1
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked
	Valued:
		Cost: 2000
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Tooltip:
		Name: Munitions Plant
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 120000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Power:
		Amount: -150
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@IndpOrMunp:
		Prerequisite: indpormunp
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@disabled:
		Prerequisite: munplowpower
		RequiresCondition: disabled || empdisable || being-warped
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	SpawnRandomActorOnDeath:
		RequiresCondition: !being-warped
	GrantCondition@DUMMY:
		Condition: noupgrade
		RequiresCondition: noupgrade
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	Encyclopedia:
		Category: Soviets/Buildings

OREP:
	Inherits: ^Building
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@TECHLOCKABLE: ^TechLockable
	Selectable:
		Bounds: 3072, 2048
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		Prerequisites: ~structures.allies, atekoralhq, ~techlevel.high
		Description: Refines ore, gems & Tiberium, increasing income by 10%.
		BuildLimit: 1
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Valued:
		Cost: 1600
	Tooltip:
		Name: Ore Purifier
	ResourcePurifierCA@Normal:
		Modifier: 10
		RequiresCondition: !disabled && !being-warped && !tech-locked && !economy-policy3
	ResourcePurifierCA@EconomyPolicy3:
		Modifier: 15
		RequiresCondition: !disabled && !being-warped && !tech-locked && economy-policy3
	GrantConditionOnPrerequisite@EconomyPolicy3:
		Condition: economy-policy3
		Prerequisites: economy.policy, influence.level3
	Power:
		Amount: -100
	WithDeathAnimation:
		DeathSequence: dead
		UseDeathTypeSuffix: false
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	SpawnRandomActorOnDeath:
		RequiresCondition: !being-warped
	GrantConditionOnPrerequisite@OwnedByAi:
		Condition: owned-by-ai
		Prerequisites: botplayer
	GrantRandomCondition@ai-pcan:
		Conditions: ai-pcan-upgrade, noupgrade
	ProvidesPrerequisite@ai-pcan:
		Prerequisite: pcan.upgrade
		RequiresCondition: ai-pcan-upgrade && owned-by-ai
	GrantCondition@DUMMY:
		Condition: noupgrade
		RequiresCondition: noupgrade
	ValidFactions:
		Factions: allies, england, germany, france, usa
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	Encyclopedia:
		Category: Allies/Buildings

UPGC:
	Inherits: ^BuildingTD
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@SHAPE: ^3x2Shape
	Inherits@UPG: ^ProducesUpgrades
	Inherits@FIRESTORMPOWER: ^FirestormPower
	Inherits@ADVANCEDRADARPOWER: ^AdvancedRadarPower
	Inherits@NANITESHIELDPOWER: ^NaniteShieldPower
	Inherits@TECHLOCKABLE: ^TechLockable
	Selectable:
		Bounds: 3072, 2560, 0, 256
	Building:
		Footprint: xxx xxx ===
		Dimensions: 3,3
		LocalCenterOffset: 0,-1024,0
	HitShape:
		Type: Rectangle
			TopLeft: -1536, -1024
			BottomRight: 1536, 1536
	Health:
		HP: 140000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	WithSpriteBody:
		PauseOnCondition: disabled || being-warped
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		Prerequisites: ~structures.gdi, gtek, ~techlevel.high
		Description: Allows the construction of advanced weaponry and upgrades.
		BuildLimit: 1
		IconPalette: chrometd
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Valued:
		Cost: 1800
	Tooltip:
		Name: Upgrade Center
	ValidFactions:
		Factions: gdi, talon, zocom, eagle, arc
	ProvidesPrerequisiteValidatedFaction@talonupgc:
		Factions: talon
		Prerequisite: upgc.talon
	ProvidesPrerequisiteValidatedFaction@zocomupgc:
		Factions: zocom
		Prerequisite: upgc.zocom
	ProvidesPrerequisiteValidatedFaction@eagleupgc:
		Factions: eagle
		Prerequisite: upgc.eagle
	ProvidesPrerequisiteValidatedFaction@arcupgc:
		Factions: arc
		Prerequisite: upgc.arc
	ProvidesPrerequisite@GDIORUPGC:
		Prerequisite: gdiorupgc
	ProvidesPrerequisite@Name:
	Power:
		Amount: -100
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	SpawnRandomActorOnDeath:
		RequiresCondition: !being-warped
	ProductionAirdropCA:
		Produces: Money
		PauseOnCondition: forceshield || invulnerability || being-warped
		ActorType: ocar.pod
		IncomingAudio: SuppliesInbound
		SpawnType: ClosestEdgeToDestination
		ProportionalSpeed: true
	WithDeliveryOverlay:
		Sequence: flare
		Palette: effect
		IsPlayerPalette: false
		Offset: -1024,768,0
	PeriodicProducerCA@DROP:
		Type: Money
		ChargeDuration: 1500
		Actors: truk.drop
		ShowSelectionBar: true
		RequiresCondition: tower.drop
		PauseOnCondition: empdisable || disabled || being-warped
	Exit:
		SpawnOffset: 511,511,0
		ExitCell: 1,1
	Turreted:
		TurnSpeed: 12
		InitialFacing: 384
		RealignDelay: -1
		Offset: -768,468,0
		RequiresCondition: !build-incomplete
		PauseOnCondition: disabled || empdisable || being-warped
	AttackTurretedCharged:
		PauseOnCondition: build-incomplete || disabled || empdisable || being-warped
		RequiresCondition: tower.rocket && loaded-rocket > 0
		ChargeLevel: 45
		DischargeRate: 45
		ShotsPerCharge: 3
		ShowSelectionBar: true
		SelectionBarColor: FF4400
		ChargeWhileTurning: true
	Armament@ROCKET:
		RequiresCondition: tower.rocket
		Weapon: FirestormBarrage
		LocalOffset: 768,64,368, 768,-64,368, 768,64,255, 768,-64,255
	SupportPowerChargeBar:
	RejectsOrders:
		Except: Stop, Sell, PowerDown
		RequiresCondition: !loaded-rocket
	RejectsOrders@LOADED:
		Except: Sell, PowerDown
		RequiresCondition: loaded-rocket
	AmmoPool:
		Ammo: 3
		ReloadCount: 3
		AmmoCondition: loaded-rocket
	ReloadAmmoPool:
		Delay: 100
		Count: 3
	WithSpriteTurret@ROCKET:
		RequiresCondition: !build-incomplete && tower.rocket
		Recoils: false
		Sequence: turret-rocket
	WithIdleOverlay@RADAR:
		RequiresCondition: !build-incomplete && tower.radar
		Sequence: turret-radar
		PauseOnCondition: !scan-active || disabled || being-warped
	WithIdleOverlay@SHIELD:
		RequiresCondition: !build-incomplete && tower.shield && !nshield-active
		Sequence: turret-shield
		PauseOnCondition: disabled || being-warped
	WithIdleOverlay@SHIELD-ACTIVE:
		RequiresCondition: !build-incomplete && tower.shield && nshield-active
		Sequence: turret-shield-active
		PauseOnCondition: disabled || being-warped
	WithIdleOverlay@SHIELD-ACTIVE-EFFECT:
		RequiresCondition: !build-incomplete && tower.shield && nshield-active
		Sequence: emp-overlay
		Palette: tseffect
		PauseOnCondition: disabled || being-warped
		Offset: -1284,384,0
	WithIdleOverlay@DZONE:
		RequiresCondition: !build-incomplete && tower.drop
		Sequence: turret-drop
		PauseOnCondition: disabled || being-warped
	DrainPrerequisitePowerOnDamage@RADAR:
		OrderName: advradarorder
	GrantConditionOnPrerequisite@SCAN:
		Prerequisites: scan-active
		Condition: scan-active
	GrantConditionOnBotOwner@RADAR:
		Condition: scan-active
		Bots: brutal, vhard, hard, normal, easy, naval
	GrantConditionOnPrerequisite@RADAR:
		Prerequisites: radar-active
		Condition: radar-active
	GpsRadarProvider:
		RequiresCondition: tower.radar && scan-active && !(disabled || being-warped)
	ExternalCondition@NSACTIVE:
		Condition: nshield-active
	ExternalCondition@NSAUDIO:
		Condition: nshield-audio
	ProximityExternalCondition@NSHIELD-AUDIO:
		Condition: nshield-audio
		RequiresCondition: nshield-active
		AffectsParent: true
		EnableSound: uscolo1.aud
	AmbientSound@NSHIELD-AUDIO:
		SoundFiles: uscolo2a.aud, uscolo2b.aud, uscolo2c.aud
		Delay: 3
		Interval: 0
		RequiresCondition: nshield-audio || nshield-active
	Pluggable:
		Conditions:
			tower.rocket: tower.rocket
			tower.radar: tower.radar
			tower.shield: tower.shield
			tower.drop: tower.drop
		Requirements:
			tower.rocket: !build-incomplete && !tower.radar && !tower.rocket && !tower.shield && !tower.drop
			tower.radar: !build-incomplete && !tower.radar && !tower.rocket && !tower.shield && !tower.drop
			tower.shield: !build-incomplete && !tower.radar && !tower.rocket && !tower.shield && !tower.drop
			tower.drop: !build-incomplete && !tower.radar && !tower.rocket && !tower.shield && !tower.drop
	ProvidesPrerequisite@pluggable:
		RequiresCondition: !build-incomplete && (tower.rocket || tower.radar || tower.shield || tower.drop)
		Prerequisite: upgc.plugged
	ProvidesPrerequisite@AMPSEEK:
		RequiresCondition: !build-incomplete && tower.radar
		Prerequisite: seek3.strat
		RequiresPrerequisites: seek.strat
	ProvidesPrerequisite@AMPBOMB:
		RequiresCondition: !build-incomplete && tower.rocket
		Prerequisite: bombard3.strat
		RequiresPrerequisites: bombard.strat
	ProvidesPrerequisite@AMPHOLD:
		RequiresCondition: !build-incomplete && tower.shield
		Prerequisite: hold3.strat
		RequiresPrerequisites: hold.strat
	ProvidesPrerequisite@AMPDROP:
		RequiresCondition: !build-incomplete && tower.drop
		Prerequisite: dropzone
	GrantConditionOnPrerequisite:
		Prerequisites: nshield
		Condition: nshield-active
	ProductionCostMultiplier@TALONBONUS:
		Multiplier: 80
		Prerequisites: player.talon
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	UpdatesCount:
		Type: StrategyLevel
		RequiresCondition: tower.rocket || tower.radar || tower.shield || tower.drop
	FreeActor@SubsequentDiscountBomb:
		Actor: upgc.bomb.previous
		RequiresCondition: tower.rocket
	FreeActor@SubsequentDiscountSeek:
		Actor: upgc.seek.previous
		RequiresCondition: tower.radar
	FreeActor@SubsequentDiscountHold:
		Actor: upgc.hold.previous
		RequiresCondition: tower.shield
	FreeActor@SubsequentDiscountDrop:
		Actor: upgc.drop.previous
		RequiresCondition: tower.drop
	UpdatesSupportPowerTimer@Firestorm:
		OrderName: rocketbarrage
		InitialOnly: true
		RequiresCondition: tower.rocket
		Ticks: 3000
	UpdatesSupportPowerTimer@AdvancedRadar:
		OrderName: advradarorder
		InitialOnly: true
		RequiresCondition: tower.radar
		Ticks: 75
	UpdatesSupportPowerTimer@NaniteShield:
		OrderName: nshieldorder
		InitialOnly: true
		RequiresCondition: tower.shield
		Ticks: 1500
	ProvidesUpgrade@Firestorm:
		RequiresCondition: tower.rocket
		Type: upgc.bomb
	ProvidesUpgrade@AdvancedRadar:
		RequiresCondition: tower.radar
		Type: upgc.seek
	ProvidesUpgrade@NaniteShield:
		RequiresCondition: tower.shield
		Type: upgc.hold
	ProvidesUpgrade@SupplyDrop:
		RequiresCondition: tower.drop
		Type: upgc.drop
	Encyclopedia:
		Category: GDI/Buildings

UPGC.SOCK:
	AlwaysVisible:
	Interactable:
	Tooltip:
		Name: Component Platform

UPGC.BOMB:
	Inherits: ^BuildingPlug
	Valued:
		Cost: 1000
	Tooltip:
		Name: Upgrade: Firestorm Missile System
	Buildable:
		Queue: Upgrade
		BuildPaletteOrder: 8
		Prerequisites: ~player.gdi, upgc, !upgc.plugged, ~bombard2.strat, ~bombard.strat, ~!seek.strat, ~!hold.strat, ~techlevel.high
		Description: Provides an napalm rocket barrage.
		IconPalette: chrometd
	TooltipExtras:
		Strengths: + Increases firepower and rate of fire of vehicles and aircraft by a further 3%\n+ Unlocks Firestorm Missile Barrage power
		Attributes: \n• Place on Upgrade Center\n(!) Mutually exclusive with Supply Drop Zone
	Plug:
		Type: tower.rocket
	Power:
		Amount: -50
	RenderSprites:
		PlayerPalette: playertd
	WithProductionIconOverlay:
		Types: Upgrade
		Prerequisites: bombard3.strat
	ProductionCostMultiplier@SubsequentDiscount:
		Multiplier: 20
		Prerequisites: upgc.bomb.previous
	Encyclopedia:
		Category: GDI/Upgrades
	EncyclopediaExtras:
		AdditionalInfo: Requires Bombardment Strategy.

UPGC.SEEK:
	Inherits: ^BuildingPlug
	Valued:
		Cost: 1000
	Tooltip:
		Name: Upgrade: Advanced Radar System
	Buildable:
		Queue: Upgrade
		BuildPaletteOrder: 10
		Prerequisites: ~player.gdi, upgc, !upgc.plugged, ~seek2.strat, ~seek.strat, ~!bombard.strat, ~!hold.strat, ~techlevel.high
		Description: Provides detailed information on enemy locations.
		IconPalette: chrometd
	TooltipExtras:
		Strengths: + Increases speed and weapon range of vehicles and aircraft by a further 5%\n+ Unlocks Advanced Radar Scan power
		Attributes: \n• Place on Upgrade Center\n(!) Mutually exclusive with Supply Drop Zone
	Plug:
		Type: tower.radar
	Power:
		Amount: -50
	RenderSprites:
		PlayerPalette: playertd
	WithProductionIconOverlay:
		Types: Upgrade
		Prerequisites: seek3.strat
	ProductionCostMultiplier@SubsequentDiscount:
		Multiplier: 20
		Prerequisites: upgc.seek.previous
	Encyclopedia:
		Category: GDI/Upgrades
	EncyclopediaExtras:
		AdditionalInfo: Requires Seek & Destroy Strategy.

UPGC.HOLD:
	Inherits: ^BuildingPlug
	Valued:
		Cost: 1000
	Tooltip:
		Name: Upgrade: Nanoshield Generator
	Buildable:
		Queue: Upgrade
		BuildPaletteOrder: 12
		Prerequisites: ~player.gdi, upgc, !upgc.plugged, ~hold2.strat, ~hold.strat, ~!bombard.strat, ~!seek.strat, ~techlevel.high
		Description: Provides the ability to shield units in an area
		IconPalette: chrometd
	TooltipExtras:
		Strengths: + Increases armor of vehicles and aircraft by a further 5%\n+ Unlocks Nanite Shield power
		Attributes: \n• Place on Upgrade Center\n(!) Mutually exclusive with Supply Drop Zone
	Plug:
		Type: tower.shield
	Power:
		Amount: -50
	RenderSprites:
		PlayerPalette: playertd
	WithProductionIconOverlay:
		Types: Upgrade
		Prerequisites: hold3.strat
	ProductionCostMultiplier@SubsequentDiscount:
		Multiplier: 20
		Prerequisites: upgc.hold.previous
	Encyclopedia:
		Category: GDI/Upgrades
	EncyclopediaExtras:
		AdditionalInfo: Requires Hold the Line Strategy.

UPGC.DROP:
	Inherits: ^BuildingPlug
	Valued:
		Cost: 1000
	Tooltip:
		Name: Upgrade: Supply Drop Zone
	Buildable:
		Queue: Upgrade
		BuildPaletteOrder: 90
		Prerequisites: ~gdiorupgc, upgc, !upgc.plugged, ~techlevel.high
		Description: Provides a dropzone for emergency supplies.
		IconPalette: chrometd
	TooltipExtras:
		Strengths: + $1250 delivered every 60 seconds (normal speed)
		Attributes: \n• Place on Upgrade Center\n(!) Mutually exclusive with Firestorm Missile System, Advanced Radar System and Nanoshield Generator
	Plug:
		Type: tower.drop
	Power:
		Amount: -25
	RenderSprites:
		PlayerPalette: playertd
	WithProductionIconOverlay:
		Types: Upgrade
		Prerequisites: dropzone
	ProductionCostMultiplier@SubsequentDiscount:
		Multiplier: 20
		Prerequisites: upgc.drop.previous
	Encyclopedia:
		Category: GDI/Upgrades

^BuildingPlugPrevious:
	Inherits@DUMMY: ^InvisibleDummy
	ProvidesPrerequisite:
	PopControlled:

upgc.bomb.previous:
	Inherits: ^BuildingPlugPrevious

upgc.seek.previous:
	Inherits: ^BuildingPlugPrevious

upgc.hold.previous:
	Inherits: ^BuildingPlugPrevious

upgc.drop.previous:
	Inherits: ^BuildingPlugPrevious

TMPP:
	Inherits: ^BuildingTD
	Inherits@POWER_OUTAGE: ^DisableOnLowPowerOrForceDisabled
	Inherits@INF: ^ProducesCyborgs
	Inherits@UPG: ^ProducesUpgrades
	Inherits@TECHLOCKABLE: ^TechLockable
	HitShape@TOP:
		Type: Rectangle
			TopLeft: -512, -1450
			BottomRight: 896, -512
	RenderSprites:
		PlayerPalette: playertd
	Selectable:
		Bounds: 3072, 2560, 0, 256
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 512,0,0, -256,0,0, -256,1024,0, -256,-1024,0, -1536,0,0, -1536,1024,0, -1536,-1024,0
		Type: Rectangle
			TopLeft: -1536, -1024
			BottomRight: 1536, 1536
	Building:
		Footprint: ___ xxx xxx ===
		Dimensions: 3,4
		LocalCenterOffset: 0,-512,0
	Health:
		HP: 120000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	WithBuildingBib:
	Buildable:
		Queue: BuildingSQ, BuildingMQ
		BuildPaletteOrder: 200
		IconPalette: chrometd
		Prerequisites: ~structures.nod, tmpl, ~techlevel.high
		Description: Provides access to cybernetic upgrades.
		BuildLimit: 1
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Cannot be captured or hacked\n• Can convert regular infantry to cyborgs
	-Capturable:
	-CaptureNotification:
	-CapturableProgressBar:
	-CapturableProgressBlink:
	-CaptureManager:
	Valued:
		Cost: 2000
	Tooltip:
		Name: Temple Prime
	Power:
		Amount: -150
	ProvidesPrerequisite@buildingname:
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@nod:
		Prerequisite: infantry.nod
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@marked:
		Factions: marked
		Prerequisite: infantry.marked
		RequiresCondition: !tech-locked
	ProvidesPrerequisite@mech:
		Prerequisite: infantry.mech
		RequiresCondition: !tech-locked
	Sellable:
		RequiresCondition: !build-incomplete && !c4 && !being-warped
	SpawnActorOnDeath:
		RequiresCondition: !being-warped
	SpawnRandomActorOnDeath:
		RequiresCondition: !being-warped
	Exit@1:
		SpawnOffset: -170,1310,0
		ExitCell: 1,3
	Exit@2:
		SpawnOffset: 190,-400,0
		ExitCell: 1,-1
		Priority: 0
	RallyPoint:
	UnitConverter:
		Type: Cyborg
		ReadyAudio: UnitUpgraded
		NoCashAudio: InsufficientFunds
		BlockedAudio: OnHold
		CostDifferenceRequired: true
		OverrideUnitBuildDurationModifier: true
		EjectOnDeploy: true
		ConvertingCondition: converting
	WithUnitConverterCountDecoration:
		Font: Bold
		Position: Top
		Color: FF0000
		Margin: 0,-15
		RequiresCondition: converting
	PrimaryBuilding:
		RequiresCondition: !converting && !global-multiqueue
	WithDecoration@TechLock:
		RequiresCondition: tech-locked
	-MindControllable@HACKABLE:
	-Targetable@HACKABLE:
	-MindControllableProgressBar@HACKABLE:
	-WithDecoration@HACKED:
	-WithDecoration@RESTORING:
	-PowerMultiplier@HACKED:
	ProductionCostMultiplier@MARKEDBONUS:
		Multiplier: 90
		Prerequisites: player.marked
	Encyclopedia:
		Category: Nod/Buildings

MSLO.Nod:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x1Shape
	Inherits@CHEMMISSILEPOWER: ^ChemicalMissilePower
	RenderSprites:
		Image: nmslo
	Selectable:
		Bounds: 2048, 1280
	Valued:
		Cost: 2500
	Tooltip:
		Name: Missile Silo
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 140
		IconPalette: chrometd
		Prerequisites: tmpl, ~techlevel.unrestricted, ~structures.nod
		BuildLimit: 1
		Description: Provides a chemical missile.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Chemical Missile
	Building:
		Footprint: xx
		Dimensions: 2,1
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	SupportPowerChargeBar:
	Power:
		Amount: -200
	MustBeDestroyed:
		RequiredForShortGame: false
	WithSupportPowerActivationAnimation:
		RequiresCondition: !build-incomplete
	ProvidesPrerequisite@buildingname:
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	Encyclopedia:
		Category: Nod/Buildings

WEAT:
	Inherits: ^Building
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDownOrForceDisable
	Inherits@SHAPE: ^2x2Shape
	Inherits@STORMPOWER: ^LightningStormPower
	Selectable:
		Bounds: 2048, 2048
	Buildable:
		Queue: DefenseSQ, DefenseMQ
		BuildPaletteOrder: 140
		Prerequisites: atek, ~structures.allies, ~techlevel.unrestricted
		BuildLimit: 1
		Description: Provides Lightning Storm support power.
	TooltipExtras:
		Attributes: • Maximum 1 can be built\n• Requires power to operate\n• Special Ability: Lightning Storm
	Valued:
		Cost: 2500
	Tooltip:
		Name: Weather Control Device
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Health:
		HP: 100000
	Armor:
		Type: Wood
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RequiresCondition: !disabled
		RevealGeneratedShroud: False
	RevealsShroud@Offline:
		Range: 5c0
		RequiresCondition: disabled
	RevealsShroud@GAPGEN:
		Range: 5c0
		RequiresCondition: !disabled
	WithIdleOverlay@ACTIVE:
		Sequence: active-overlay
		Palette: d2keffect
		RequiresCondition: active
	SupportPowerChargeBar:
	Power:
		Amount: -200
	ProvidesPrerequisite@buildingname:
	MustBeDestroyed:
		RequiredForShortGame: false
	InfiltrateForSupportPowerReset:
		Types: ResetSupportPowerInfiltrate
		InfiltrationNotification: BuildingInfiltrated
		InfiltratedNotification: BaseAttack
		PlayerExperience: 15
	Targetable@INFILTRATION:
		RequiresCondition: !being-warped
		TargetTypes: ResetSupportPowerInfiltrate
	WithBuildingBib:
		HasMinibib: true
	Encyclopedia:
		Category: Allies/Buildings

IOK:
	Inherits: ^BasicBuilding
	-FrozenUnderFog:
	-FrozenUnderFogUpdatedByGpsRadar:
	-ShakeOnDeath:
	FireWarheadsOnDeath:
		Weapon: SmallBuildingExplode
		EmptyWeapon: SmallBuildingExplode
	HiddenUnderFog:
	Selectable:
		Bounds: 1024, 1024
		DecorationBounds: 896, 1706, 0, -448
	Health:
		HP: 15000
	Tooltip:
		Name: Idol of Kane
	TooltipExtras:
		Strengths: • Increases damage and movement speed of friendly infantry\n• Reduces damage and movement speed of enemy infantry\n• Provides vision
		Weaknesses: • Cannot attack
		Attributes: • Special Ability: Rage Generator (enemy units within range go berserk)
	Armor:
		Type: Concrete
	Targetable:
		TargetTypes: Ground, Structure, Defense
	RevealsShroud:
		MinRange: 6c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 6c0
	Building:
		Dimensions: 1,1
		Footprint: x
		TerrainTypes: Clear,Road
	WithIdleOverlay:
		Sequence: holo
		Palette: player
		IsPlayerPalette: True
		RequiresCondition: !build-incomplete
	WithIdleOverlay@Outline:
		Sequence: outline
		Palette: player
		IsPlayerPalette: True
	WithMakeAnimation:
		Condition: build-incomplete
	AmbientSoundCA:
		Interval: 100000
		SoundFiles: iok-create1.aud, iok-create2.aud, iok-create3.aud, iok-create4.aud, iok-create5.aud
	SoundOnDamageTransition:
		-DamagedSounds:
		DestroyedSounds: kaboom15.aud
	ProximityExternalCondition@IdolOfKaneBuff:
		Condition: iokbuff
		Range: 5c0
		ValidRelationships: Ally
		RequiresCondition: !rage-active && initialized
	ProximityExternalCondition@IdolOfKaneDebuff:
		Condition: iokdebuff
		Range: 5c0
		ValidRelationships: Neutral, Enemy
		RequiresCondition: !rage-active && initialized
	ProximityExternalCondition@Rage:
		Condition: berserk
		Range: 5c0
		ValidRelationships: Neutral, Enemy
		RequiresCondition: rage-active
	WithRadiatingCircle:
		EndRadius: 5c0
		Interval: 40
		Duration: 30
		ValidRelationships: Ally, Enemy, Neutral
		AlwaysShowMaxRange: true
		Visible: Always
		MaxRadiusColor: 66666660
		MaxRadiusFlashColor: 66666660
		Color: 66666630
		Width: 2
		RequiresCondition: !rage-active && initialized
	WithRadiatingCircle@Rage:
		EndRadius: 5c0
		Interval: 5
		Duration: 50
		ValidRelationships: Ally, Enemy, Neutral
		AlwaysShowMaxRange: true
		Visible: Always
		MaxRadiusColor: FF000070
		MaxRadiusFlashColor: FF000090
		Color: FF000040
		Width: 2
		RequiresCondition: rage-active
	GrantTimedConditionOnDeploy@Rage:
		DeployedCondition: rage-active
		ShowSelectionBar: true
		DeployedTicks: 125
		CooldownTicks: 375
		ShowSelectionBarWhenFull: false
		ChargingColor: 770000
		DischargingColor: ff0000
		DeploySound: rage-gen.aud
		Instant: true
		StartsFullyCharged: true
		RequiresCondition: initialized
	WithColoredOverlay@Rage:
		RequiresCondition: rage-active
		Color: FF000033
	ReloadAmmoPoolCA@InitialRage:
		Delay: 125
		SelectionBarColor: 770000
	AmmoPool@InitialRage:
		Ammo: 1
		InitialAmmo: 0
		AmmoCondition: initialized
