^Palettes:
	TintPostProcessEffect:

World:
	LuaScript:
		Scripts: campaign.lua, conspiracy.lua
	MissionData:
		Briefing: The Allied attack on our research facility in North Africa happened more swiftly than anticipated. While most of our assets were safely removed, two highly valued researchers were captured.\n\nWhile I have no reason to doubt their loyalty to the Brotherhood, their captivity is unacceptable. Any information falling into enemy hands jeopardizes our mission.\n\nYour objective is two-fold. A GDI base exists close to where the researchers are currently being held. One of our sleeper cells recently moved into position there and is poised to take control of the base. Take a small force to distract and weaken the GDI forces, and when the time is right our brothers will make their allegiance known.\n\nWith the base under our control, locate our captured brethren and bring them home.\n\nEven if the Allies don't suspect <PERSON><PERSON> of outright treachery, the fact that they were so deeply compromised will surely make the Allies reconsider how closely they cooperate in future.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: nomercy

Player:
	PlayerResources:
		DefaultCash: 6000

# Disable tech

MSLO.Nod:
	Inherits@CAMPAIGNDISABLED: ^Disabled

ENLI:
	Inherits@CAMPAIGNDISABLED: ^Disabled

RMBC:
	Inherits@CAMPAIGNDISABLED: ^Disabled

TMPP:
	Inherits@CAMPAIGNDISABLED: ^Disabled

advcyber.upgrade:
	Inherits@CAMPAIGNDISABLED: ^Disabled

cyborgarmor.upgrade:
	Inherits@CAMPAIGNDISABLED: ^Disabled

cyborgspeed.upgrade:
	Inherits@CAMPAIGNDISABLED: ^Disabled

# Misc

## Remove Nod logo from Legion Conyard

AFAC:
	RenderSprites:
		-FactionImages:

CHAN:
	-Wanders:
	Selectable:
		-Class:
	Mobile:
		Speed: 54
	Tooltip:
		Name: Nod Researcher
	-Valued:

MOEBIUS:
	Tooltip:
		Name: Nod Researcher
	Voiced:
		VoiceSet: CivilianMaleVoice
	-Valued:

HQ:
	SpawnActorPowerCA@sathack:
		LifeTime: 75
		ChargeInterval: 4500

HOSP:
	Inherits@HACKABLE: ^Hackable
	-GrantConditionOnPrerequisite@OwnedByAi:
	-PeriodicProducerCA@MEDIC:
	-PeriodicProducerCA@REJUVENATOR:
	-GrantConditionIfOwnerIsNeutral:
	-GrantConditionOnPrerequisite@SCRIN:
	TooltipExtras:
		Description: When controlled, heals nearby infantry.

# Hunt() requires only 1 AttackBase
BATF.AI:
	-AttackFrontal:
