Player:
	-ProvidesPrerequisiteOnKillCount@NodCovenants:

wrath.covenant:
	Inherits@CAMPAIGNDISABLED: ^Disabled

unity.covenant:
	Inherits@CAMPAIGNDISABLED: ^Disabled

zeal.covenant:
	Inherits@CAMPAIGNDISABLED: ^Disabled

covenant.upgrade:
	Inherits@CAMPAIGNDISABLED: ^Disabled

cyborgdmg.upgrade:
	Inherits@CAMPAIGNDISABLED: ^Disabled

cyborgprod.upgrade:
	Inherits@CAMPAIGNDISABLED: ^Disabled

cyborgspeed.upgrade:
	Buildable:
		Prerequisites: ~player.nod, tmpp, ~techlevel.high

^AssassinSquadPower:
	ProduceActorPowerCA@AssassinSquad:
		Prerequisites: ~disabled

^HackerCellPower:
	ProduceActorPowerCA@HackerCell:
		Prerequisites: ~disabled

^ConfessorCabalPower:
	ProduceActorPowerCA@ConfessorCabal:
		Prerequisites: ~disabled
