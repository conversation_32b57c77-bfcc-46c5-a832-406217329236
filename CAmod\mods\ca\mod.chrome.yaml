Chrome:
	ca|chrome.yaml

ChromeLayout:
	common|chrome/ingame.yaml
	common|chrome/ingame-chat.yaml
	ca|chrome/ingame-transients.yaml
	common|chrome/ingame-fmvplayer.yaml
	common|chrome/ingame-info.yaml
	common|chrome/ingame-infoscripterror.yaml
	common|chrome/ingame-infobriefing.yaml
	common|chrome/ingame-infoobjectives.yaml
	ca|chrome/ingame-infostats.yaml
	common|chrome/ingame-info-lobby-options.yaml
	ca|chrome/ingame-menu.yaml
	ca|chrome/ingame-observer.yaml
	ca|chrome/ingame-player.yaml
	common|chrome/ingame-perf.yaml
	common|chrome/ingame-debug.yaml
	common|chrome/ingame-debuginfo.yaml
	common|chrome/ingame-infochat.yaml
	ca|chrome/mainmenu.yaml
	common|chrome/settings.yaml
	ca|chrome/settings-display.yaml
	common|chrome/settings-audio.yaml
	common|chrome/settings-input.yaml
	common|chrome/settings-hotkeys.yaml
	common|chrome/settings-advanced.yaml
	common|chrome/credits.yaml
	common|chrome/lobby.yaml
	common|chrome/lobby-mappreview.yaml
	common|chrome/lobby-players.yaml
	ca|chrome/lobby-options.yaml
	common|chrome/lobby-music.yaml
	common|chrome/lobby-servers.yaml
	common|chrome/lobby-kickdialogs.yaml
	ca|chrome/color-picker.yaml
	common|chrome/mainmenu-prompts.yaml
	common|chrome/map-chooser.yaml
	common|chrome/multiplayer-browser.yaml
	common|chrome/multiplayer-browserpanels.yaml
	common|chrome/multiplayer-createserver.yaml
	common|chrome/multiplayer-directconnect.yaml
	common|chrome/connection.yaml
	common|chrome/replaybrowser.yaml
	common|chrome/gamesave-browser.yaml
	ca|chrome/gamesave-loading.yaml
	common|chrome/dropdowns.yaml
	common|chrome/musicplayer.yaml
	ca|chrome/tooltips.yaml
	common|chrome/assetbrowser.yaml
	ca|chrome/missionbrowser.yaml
	common|chrome/confirmation-dialogs.yaml
	common|chrome/editor.yaml
	common|chrome/playerprofile.yaml
	common|chrome/text-notifications.yaml
	ca|chrome/encyclopedia.yaml

ChromeMetrics:
	common|metrics.yaml
	ca|metrics.yaml

Fonts:
	Tiny:
		Font: ca|bombardreg.ttf
		Size: 12
		Ascender: 8
	TinyBold:
		Font: ca|bombardreg.ttf
		Size: 12
		Ascender: 8
	Small:
		Font: ca|bombardreg.ttf
		Size: 14
		Ascender: 9
	Regular:
		Font: ca|bombardreg.ttf
		Size: 16
		Ascender: 11
	Bold:
		Font: ca|bombard.ttf
		Size: 18
		Ascender: 11
	Medium:
		Font: ca|bombardreg.ttf
		Size: 20
		Ascender: 14
	MediumBold:
		Font: ca|bombard.ttf
		Size: 20
		Ascender: 14
	BigBold:
		Font: ca|bombard.ttf
		Size: 26
		Ascender: 18
	Title:
		Font: ca|bombard.ttf
		Size: 32
		Ascender: 26

Hotkeys:
	common|hotkeys/game.yaml
	common|hotkeys/observer.yaml
	common|hotkeys/production-common.yaml
	common|hotkeys/supportpowers.yaml
	common|hotkeys/viewport.yaml
	common|hotkeys/chat.yaml
	common|hotkeys/editor.yaml
	common|hotkeys/control-groups.yaml
	ca|hotkeys/ca.yaml

AllowUnusedFluentMessagesInExternalPackages: true
