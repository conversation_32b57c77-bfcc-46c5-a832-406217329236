tc04:
	Defaults:
		Filename: tc04.tem
		TilesetFilenames:
			SNOW: tc04.sno
			JUNGLE: tc04.jun
			WINTER: tc04.win
			BARREN: tc04.bar
	idle:

tc04.husk:
	Defaults:
		Filename: tc04.tem
		TilesetFilenames:
			SNOW: tc04.sno
			JUNGLE: tc04.jun
			WINTER: tc04.win
			BARREN: tc04.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc05:
	Defaults:
		Filename: tc05.tem
		TilesetFilenames:
			SNOW: tc05.sno
			JUNGLE: tc05.jun
			WINTER: tc05.win
			BARREN: tc05.bar
	idle:

tc05.husk:
	Defaults:
		Filename: tc05.tem
		TilesetFilenames:
			SNOW: tc05.sno
			JUNGLE: tc05.jun
			WINTER: tc05.win
			BARREN: tc05.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc03:
	Defaults:
		Filename: tc03.tem
		TilesetFilenames:
			SNOW: tc03.sno
			JUNGLE: tc03.jun
			WINTER: tc03.win
			BARREN: tc03.bar
	idle:

tc03.husk:
	Defaults:
		Filename: tc03.tem
		TilesetFilenames:
			SNOW: tc03.sno
			JUNGLE: tc03.jun
			WINTER: tc03.win
			BARREN: tc03.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc02:
	Defaults:
		Filename: tc02.tem
		TilesetFilenames:
			SNOW: tc02.sno
			JUNGLE: tc02.jun
			WINTER: tc02.win
			BARREN: tc02.bar
	idle:

tc02.husk:
	Defaults:
		Filename: tc02.tem
		TilesetFilenames:
			SNOW: tc02.sno
			JUNGLE: tc02.jun
			WINTER: tc02.win
			BARREN: tc02.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

tc01:
	Defaults:
		Filename: tc01.tem
		TilesetFilenames:
			SNOW: tc01.sno
			DESERT: tc01.des
			JUNGLE: tc01.jun
			WINTER: tc01.win
			BARREN: tc01.bar
	idle:

tc01.husk:
	Defaults:
		Filename: tc01.tem
		TilesetFilenames:
			SNOW: tc01.sno
			DESERT: tc01.des
			JUNGLE: tc01.jun
			WINTER: tc01.win
			BARREN: tc01.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t18:
	Defaults:
		Filename: t18.des
	idle:

t18.husk:
	Defaults:
		Filename: t18.des
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t17:
	Defaults:
		Filename: t17.tem
		TilesetFilenames:
			SNOW: t17.sno
			JUNGLE: t17.jun
			WINTER: t17.win
			BARREN: t17.bar
	idle:

t17.husk:
	Defaults:
		Filename: t17.tem
		TilesetFilenames:
			SNOW: t17.sno
			JUNGLE: t17.jun
			WINTER: t17.win
			BARREN: t17.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t16:
	Defaults:
		Filename: t16.tem
		TilesetFilenames:
			SNOW: t16.sno
			JUNGLE: t16.jun
			WINTER: t16.win
			BARREN: t16.bar
	idle:

t16.husk:
	Defaults:
		Filename: t16.tem
		TilesetFilenames:
			SNOW: t16.sno
			JUNGLE: t16.jun
			WINTER: t16.win
			BARREN: t16.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t15:
	Defaults:
		Filename: t15.tem
		TilesetFilenames:
			SNOW: t15.sno
			JUNGLE: t15.jun
			WINTER: t15.win
			BARREN: t15.bar
	idle:

t15.husk:
	Defaults:
		Filename: t15.tem
		TilesetFilenames:
			SNOW: t15.sno
			JUNGLE: t15.jun
			WINTER: t15.win
			BARREN: t15.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t14:
	Defaults:
		Filename: t14.tem
		TilesetFilenames:
			SNOW: t14.sno
			JUNGLE: t14.jun
			WINTER: t14.win
			BARREN: t14.bar
	idle:

t14.husk:
	Defaults:
		Filename: t14.tem
		TilesetFilenames:
			SNOW: t14.sno
			JUNGLE: t14.jun
			WINTER: t14.win
			BARREN: t14.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t13:
	Defaults:
		Filename: t13.tem
		TilesetFilenames:
			SNOW: t13.sno
			JUNGLE: t13.jun
			WINTER: t13.win
			BARREN: t13.bar
	idle:

t13.husk:
	Defaults:
		Filename: t13.tem
		TilesetFilenames:
			SNOW: t13.sno
			JUNGLE: t13.jun
			WINTER: t13.win
			BARREN: t13.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t12:
	Defaults:
		Filename: t12.tem
		TilesetFilenames:
			SNOW: t12.sno
			JUNGLE: t12.jun
			WINTER: t12.win
			BARREN: t12.bar
	idle:

t12.husk:
	Defaults:
		Filename: t12.tem
		TilesetFilenames:
			SNOW: t12.sno
			JUNGLE: t12.jun
			WINTER: t12.win
			BARREN: t12.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t11:
	Defaults:
		Filename: t11.tem
		TilesetFilenames:
			SNOW: t11.sno
			JUNGLE: t11.jun
			WINTER: t11.win
			BARREN: t11.bar
	idle:

t11.husk:
	Defaults:
		Filename: t11.tem
		TilesetFilenames:
			SNOW: t11.sno
			JUNGLE: t11.jun
			WINTER: t11.win
			BARREN: t11.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t10:
	Defaults:
		Filename: t10.tem
		TilesetFilenames:
			SNOW: t10.sno
			JUNGLE: t10.jun
			WINTER: t10.win
			BARREN: t10.bar
	idle:

t10.husk:
	Defaults:
		Filename: t10.tem
		TilesetFilenames:
			SNOW: t10.sno
			JUNGLE: t10.jun
			WINTER: t10.win
			BARREN: t10.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t09:
	Defaults:
		Filename: t09.des
	idle:

t09.husk:
	Defaults:
		Filename: t09.des
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t08:
	Defaults:
		Filename: t08.tem
		TilesetFilenames:
			SNOW: t08.sno
			DESERT: t08.des
			JUNGLE: t08.jun
			WINTER: t08.win
			BARREN: t08.bar
	idle:

t08.husk:
	Defaults:
		Filename: t08.tem
		TilesetFilenames:
			SNOW: t08.sno
			DESERT: t08.des
			JUNGLE: t08.jun
			WINTER: t08.win
			BARREN: t08.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t07:
	Defaults:
		Filename: t07.tem
		TilesetFilenames:
			SNOW: t07.sno
			JUNGLE: t07.jun
			WINTER: t07.win
			BARREN: t07.bar
	idle:

t07.husk:
	Defaults:
		Filename: t07.tem
		TilesetFilenames:
			SNOW: t07.sno
			JUNGLE: t07.jun
			WINTER: t07.win
			BARREN: t07.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t06:
	Defaults:
		Filename: t06.tem
		TilesetFilenames:
			SNOW: t06.sno
			JUNGLE: t06.jun
			WINTER: t06.win
			BARREN: t06.bar
	idle:

t06.husk:
	Defaults:
		Filename: t06.tem
		TilesetFilenames:
			SNOW: t06.sno
			JUNGLE: t06.jun
			WINTER: t06.win
			BARREN: t06.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t05:
	Defaults:
		Filename: t05.tem
		TilesetFilenames:
			SNOW: t05.sno
			JUNGLE: t05.jun
			WINTER: t05.win
			BARREN: t05.bar
	idle:

t05.husk:
	Defaults:
		Filename: t05.tem
		TilesetFilenames:
			SNOW: t05.sno
			JUNGLE: t05.jun
			WINTER: t05.win
			BARREN: t05.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t04:
	Defaults:
		Filename: t04.des
	idle:

t04.husk:
	Defaults:
		Filename: t04.des
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t03:
	Defaults:
		Filename: t03.tem
		TilesetFilenames:
			SNOW: t03.sno
			JUNGLE: t03.jun
			WINTER: t03.win
			BARREN: t03.bar
	idle:

t03.husk:
	Defaults:
		Filename: t03.tem
		TilesetFilenames:
			SNOW: t03.sno
			JUNGLE: t03.jun
			WINTER: t03.win
			BARREN: t03.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t02:
	Defaults:
		Filename: t02.tem
		TilesetFilenames:
			SNOW: t02.sno
			JUNGLE: t02.jun
			WINTER: t02.win
			BARREN: t02.bar
	idle:

t02.husk:
	Defaults:
		Filename: t02.tem
		TilesetFilenames:
			SNOW: t02.sno
			JUNGLE: t02.jun
			WINTER: t02.win
			BARREN: t02.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

t01:
	Defaults:
		Filename: t01.tem
		TilesetFilenames:
			SNOW: t01.sno
			JUNGLE: t01.jun
			WINTER: t01.win
			BARREN: t01.bar
	idle:

t01.husk:
	Defaults:
		Filename: t01.tem
		TilesetFilenames:
			SNOW: t01.sno
			JUNGLE: t01.jun
			WINTER: t01.win
			BARREN: t01.bar
	idle:
		Start: 1
	dead:
		Start: 2
		Length: 8
		Tick: 80

ice01:
	Defaults:
		Filename: ice01.tem
		TilesetFilenames:
			SNOW: ice01.sno
			JUNGLE: ice01.jun
			WINTER: ice01.win
			BARREN: ice01.bar
	idle:
		Length: *

ice02:
	Defaults:
		Filename: ice02.tem
		TilesetFilenames:
			SNOW: ice02.sno
			JUNGLE: ice02.jun
			WINTER: ice02.win
			BARREN: ice02.bar
	idle:
		Length: *

ice03:
	Defaults:
		Filename: ice03.tem
		TilesetFilenames:
			SNOW: ice03.sno
			JUNGLE: ice03.jun
			WINTER: ice03.win
			BARREN: ice03.bar
	idle:
		Length: *

ice04:
	Defaults:
		Filename: ice04.tem
		TilesetFilenames:
			SNOW: ice04.sno
			JUNGLE: ice04.jun
			WINTER: ice04.win
			BARREN: ice04.bar
	idle:
		Length: *

ice05:
	Defaults:
		Filename: ice05.tem
		TilesetFilenames:
			SNOW: ice05.sno
			JUNGLE: ice05.jun
			WINTER: ice05.win
			BARREN: ice05.bar
	idle:
		Length: *

v01:
	Defaults:
		Filename: v01.tem
		TilesetFilenames:
			SNOW: v01.sno
			JUNGLE: v01.jun
			WINTER: v01.win
			BARREN: v01.bar
	idle:
	damaged-idle:
		Start: 1

v02:
	Defaults:
		Filename: v02.tem
		TilesetFilenames:
			SNOW: v02.sno
			JUNGLE: v02.jun
			WINTER: v02.win
			BARREN: v02.bar
	idle:
	damaged-idle:
		Start: 1

v03:
	Defaults:
		Filename: v03.tem
		TilesetFilenames:
			SNOW: v03.sno
			JUNGLE: v03.jun
			WINTER: v03.win
			BARREN: v03.bar
	idle:
	damaged-idle:
		Start: 1

v04:
	Defaults:
		Filename: v04.tem
		TilesetFilenames:
			SNOW: v04.sno
			JUNGLE: v04.jun
			WINTER: v04.win
			BARREN: v04.bar
	idle:
	damaged-idle:
		Start: 1

v05:
	Defaults:
		Filename: v05.tem
		TilesetFilenames:
			SNOW: v05.sno
			JUNGLE: v05.jun
			WINTER: v05.win
			BARREN: v05.bar
	idle:
	damaged-idle:
		Start: 1

v06:
	Defaults:
		Filename: v06.tem
		TilesetFilenames:
			SNOW: v06.sno
			JUNGLE: v06.jun
			WINTER: v06.win
			BARREN: v06.bar
	idle:
	damaged-idle:
		Start: 1

v07:
	Defaults:
		Filename: v07.tem
		TilesetFilenames:
			SNOW: v07.sno
			JUNGLE: v07.jun
			WINTER: v07.win
			BARREN: v07.bar
	idle:
	damaged-idle:
		Start: 1

v08:
	Defaults:
		TilesetFilenames:
			SNOW: v08.sno
			INTERIOR: v08.int
			TEMPERAT: v08.tem
			DESERT: v08.des
			JUNGLE: v08.jun
			WINTER: v08.win
			BARREN: v08.bar
	idle:
	damaged-idle:
		Start: 1

v09:
	Defaults:
		TilesetFilenames:
			SNOW: v09.sno
			INTERIOR: v09.int
			TEMPERAT: v09.tem
			DESERT: v09.des
			JUNGLE: v09.jun
			WINTER: v09.win
			BARREN: v09.bar
	idle:
	damaged-idle:
		Start: 1

v10:
	Defaults:
		TilesetFilenames:
			SNOW: v10.sno
			INTERIOR: v10.int
			TEMPERAT: v10.tem
			DESERT: v10.des
			JUNGLE: v10.jun
			WINTER: v10.win
			BARREN: v10.bar
	idle:
	damaged-idle:
		Start: 1

v11:
	Defaults:
		TilesetFilenames:
			SNOW: v11.sno
			INTERIOR: v11.int
			TEMPERAT: v11.tem
			DESERT: v11.des
			JUNGLE: v11.jun
			WINTER: v11.win
			BARREN: v11.bar
	idle:
	damaged-idle:
		Start: 1

v12:
	Defaults:
		TilesetFilenames:
			SNOW: v12.sno
			INTERIOR: v12.int
			TEMPERAT: v12.tem
			DESERT: v12.des
			JUNGLE: v12.jun
			WINTER: v12.win
			BARREN: v12.bar
	idle:
	damaged-idle:
		Start: 1

v13:
	Defaults:
		TilesetFilenames:
			SNOW: v13.sno
			INTERIOR: v13.int
			TEMPERAT: v13.tem
			DESERT: v13.des
			JUNGLE: v13.jun
			WINTER: v13.win
			BARREN: v13.bar
	idle:
	damaged-idle:
		Start: 1

v14:
	Defaults:
		TilesetFilenames:
			SNOW: v14.sno
			INTERIOR: v14.int
			TEMPERAT: v14.tem
			DESERT: v14.des
			JUNGLE: v14.jun
			WINTER: v14.win
			BARREN: v14.bar
	idle:
		ZOffset: -512
	damaged-idle:
		Start: 1
		ZOffset: -512

v15:
	Defaults:
		TilesetFilenames:
			SNOW: v15.sno
			INTERIOR: v15.int
			TEMPERAT: v15.tem
			DESERT: v15.des
			JUNGLE: v15.jun
			WINTER: v15.win
			BARREN: v15.bar
	idle:
		ZOffset: -512
	damaged-idle:
		Start: 1
		ZOffset: -512

v16:
	Defaults:
		TilesetFilenames:
			SNOW: v16.sno
			INTERIOR: v16.int
			TEMPERAT: v16.tem
			DESERT: v16.des
			JUNGLE: v16.jun
			WINTER: v16.win
			BARREN: v16.bar
	idle:
		ZOffset: -512
	damaged-idle:
		Start: 1
		ZOffset: -512

v17:
	Defaults:
		TilesetFilenames:
			SNOW: v17.sno
			INTERIOR: v17.int
			TEMPERAT: v17.tem
			DESERT: v17.des
			JUNGLE: v17.jun
			WINTER: v17.win
			BARREN: v17.bar
	idle:
		ZOffset: -512
	damaged-idle:
		Start: 1
		ZOffset: -512

v18:
	Defaults:
		TilesetFilenames:
			SNOW: v18.sno
			INTERIOR: v18.int
			TEMPERAT: v18.tem
			DESERT: v18.des
			JUNGLE: v18.jun
			WINTER: v18.win
			BARREN: v18.bar
	idle:
		ZOffset: -512
	damaged-idle:
		Start: 1
		ZOffset: -512

v19:
	idle:
		Filename: v19.shp
		Length: 14

v19.husk:
	idle:
		Filename: v19.shp
		Start: 28
	fire-start:
		Filename: flmspt.shp
		Length: *
		Offset: 7,-15
		ZOffset: 1
	fire-loop:
		Filename: flmspt.shp
		Start: 50
		Length: *
		Offset: 7,-15
		ZOffset: 1

v20:
	idle:
		Filename: v20.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v20.des
		Start: 3
		Length: 3
		Tick: 120

v21:
	idle:
		Filename: v21.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v21.des
		Start: 3
		Length: 3
		Tick: 120

v22:
	idle:
		Filename: v22.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v22.des
		Start: 3
		Length: 3
		Tick: 120

v23:
	idle:
		Filename: v23.des
		Length: 3
		Tick: 120
	damaged-idle:
		Filename: v23.des
		Start: 3
		Length: 3
		Tick: 120

v24:
	idle:
		Filename: v24.des
	damaged-idle:
		Filename: v24.des
		Start: 1

v25:
	idle:
		Filename: v25.des
	damaged-idle:
		Filename: v25.des
		Start: 1

v26:
	idle:
		Filename: v26.des
	damaged-idle:
		Filename: v26.des
		Start: 1

v27:
	idle:
		Filename: v27.des
	damaged-idle:
		Filename: v27.des
		Start: 1

v28:
	idle:
		Filename: v28.des
	damaged-idle:
		Filename: v28.des
		Start: 1

v29:
	idle:
		Filename: v29.des
	damaged-idle:
		Filename: v29.des
		Start: 1

v30:
	idle:
		Filename: v30.des
	damaged-idle:
		Filename: v30.des
		Start: 2

v31:
	idle:
		Filename: v31.des
	damaged-idle:
		Filename: v31.des
		Start: 1

v32:
	idle:
		Filename: v32.des
	damaged-idle:
		Filename: v32.des
		Start: 1

v33:
	idle:
		Filename: v33.des
	damaged-idle:
		Filename: v33.des
		Start: 1

v34:
	idle:
		Filename: v34.des
	damaged-idle:
		Filename: v34.des
		Start: 1

v35:
	idle:
		Filename: v35.des
	damaged-idle:
		Filename: v35.des
		Start: 1

v36:
	idle:
		Filename: v36.des
	damaged-idle:
		Filename: v36.des
		Start: 1

v37:
	idle:
		Filename: v37.des
	damaged-idle:
		Filename: v37.des
		Start: 1

rice:
	idle:
		Filename: rice.tem
		ZOffset: -512
	damaged-idle:
		Filename: rice.tem
		Start: 1
		ZOffset: -512

utilpol1:
	idle:
		Filename: utilpol1.shp
	damaged-idle:
		Filename: utilpol1.shp
		Start: 1
	dead:
		Filename: utilpol1.shp
		Start: 1

utilpol2:
	idle:
		Filename: utilpol2.shp
	damaged-idle:
		Filename: utilpol2.shp
		Start: 1
	dead:
		Filename: utilpol2.shp
		Start: 1

ammobox1:
	idle:
		Filename: ammobox1.shp

ammobox2:
	idle:
		Filename: ammobox2.shp

ammobox3:
	idle:
		Filename: ammobox3.shp

tanktrap1:
	idle:
		Filename: tanktrap1.shp

tanktrap2:
	idle:
		Filename: tanktrap2.shp

rushouse:
	idle:
		Filename: rushouse.shp
	damaged-idle:
		Filename: rushouse.shp
		Start: 1

rushouse2:
	idle:
		Filename: rushouse2.shp
	damaged-idle:
		Filename: rushouse2.shp
		Start: 1

rushouse3:
	idle:
		Filename: rushouse3.shp
	damaged-idle:
		Filename: rushouse3.shp
		Start: 1

rushouse4:
	idle:
		Filename: rushouse4.shp
	damaged-idle:
		Filename: rushouse4.shp
		Start: 1

asianhut:
	idle:
		Filename: asianhut.shp
	damaged-idle:
		Filename: asianhut.shp
		Start: 1

barb:
	idle:
		Filename: barb.shp
		Length: 16
	damaged-idle:
		Filename: barb.shp
		Start: 16
		Length: 16

wood:
	idle:
		Filename: wood.shp
		Length: 16
	damaged-idle:
		Filename: wood.shp
		Start: 16
		Length: 16

barl:
	idle:
		Filename: barl.shp

brl3:
	idle:
		Filename: brl3.shp

boxes01:
	idle:
		Filename: boxes01.int

boxes02:
	idle:
		Filename: boxes02.int

boxes03:
	idle:
		Filename: boxes03.int

boxes04:
	idle:
		Filename: boxes04.int

boxes05:
	idle:
		Filename: boxes05.int

boxes06:
	idle:
		Filename: boxes06.int

boxes07:
	idle:
		Filename: boxes07.int

boxes08:
	idle:
		Filename: boxes08.int

boxes09:
	idle:
		Filename: boxes09.int

rock1:
	idle:
		Filename: rock1.tem
		TilesetFilenames:
			DESERT: rock1.des

rock2:
	idle:
		Filename: rock2.tem
		TilesetFilenames:
			DESERT: rock2.des

rock3:
	idle:
		Filename: rock3.tem
		TilesetFilenames:
			DESERT: rock3.des

rock4:
	idle:
		Filename: rock4.tem
		TilesetFilenames:
			DESERT: rock4.des

rock5:
	idle:
		Filename: rock5.tem
		TilesetFilenames:
			DESERT: rock5.des

rock6:
	idle:
		Filename: rock6.tem
		TilesetFilenames:
			DESERT: rock6.des

rock7:
	idle:
		Filename: rock7.tem
		TilesetFilenames:
			DESERT: rock7.des

snowhut:
	idle:
		Filename: snowhut.shp
		Length: 3
		Tick: 360
	damaged-idle:
		Filename: snowhut.shp
		Start: 3
		Tick: 120

lhus:
	Defaults:
		Offset: 0,-16
	idle:
		Filename: lhus.shp
		Length: 16
		Tick: 180
	damaged-idle:
		Filename: lhus.shp
		Start: 16
		Tick: 180
		Length: 8

windmill:
	Defaults:
		Offset: 0,-16
	idle:
		Filename: windmill.shp
		Length: 8
		Tick: 80
	damaged-idle:
		Filename: windmill.shp
		Start: 8
		Length: 8
		Tick: 80

v01.husk:
	idle:
		Filename: v01.tem
		TilesetFilenames:
			SNOW: v01.sno
			JUNGLE: v01.jun
			WINTER: v01.win
			BARREN: v01.bar

v02.husk:
	idle:
		Filename: v02.tem
		TilesetFilenames:
			SNOW: v02.sno
			JUNGLE: v02.jun
			WINTER: v02.win
			BARREN: v02.bar

v03.husk:
	idle:
		Filename: v03.tem
		TilesetFilenames:
			SNOW: v03.sno
			JUNGLE: v03.jun
			WINTER: v03.win
			BARREN: v03.bar

v04.husk:
	idle:
		Filename: v04.tem
		TilesetFilenames:
			SNOW: v04.sno
			JUNGLE: v04.jun
			WINTER: v04.win
			BARREN: v04.bar

v05.husk:
	idle:
		Filename: v05.tem
		TilesetFilenames:
			SNOW: v05.sno
			JUNGLE: v05.jun
			WINTER: v05.win
			BARREN: v05.bar

v06.husk:
	idle:
		Filename: v06.tem
		TilesetFilenames:
			SNOW: v06.sno
			JUNGLE: v06.jun
			WINTER: v06.win
			BARREN: v06.bar

v07.husk:
	idle:
		Filename: v07.tem
		TilesetFilenames:
			SNOW: v07.sno
			JUNGLE: v07.jun
			WINTER: v07.win
			BARREN: v07.bar

v08.husk:
	idle:
		Filename: v08.tem
		TilesetFilenames:
			SNOW: v08.sno
			JUNGLE: v08.jun
			WINTER: v08.win
			BARREN: v08.bar

v09.husk:
	idle:
		Filename: v09.tem
		TilesetFilenames:
			SNOW: v09.sno
			JUNGLE: v09.jun
			WINTER: v09.win
			BARREN: v09.bar

v10.husk:
	idle:
		Filename: v10.tem
		TilesetFilenames:
			SNOW: v10.sno
			JUNGLE: v10.jun
			WINTER: v10.win
			BARREN: v10.bar

v11.husk:
	idle:
		Filename: v11.tem
		TilesetFilenames:
			SNOW: v11.sno
			JUNGLE: v11.jun
			WINTER: v11.win
			BARREN: v11.bar

v12.husk:
	idle:
		Filename: v12.tem
		TilesetFilenames:
			SNOW: v12.sno
			JUNGLE: v12.jun
			WINTER: v12.win
			BARREN: v12.bar
		ZOffset: -512

v13.husk:
	idle:
		Filename: v13.tem
		TilesetFilenames:
			SNOW: v13.sno
			JUNGLE: v13.jun
			WINTER: v13.win
			BARREN: v13.bar
		ZOffset: -512

v14.husk:
	idle:
		Filename: v14.tem
		TilesetFilenames:
			SNOW: v14.sno
			JUNGLE: v14.jun
			WINTER: v14.win
			BARREN: v14.bar
		ZOffset: -512

v15.husk:
	idle:
		Filename: v15.tem
		TilesetFilenames:
			SNOW: v15.sno
			JUNGLE: v15.jun
			WINTER: v15.win
			BARREN: v15.bar
		ZOffset: -512

v16.husk:
	idle:
		Filename: v16.tem
		TilesetFilenames:
			SNOW: v16.sno
			JUNGLE: v16.jun
			WINTER: v16.win
			BARREN: v16.bar
		ZOffset: -512

v17.husk:
	idle:
		Filename: v17.tem
		TilesetFilenames:
			SNOW: v17.sno
			JUNGLE: v17.jun
			WINTER: v17.win
			BARREN: v17.bar
		ZOffset: -512

v18.husk:
	idle:
		Filename: v18.tem
		TilesetFilenames:
			SNOW: v18.sno
			JUNGLE: v18.jun
			WINTER: v18.win
			BARREN: v18.bar
		ZOffset: -512

arco:
	idle:
		Filename: arco.shp
	damaged-idle:
		Filename: arco.shp
		Start: 1

arco.husk:
	idle:
		Filename: arco.shp
		Start: 1

scrinflora1:
	Defaults:
		Filename: scrinflora1.shp
	idle:
		Offset: 2, -10

scrinflora2:
	Defaults:
		Filename: scrinflora2.shp
	idle:
		Offset: -5, -12

scrinflora3:
	Defaults:
		Filename: scrinflora3.shp
	idle:
		Offset: 2, -12

scrinflora4:
	Defaults:
		Filename: scrinflora4.shp
	idle:
		Offset: 3, -11

scrinflora5:
	Defaults:
		Filename: scrinflora5.shp
	idle:
		Offset: 4, -11

scrinflora6:
	Defaults:
		Filename: scrinflora6.shp
	idle:
		Offset: 2, -15

scrinflora7:
	Defaults:
		Filename: scrinflora7.shp
	idle:
		Offset: -5, -15

scrinflora8:
	Defaults:
		Filename: scrinflora8.shp
	idle:
		Offset: 2, -12

scrinflora9:
	Defaults:
		Filename: scrinflora9.shp
	idle:
		Offset: 0, 0

scrinflora10:
	Defaults:
		Filename: scrinflora10.shp
	idle:
		Offset: 0, 0

scrinflora11:
	Defaults:
		Filename: scrinflora11.shp
	idle:
		Offset: 0, 0

scrinflora12:
	Defaults:
		Filename: scrinflora12.shp
	idle:
		Offset: 0, -4

scrinflora13:
	Defaults:
		Filename: scrinflora13.shp
	idle:
		Offset: 0, -4

scrinflora14:
	Defaults:
		Filename: scrinflora14.shp
	idle:
		Offset: 2, -4
