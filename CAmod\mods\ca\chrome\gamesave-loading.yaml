Container@GAMESAVE_LOADING_SCREEN:
	Logic: GameSaveLoadingLogic
	Width: WINDOW_WIDTH
	Height: WINDOW_HEIGHT
	Children:
		LogicKeyListener@CANCEL_HANDLER:
		Image@LOGO:
			X: (WINDOW_WIDTH - 1024) / 2
			Y: (WINDOW_HEIGHT - 256) / 2
			ImageCollection: loading-artwork
			ImageName: logo
		Label@TITLE:
			Width: WINDOW_WIDTH
			Y: 3 * WINDOW_HEIGHT / 4 - 29
			Height: 25
			Font: Bold
			Align: Center
			Text: label-gamesave-loading-screen-title
		ProgressBar@PROGRESS:
			X: (WINDOW_WIDTH - 500) / 2
			Y: 3 * WINDOW_HEIGHT / 4
			Width: 500
			Height: 20
			Background: observer-scrollpanel-button-pressed
			Bar: observer-scrollpanel-button
		Label@DESC:
			Width: WINDOW_WIDTH
			Y: 3 * WINDOW_HEIGHT / 4 + 19
			Height: 25
			Font: Regular
			Align: Center
			Text: label-gamesave-loading-screen-desc
		Label@DESC2:
			Width: WINDOW_WIDTH
			Y: 3 * WINDOW_HEIGHT / 4 + 19 + 30
			Height: 25
			Font: Small
			Align: Center
			Text: label-gamesave-loading-screen-loadtime-line1
			TextColor: 777777
		Label@DESC3:
			Width: WINDOW_WIDTH
			Y: 3 * WINDOW_HEIGHT / 4 + 19 + 48
			Height: 25
			Font: Small
			Align: Center
			Text: label-gamesave-loading-screen-loadtime-line2
			TextColor: 777777
