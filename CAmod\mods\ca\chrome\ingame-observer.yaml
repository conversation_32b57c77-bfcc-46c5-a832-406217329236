Container@OBSERVER_WIDGETS:
	Logic: MenuButtonsChromeLogic, LoadIngameChatLogic
	Children:
		Background@SELECTION_TOOLTIP:
			Logic: SelectionTooltipLogic
			Background: dialog4
			X: WINDOW_WIDTH
			Y: WINDOW_HEIGHT
			Width: 200
			Height: 65
			Children:
				Label@NAME:
					X: 7
					Y: 3
					Height: 23
					Font: Bold
				Label@DESC:
					X: 7
					Y: 27
					Height: 2
					Font: TinyBold
					VAlign: Top
				Label@STRENGTHS:
					X: 7
					Y: 29
					Height: 2
					Font: TinyBold
					VAlign: Top
					TextColor: 33DD33
				Label@WEAKNESSES:
					X: 7
					Y: 30
					Height: 2
					Font: TinyBold
					VAlign: Top
					TextColor: EE5555
				Label@ATTRIBUTES:
					X: 7
					Y: 31
					Height: 2
					Font: TinyBold
					VAlign: Top
					TextColor: FFFF00
				Image@ARMORTYPE_ICON:
					X: 3
					Y: 7
					Width: 16
					Height: 16
					ImageCollection: sidebar-bits
					ImageName: production-tooltip-armor
				Label@ARMORTYPE:
					Y: 3
					Height: 16
					Font: Bold
				Image@COST_ICON:
					Y: 26
					Width: 16
					Height: 16
					ImageCollection: sidebar-bits
					ImageName: production-tooltip-cost
				Label@COST:
					Y: 22
					Height: 23
					Font: Bold
		Container@CHAT_ROOT:
		Container@MUTE_INDICATOR:
			Logic: MuteIndicatorLogic
			X: WINDOW_WIDTH - WIDTH - 260
			Y: 10
			Width: 200
			Height: 25
			Children:
				Image@ICON:
					X: PARENT_WIDTH - WIDTH
					Y: 1
					Width: 24
					Height: 24
					ImageCollection: sidebar-bits
					ImageName: indicator-muted
				Label@LABEL:
					Width: PARENT_WIDTH - 30
					Height: 25
					Align: Right
					Text: label-mute-indicator
					Contrast: true
		LogicKeyListener@OBSERVER_KEY_LISTENER:
		MenuButton@OPTIONS_BUTTON:
			X: 5
			Y: 5
			Width: 160
			Height: 25
			Text: Options (Esc)
			Font: Bold
			Key: escape
			DisableWorldSounds: true
		Container@GAME_TIMER_BLOCK:
			Logic: GameTimerLogic
			X: (WINDOW_WIDTH - WIDTH) / 2
			Width: 100
			Height: 55
			Children:
				LabelWithTooltip@GAME_TIMER:
					Width: PARENT_WIDTH
					Height: 30
					Align: Center
					Font: Title
					Contrast: true
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: SIMPLE_TOOLTIP
				Label@GAME_TIMER_STATUS:
					Y: 32
					Width: PARENT_WIDTH
					Height: 15
					Align: Center
					Font: Bold
					Contrast: true
		Background@RADAR_BG:
			X: WINDOW_WIDTH - 255
			Y: 5
			Width: 250
			Height: 250
			Children:
				Radar@INGAME_RADAR:
					X: 10
					Y: 10
					Width: PARENT_WIDTH - 19
					Height: PARENT_HEIGHT - 19
					WorldInteractionController: INTERACTION_CONTROLLER
				VideoPlayer@PLAYER:
					X: 10
					Y: 10
					Width: PARENT_WIDTH - 20
					Height: PARENT_HEIGHT - 20
					Skippable: false
		Background@OBSERVER_CONTROL_BG:
			X: WINDOW_WIDTH - 255
			Y: 260
			Width: 250
			Height: 55
			Children:
				DropDownButton@SHROUD_SELECTOR:
					Logic: ObserverShroudSelectorLogic
						CombinedViewKey: ObserverCombinedView
						WorldViewKey: ObserverWorldView
					X: 15
					Y: 15
					Width: 220
					Height: 25
					Font: Bold
					Children:
						LogicKeyListener@SHROUD_KEYHANDLER:
						Image@FLAG:
							Width: 23
							Height: 23
							X: 4
							Y: 2
						Label@LABEL:
							X: 34
							Width: PARENT_WIDTH
							Height: 25
							Shadow: True
						Label@NOFLAG_LABEL:
							X: 5
							Width: PARENT_WIDTH
							Height: 25
							Shadow: True
				Container@REPLAY_PLAYER:
					Logic: ReplayControlBarLogicCA
					Y: 39
					Width: 160
					Height: 35
					Visible: false
					Children:
						Button@BUTTON_PAUSE:
							X: 15
							Y: 10
							Width: 26
							Height: 26
							Key: Pause
							TooltipText: Pause
							TooltipContainer: TOOLTIP_CONTAINER
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_PAUSE:
									X: 5
									Y: 5
									ImageCollection: music
									ImageName: pause
						Button@BUTTON_PLAY:
							X: 15
							Y: 10
							Width: 26
							Height: 26
							Key: Pause
							IgnoreChildMouseOver: true
							TooltipText: Play
							TooltipContainer: TOOLTIP_CONTAINER
							Children:
								Image@IMAGE_PLAY:
									X: 5
									Y: 5
									ImageCollection: music
									ImageName: play
						Button@BUTTON_SLOW:
							X: 52
							Y: 13
							Width: 34
							Height: 20
							Key: ReplaySpeedSlow
							TooltipText: 50% speed
							TooltipContainer: TOOLTIP_CONTAINER
							Text: 0.5x
							Font: TinyBold
						Button@BUTTON_REGULAR:
							X: 52 + 36
							Y: 13
							Width: 34
							Height: 20
							Key: ReplaySpeedRegular
							TooltipText: 100% speed
							TooltipContainer: TOOLTIP_CONTAINER
							Text: 1x
							Font: TinyBold
						Button@BUTTON_FAST:
							X: 52 + 36 * 2
							Y: 13
							Width: 34
							Height: 20
							Key: ReplaySpeedFast
							TooltipText: 133% speed
							TooltipContainer: TOOLTIP_CONTAINER
							VisualHeight: 0
							Text: 1.33x
							Font: TinyBold
						Button@BUTTON_FASTER:
							X: 52 + 36 * 3
							Y: 13
							Width: 34
							Height: 20
							TooltipText: 200% speed
							TooltipContainer: TOOLTIP_CONTAINER
							VisualHeight: 0
							Text: 2x
							Font: TinyBold
						Button@BUTTON_MAXIMUM:
							X: 52 + 36 * 4
							Y: 13
							Width: 38
							Height: 20
							Key: ReplaySpeedMax
							TooltipText: Maximum speed
							TooltipContainer: TOOLTIP_CONTAINER
							Text: MAX
							Font: TinyBold
				ObserverArmyValues:
					X: WINDOW_WIDTH - WIDTH - 10
					Y: 320
					Width: 245
					ReplayYPosModifier: 35
		Container@INGAME_OBSERVERSTATS_BG:
			Logic: ObserverStatsLogicCA
				StatisticsNoneKey: StatisticsNone
				StatisticsBasicKey: StatisticsBasic
				StatisticsEconomyKey: StatisticsEconomy
				StatisticsProductionKey: StatisticsProduction
				StatisticsSupportPowersKey: StatisticsSupportPowers
				StatisticsCombatKey: StatisticsCombat
				StatisticsArmyKey: StatisticsArmy
				StatisticsGraphKey: StatisticsGraph
				StatisticsArmyGraphKey: StatisticsArmyGraph
			X: 5
			Y: 5
			Width: 760
			Height: 250
			Children:
				DropDownButton@STATS_DROPDOWN:
					X: 165
					Y: 0
					Width: 185
					Height: 25
					Font: Bold
					Children:
						LogicKeyListener@STATS_DROPDOWN_KEYHANDLER:
				Container@GRAPH_BG:
					Y: 30
					X: 0
					Width: PARENT_WIDTH
					Height: 25
					Children:
						Container@BASIC_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 700
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@CASH_HEADER:
									X: 155
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Cash
									Align: Right
									Shadow: True
								Label@POWER_HEADER:
									X: 235
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Power
									Align: Center
									Shadow: True
								Label@KILLS_HEADER:
									X: 315
									Y: 0
									Width: 40
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Kills
									Align: Right
									Shadow: True
								Label@DEATHS_HEADER:
									X: 355
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Deaths
									Align: Right
									Shadow: True
								Label@ASSETS_DESTROYED_HEADER:
									X: 415
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Destroyed
									Align: Right
									Shadow: True
								Label@ASSETS_LOST_HEADER:
									X: 495
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Lost
									Align: Right
									Shadow: True
								Label@EXPERIENCE_HEADER:
									X: 575
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Score
									Align: Right
									Shadow: True
								Label@ACTIONS_MIN_HEADER:
									X: 635
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: APM
									Align: Right
									Shadow: True
						Container@ECONOMY_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 700
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 35
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Shadow: True
								Label@CASH_HEADER:
									X: 155
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Cash
									Align: Right
									Shadow: True
								Label@INCOME_HEADER:
									X: 235
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Income
									Align: Right
									Shadow: True
								Label@ASSETS_HEADER:
									X: 315
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Assets
									Align: Right
									Shadow: True
								Label@EARNED_HEADER:
									X: 395
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Earned
									Align: Right
									Shadow: True
								Label@SPENT_HEADER:
									X: 475
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Spent
									Align: Right
									Shadow: True
								Label@HARVESTERS_HEADER:
									X: 545
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Harvs
									Align: Right
									Shadow: True
								Label@BOUNTY_HEADER:
									X: 635
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Bounty
									Align: Right
									Shadow: True
						Container@PRODUCTION_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@PRODUCTION_HEADER:
									X: 155
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Production
									Shadow: True
						Container@SUPPORT_POWERS_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@SUPPORT_POWERS_HEADER:
									X: 155
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Support Powers
									Shadow: True
						Container@ARMY_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@ARMY_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Army
									Shadow: True
						Container@UPGRADES_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@UPGRADES_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Upgrades
									Shadow: True
						Container@BUILD_ORDER_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@BUILD_ORDER_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Initial Build Order
									Shadow: True
						Container@UNITS_PRODUCED_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@UNITS_PRODUCED_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Units Produced
									Shadow: True
						Container@COMBAT_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 780
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Player
									Align: Left
									Shadow: True
								Label@ASSETS_DESTROYED_HEADER:
									X: 155
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Destroyed
									Align: Right
									Shadow: True
								Label@ASSETS_LOST_HEADER:
									X: 230
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Lost
									Align: Right
									Shadow: True
								Label@UNITS_KILLED_HEADER:
									X: 305
									Y: 0
									Width: 90
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Units Killed
									Align: Right
									Shadow: True
								Label@UNITS_DEAD_HEADER:
									X: 395
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Units Lost
									Align: Right
									Shadow: True
								Label@BUILDINGS_KILLED_HEADER:
									X: 475
									Y: 0
									Width: 85
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Bldg Killed
									Align: Right
									Shadow: True
								Label@BUILDINGS_DEAD_HEADER:
									X: 560
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Bldg Lost
									Align: Right
									Shadow: True
								Label@ARMY_VALUE_HEADER:
									X: 640
									Y: 0
									Width: 90
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Army Value
									Align: Right
									Shadow: True
								Label@VISION_HEADER:
									X: 720
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: Vision
									Align: Right
									Shadow: True
				ScrollPanel@PLAYER_STATS_PANEL:
					X: 0
					Y: 55
					Width: PARENT_WIDTH
					Height: 250
					TopBottomSpacing: 0
					BorderWidth: 0
					Background:
					ScrollbarWidth: 25
					ScrollBar: Hidden
					Children:
						ScrollItem@TEAM_TEMPLATE:
							X: 0
							Y: 0
							Width: 650 #PARENT_RIGHT - 35
							Height: 25
							Children:
								ColorBlock@TEAM_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@TEAM_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@TEAM:
									X: 10
									Y: 0
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
						ScrollItem@BASIC_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 700
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 2
									Y: 2
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								Label@CASH:
									X: 155
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@POWER:
									X: 235
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Center
									Shadow: True
								Label@KILLS:
									X: 315
									Y: 0
									Width: 40
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@DEATHS:
									X: 355
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS_DESTROYED:
									X: 415
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS_LOST:
									X: 495
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@EXPERIENCE:
									X: 575
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ACTIONS_MIN:
									X: 635
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
						ScrollItem@ECONOMY_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 700
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 2
									Y: 2
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								Label@CASH:
									X: 155
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@INCOME:
									X: 235
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS:
									X: 315
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@EARNED:
									X: 395
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@SPENT:
									X: 475
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@HARVESTERS:
									X: 545
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@BOUNTY:
									X: 635
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
						ScrollItem@PRODUCTION_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 2
									Y: 2
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverProductionIcons@PRODUCTION_ICONS:
									X: 155
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@SUPPORT_POWERS_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 2
									Y: 2
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverSupportPowerIcons@SUPPORT_POWER_ICONS:
									X: 155
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@ARMY_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverArmyIcons@ARMY_ICONS:
									X: 155
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
									TooltipTemplate: ARMY_TOOLTIP
						ScrollItem@UPGRADES_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverUpgradeIcons@UPGRADES_ICONS:
									X: 155
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
									TooltipTemplate: ARMY_TOOLTIP
						ScrollItem@BUILD_ORDER_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverBuildOrderIcons@BUILD_ORDER_ICONS:
									X: 155
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
									TooltipTemplate: ARMY_TOOLTIP
						ScrollItem@UNITS_PRODUCED_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverUnitsProducedIcons@UNITS_PRODUCED_ICONS:
									X: 155
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
									TooltipTemplate: ARMY_VALUE_TOOLTIP
						ScrollItem@COMBAT_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 780
							Height: 25
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 2
									Y: 2
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 35
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								Label@ASSETS_DESTROYED:
									X: 155
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS_LOST:
									X: 230
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@UNITS_KILLED:
									X: 305
									Y: 0
									Width: 90
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@UNITS_DEAD:
									X: 395
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@BUILDINGS_KILLED:
									X: 475
									Y: 0
									Width: 85
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@BUILDINGS_DEAD:
									X: 560
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ARMY_VALUE:
									X: 640
									Y: 0
									Width: 90
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@VISION:
									X: 720
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
				Container@INCOME_GRAPH_CONTAINER:
					X: 0
					Y: 30
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Visible: False
					Children:
						ColorBlock@GRAPH_BACKGROUND:
							X: 0
							Y: 0
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Color: 00000090
						ScrollableLineGraph@INCOME_GRAPH:
							X: 0
							Y: 0
							Width: PARENT_WIDTH - 5
							Height: PARENT_HEIGHT
							ValueFormat: ${0}
							YAxisValueFormat: ${0:F0}
							XAxisSize: 40
							XAxisTicksPerLabel: 2
							XAxisLabel: Game Minute
							YAxisLabel: Earnings
							LabelFont: TinyBold
							AxisFont: TinyBold
				Container@ARMY_VALUE_GRAPH_CONTAINER:
					X: 0
					Y: 30
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Visible: False
					Children:
						ColorBlock@GRAPH_BACKGROUND:
							X: 0
							Y: 0
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Color: 00000090
						ScrollableLineGraph@ARMY_VALUE_GRAPH:
							X: 0
							Y: 0
							Width: PARENT_WIDTH - 5
							Height: PARENT_HEIGHT
							ValueFormat: ${0}
							YAxisValueFormat: ${0:F0}
							XAxisSize: 40
							XAxisTicksPerLabel: 2
							XAxisLabel: Game Minute
							YAxisLabel: Army Value
							LabelFont: TinyBold
							AxisFont: TinyBold
				Container@TEAM_ARMY_VALUE_GRAPH_CONTAINER:
					X: 0
					Y: 30
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Visible: False
					Children:
						ColorBlock@GRAPH_BACKGROUND:
							X: 0
							Y: 0
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Color: 00000090
						ScrollableLineGraph@TEAM_ARMY_VALUE_GRAPH:
							X: 0
							Y: 0
							Width: PARENT_WIDTH - 5
							Height: PARENT_HEIGHT
							ValueFormat: ${0}
							YAxisValueFormat: ${0:F0}
							XAxisSize: 40
							XAxisTicksPerLabel: 2
							XAxisLabel: Game Minute
							YAxisLabel: Army Value
							LabelFont: TinyBold
							AxisFont: TinyBold
