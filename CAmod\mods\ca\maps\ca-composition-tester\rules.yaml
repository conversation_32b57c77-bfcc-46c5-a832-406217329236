
World:
	LuaScript:
		Scripts: composition-tester.lua
	MissionData:
		Briefing: Map for testing fixed value army compositions against each other. Requires 2+ players. Move the trucks into wormholes to reset/save/restore.

Player:
	-ModularBot@BrutalAI:
	-ModularBot@VeryHardAI:
	-Mo<PERSON><PERSON><PERSON>ot@HardAI:
	-ModularBot@NormalAI:
	-ModularBot@EasyAI:
	-ModularBot@NavalAI:

PATR:
	AttackOrderPowerCA@EMPMISSILE:
		ChargeInterval: 1

FACT:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

WEAP:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

TENT:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

AFLD:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

DOME:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None
	GpsRadarProvider:
		RequiresCondition: gps-active
	GrantDelayedCondition@GPS:
		Condition: gps-active
		Delay: 1

FIX:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-GrantConditionOnResupplying@Resupplying:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

NUK2:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

SYRD:
	DamageMultiplier@1:
		Modifier: 0
	-Sellable:
	Capturable:
		Types: None
		RequiresCondition: being-captured
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None

INDP:
	Valued:
		Cost: 0
	Buildable:
		Prerequisites: ~player.soviet

MUNP:
	Valued:
		Cost: 0
	Buildable:
		Prerequisites: ~player.soviet

CVAT:
	Valued:
		Cost: 0
	Buildable:
		Prerequisites: ~player.soviet

TRUK:
	Mobile:
		Speed: 300
		TurnSpeed: 256

CONFIGWORMHOLE:
	Inherits: WORMHOLE
	-Targetable:
	-RallyPoint:
	-TeleportNetwork:
	-Exit:
	Immobile:
		OccupiesSpace: false
	WithTextDecoration:
		Text: Reset
		Position: Top
		Font: Bold
		ValidRelationships: Ally, Enemy, Neutral
	RenderSprites:
		Image: wormhole

RESETWORMHOLE:
	Inherits: CONFIGWORMHOLE
	WithTextDecoration:
		Text: Reset
		Color: FF0000

SAVEWORMHOLE:
	Inherits: CONFIGWORMHOLE
	WithTextDecoration:
		Text: Save
		Color: 00FF00

RESTOREWORMHOLE:
	Inherits: CONFIGWORMHOLE
	WithTextDecoration:
		Text: Restore
		Color: 00FFFF

CASHWORMHOLE:
	Inherits: CONFIGWORMHOLE
	WithTextDecoration:
		Text: Cash
		Color: FFFF00

RESETWORMHOLE1:
	Inherits: RESETWORMHOLE
	WithTextDecoration:
		Text: Reset P1

SAVEWORMHOLE1:
	Inherits: SAVEWORMHOLE
	WithTextDecoration:
		Text: Save P1

RESTOREWORMHOLE1:
	Inherits: RESTOREWORMHOLE
	WithTextDecoration:
		Text: Restore P1

RESETWORMHOLE2:
	Inherits: RESETWORMHOLE
	WithTextDecoration:
		Text: Reset P2

SAVEWORMHOLE2:
	Inherits: SAVEWORMHOLE
	WithTextDecoration:
		Text: Save P2

RESTOREWORMHOLE2:
	Inherits: RESTOREWORMHOLE
	WithTextDecoration:
		Text: Restore P2

CASHWORMHOLE1:
	Inherits: CASHWORMHOLE
	WithTextDecoration:
		Text: Cash P1

CASHWORMHOLE2:
	Inherits: CASHWORMHOLE
	WithTextDecoration:
		Text: Cash P2
