World:
	MissionData:
		Briefing: ================\n\n- Each player starts with a single Mastermind (mind control unit that can control up to 3 units at a time).\n-------------------------\n- Random neutral units are spawned across the map (indestructible until controlled).\n-------------------------\n- Deploy (F) Mastermind for limited duration cloak.\n-------------------------\n- Objective is to kill all enemy Masterminds.\n\n================\n
	StartingUnits@mastonly:
		Class: none
		ClassName: Mastermind
		BaseActor: mast
		Factions: allies, england, france, germany, usa, soviet, russia, ukraine, iraq, yuri, gdi, talon, zocom, eagle, arc, nod, blackh, marked, legion, shadow, scrin, reaper, traveler, harbinger, collector
	-StartingUnits@mcvonly:
	-StartingUnits@lightallies:
	-StartingUnits@lightsoviet:
	-StartingUnits@heavyallies:
	-StartingUnits@heavysoviet:
	-StartingUnits@mcvonly2:
	-StartingUnits@defaultgdia:
	-StartingUnits@defaultnoda:
	-StartingUnits@heavynoda:
	-StartingUnits@heavygdia:
	-StartingUnits@mcvonlyscrin:
	-StartingUnits@lightscrin:
	-StartingUnits@heavyscrin:
	SpawnStartingUnits:
		DropdownVisible: False
	MapBuildRadius:
		AllyBuildRadiusCheckboxVisible: False
		BuildRadiusCheckboxVisible: False
	MapOptions:
		TechLevelDropdownVisible: False
		ShortGameCheckboxEnabled: True
		ShortGameCheckboxLocked: True
		ShortGameCheckboxVisible: False
	CrateSpawner:
		CheckboxEnabled: False
		CheckboxLocked: True
		CheckboxVisible: False
	MapStartingLocations:
		SeparateTeamSpawnsCheckboxVisible: False
	TimeLimitManager:
		TimeLimitLocked: True
		TimeLimitDropdownVisible: False

Player:
	PlayerResources:
		SelectableCash: 0
		DefaultCash: 0
		DefaultCashDropdownVisible: False
	LobbyPrerequisiteCheckbox@GLOBALBOUNTY:
		Enabled: False
		Locked: True
		Visible: False
	LobbyPrerequisiteCheckbox@FORCESHIELD:
		Enabled: False
		Visible: False
	DeveloperMode:
		CheckboxLocked: True
		CheckboxVisible: False
	LobbyPrerequisiteCheckbox@GLOBALFACTUNDEPLOY:
		Visible: False
	LobbyPrerequisiteCheckbox@NAVY:
		Visible: False
	LobbyPrerequisiteCheckbox@BALANCEDHARVESTING:
		Visible: False
	LobbyPrerequisiteCheckbox@FASTREGROWTH:
		Visible: False
	LobbyPrerequisiteCheckbox@REVEALONFIRE:
		Enabled: False
		Locked: True
		Visible: False
	LobbyPrerequisiteDropdown@QUEUETYPE:
		Default: global.singlequeue
		Visible: False
		Values:
			global.singlequeue: options-queuetype.singlequeue

^IndestructibleWhenNeutral:
	GrantConditionIfOwnerIsNeutral@NEUTRAL:
		Condition: is-neutral
	DamageMultiplier@NEUTRALINDESTRUCTIBLE:
		Modifier: 0
		RequiresCondition: is-neutral
	SpeedMultiplier@NEUTRALIMMOBILE:
		Modifier: 0
		RequiresCondition: is-neutral
	GrantCondition@NEUTRALDISABLE:
		Condition: being-warped
		RequiresCondition: is-neutral
	WithPalettedOverlay@TEMPORALl:
		RequiresCondition: being-warped && !is-neutral
	WithColoredOverlay@TEMPORAL2:
		RequiresCondition: being-warped && !is-neutral
	WithIdleOverlay@TEMPORAL:
		RequiresCondition: being-warped && !is-neutral
	Targetable@TEMPORAL:
		RequiresCondition: !invulnerability && !is-neutral

^Infantry:
	Inherits@INDESTRUCTIBLE: ^IndestructibleWhenNeutral
	Crushable:
		RequiresCondition: !is-neutral
	Targetable@MCINF:
		TargetTypes: MindControllableInfantry

^Vehicle-NOUPG:
	Inherits@INDESTRUCTIBLE: ^IndestructibleWhenNeutral
	Capturable:
		RequiresCondition: !is-neutral

^Defense:
	Inherits@INDESTRUCTIBLE: ^IndestructibleWhenNeutral
	Inherits@MindControllable: ^MindControllable

^AffectedByDriverKill:
	GrantConditionIfOwnerIsNeutral@DRIVER_DEAD:
		Condition: is-neutral
	GrantCondition@HACK:
		Condition: driver-dead
		RequiresCondition: driver-dead

MAST:
	Inherits@CLUSTERMISSILEPOWER: ^ClusterMissilePower
	Inherits@SATHACKPOWER: ^SatHackPower
	-Passenger:
	-PassengerBlocked:
	-UnusedCondition@Passenger:
	-MassEntersCargo:
	-TakeCover:
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: HoldFire
	Health:
		HP: 120000
	Mobile:
		Speed: 56
	MustBeDestroyed:
		RequiredForShortGame: true
	SpawnActorPowerCA@sathack:
		-Prerequisites:
		-PauseOnCondition:
		ChargeInterval: 2250
	-SpawnActorPowerCA@sathacklegion:
	NukePower@Cluster:
		-Prerequisites:
		-PauseOnCondition:
	GrantTimedConditionOnDeploy:
		DeployedCondition: deployed
		CooldownTicks: 1250
		DeployedTicks: 375
		StartsFullyCharged: true
		DischargingColor: bb0000
		ChargingColor: cc00cc
		Instant: true
	Cloak:
		InitialDelay: 0
		CloakDelay: 25
		CloakedCondition: hidden
		IsPlayerPalette: true
		DetectionTypes: SuperCloak
		RequiresCondition: deployed && !cloak-force-disabled
		UncloakOn: Unload, Infiltrate, Demolish, Dock, Attack, Damage
		CloakSound: gstealon.aud
		UncloakSound: gstealof.aud
	Cloak@CRATE-CLOAK:
		RequiresCondition: crate-cloak && !(cloak-force-disabled || invisibility || hidden)
	WithNameTagDecorationCA:
		Position: Top
		Font: Regular
		Margin: 0, -20
		ColorSource: Player
		ContrastColorLight: 000000
		ValidRelationships: Ally, Enemy, Neutral
	MindController:
		ExperienceFromControl: 0
	ReloadAmmoPool@Vehicles:
		Delay: 50
	-SpawnActorAbility:
	-AmmoPool@MindSpark:
	-ReloadAmmoPoolCA@MindSpark:

RNDV:
	Inherits@1: ^1x1Shape
	Interactable:
	EditorOnlyTooltip:
		Name: (random vehicle)
	AlwaysVisible:
	Mobile:
		Locomotor: wheeled
		TurnSpeed: 20
	WithFacingSpriteBody:
	RenderSpritesEditorOnly:
		Image: mnly
	MapEditorData:
		Categories: System
	RequiresSpecificOwners:
		ValidOwnerNames: Neutral
	Health:
		HP: 120000
	KillsSelf:
		Delay: 5
	SpawnRandomActorOnDeath@RANDOM:
		Actors: btr, bggy, hmmv, jeep, gdrn, gunw, shrw, bike, arty, arty.nod, howi, apc, vulc, rapc, ifv, ttnk, ttra, xo, msam, hsam, mlrs, spec, v2rl, katy, ftnk, hftk, corr, 1tnk, 2tnk, 3tnk, 3tnk.atomic, 3tnk.yuri, 3tnk.atomicyuri, mtnk, mtnk.drone, ltnk, wtnk, stnk.nod, rtnk, ptnk, pcan, ctnk, cryo, mgg, msg, mrj, mnly, msar, devo, stcr, ruin, atmz, lace, seek, intl, dark, tnkd, v3rl, isu, memp, cdrn, dtrk, qtnk, lchr, sapc
		OwnerType: InternalName
	ClassicFacingBodyOrientation:
	QuantizeFacingsFromSequence:

RNDB:
	Inherits: RNDV
	RenderSpritesEditorOnly:
		Image: amcv
	SpawnRandomActorOnDeath@RANDOM:
		Actors: 4tnk, htnk, titn, 4tnk.atomic, 4tnk.erad, apoc, htnk.ion, titn.rail, htnk.hover, htnk.drone, batf, chpr, tpod, rtpd, disr

OBLI:
	Power:
		Amount: 0

GUN:
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Power:
		Amount: 0

ATWR:
	Power:
		Amount: 0

FTUR:
	Inherits@IDISABLE: ^DisableOnLowPowerOrPowerDown
	Power:
		Amount: 0

# Make drones mindcontrollable and not disabled without radar

HTNK.Drone:
	-Targetable@MindControlImmune:
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active

MTNK.Drone:
	-Targetable@MindControlImmune:
	Power:
		Amount: 0
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active

MEMP:
	-Targetable@MindControlImmune:
	Power:
		Amount: 0
	ProvidesPrerequisite@radar-active:
		Prerequisite: radar-active

CDRN:
	-Targetable@MindControlImmune:
	Power:
		Amount: 0

GDRN:
	-Targetable@MindControlImmune:
	Power:
		Amount: 0

MDRN:
	-Targetable@MindControlImmune:
	Power:
		Amount: 0

BRUT:
	-Targetable@MindControlImmune:

CRATE:
	-GiveCashCrateAction:
	-GiveBaseBuilderCrateAction@TD:
	-GiveBaseBuilderCrateAction@RA:
	-GiveUnitCrateAction@squadheavyallies:
	-GiveUnitCrateAction@squadheavysoviet:
	-GiveUnitCrateAction@squadheavynod:
	-GiveUnitCrateAction@squadheavygdi:
	-GiveUnitCrateAction@squadheavyscrin:
	HealActorsCrateAction:
		SelectionShares: 40
	LevelUpCrateAction:
		SelectionShares: 50
	DuplicateUnitCrateAction:
		SelectionShares: 6
		MaxAmount: 2
		MinAmount: 1

APOC:
	Mobile:
		Locomotor: heavytracked

BATF:
	Mobile:
		Locomotor: heavytracked

TITN:
	Mobile:
		Locomotor: heavytracked

JUGG:
	Mobile:
		Locomotor: heavytracked
