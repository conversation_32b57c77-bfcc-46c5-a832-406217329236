^Palettes:
	PaletteFromFile@navy-barren:
		Name: navy
		Tileset: BARREN
		Filename: barrennavy.pal
		ShadowIndex: 4
	PaletteFromFile@navy-snow:
		Name: navy
		Tileset: SNOW
		Filename: snow.pal
		ShadowIndex: 4
	PaletteFromFile@navy-interior:
		Name: navy
		Tileset: INTERIOR
		Filename: interior.pal
		ShadowIndex: 4
	PaletteFromFile@navy-temperat:
		Name: navy
		Tileset: TEMPERAT
		Filename: temperat.pal
		ShadowIndex: 4
	PaletteFromFile@navy-desert:
		Name: navy
		Tileset: DESERT
		Filename: temperat.pal
		ShadowIndex: 4
	PaletteFromFile@navy-winter:
		Name: navy
		Tileset: WINTER
		Filename: winter.pal
		ShadowIndex: 4
	PaletteFromFile@navy-jungle:
		Name: navy
		Tileset: JUNGLE
		Filename: temperat.pal
		ShadowIndex: 4
	PaletteFromFile@terrain-snow:
		Name: terrain
		Tileset: SNOW
		Filename: snow.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-interior:
		Name: terrain
		Tileset: INTERIOR
		Filename: interior.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-temperat:
		Name: terrain
		Tileset: TEMPERAT
		Filename: temperat.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-desert:
		Name: terrain
		Tileset: DESERT
		Filename: desert.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-winter:
		Name: terrain
		Tileset: WINTER
		Filename: winter.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-jungle:
		Name: terrain
		Tileset: JUNGLE
		Filename: temperat.pal
		ShadowIndex: 3, 4
	PaletteFromFile@terrain-barren:
		Name: terrain
		Tileset: BARREN
		Filename: barren.pal
		ShadowIndex: 3, 4
	PaletteFromFile@tiberium-snow:
		Name: tiberiumpalette
		Tileset: SNOW
		Filename: temptdsnow.pal
		ShadowIndex: 3, 4
	PaletteFromFile@tiberium-temperat:
		Name: tiberiumpalette
		Tileset: TEMPERAT
		Filename: temperattd.pal
		ShadowIndex: 3, 4
	PaletteFromFile@tiberium-winter:
		Name: tiberiumpalette
		Tileset: WINTER
		Filename: temperattd.pal
		ShadowIndex: 3, 4
	PaletteFromFile@tiberium-jungle:
		Name: tiberiumpalette
		Tileset: JUNGLE
		Filename: temperattd.pal
		ShadowIndex: 3, 4
	PaletteFromFile@tiberium-interior:
		Name: tiberiumpalette
		Tileset: INTERIOR
		Filename: temperattd.pal
		ShadowIndex: 3, 4
	PaletteFromFile@tiberium-barren:
		Name: tiberiumpalette
		Tileset: BARREN
		Filename: temperattd.pal
		ShadowIndex: 3, 4
	PaletteFromFile@tiberium-desert:
		Name: tiberiumpalette
		Tileset: DESERT
		Filename: desert.pal
		ShadowIndex: 3, 4
	PaletteFromFile@player:
		Name: player
		Filename: temperat.pal
		ShadowIndex: 4
	PaletteFromFile@playertd:
		Name: playertd
		Filename: temperattd.pal
		ShadowIndex: 4
	PaletteFromFile@playerscrin:
		Name: playerscrin
		Filename: scrin.pal
		ShadowIndex: 4
	PaletteFromFile@d2k:
		Name: d2k
		Filename: PALETTE.BIN
		ShadowIndex: 1
		CursorPalette: true
	PaletteFromFile@d2keffect:
		Name: d2keffect
		Filename: PALETTE.BIN
		ShadowIndex: 1
		AllowModifiers: false
	PaletteFromFile@chrome:
		Name: chrome
		Filename: temperat.pal
		ShadowIndex: 3
		AllowModifiers: false
	PaletteFromFile@chromeTD:
		Name: chrometd
		Filename: temperattd.pal
		AllowModifiers: false
	PaletteFromFile@cursor:
		Name: cursor
		Filename: temperat.pal
		AllowModifiers: false
		CursorPalette: true
	PaletteFromFile@effect:
		Name: effect
		Filename: temperat.pal
		ShadowIndex: 4
	PaletteFromFile@tdeffect:
		Name: tdeffect
		Filename: temperattd.pal
		ShadowIndex: 4
	PaletteFromFile@tsunit:
		Name: tsunit
		Filename: unittem.pal
		ShadowIndex: 4
	PaletteFromFile@ra2unit:
		Name: ra2unit
		Filename: ra2unittem.pal
		ShadowIndex: 4
	PaletteFromFile@desert:
		Name: desert
		Filename: desert.pal
		ShadowIndex: 4
	PaletteFromFile@temptd:
		Name: temptd
		Filename: temperattd.pal
		ShadowIndex: 4
	OverlayPlayerColorPalette@TD:
		BasePalette: temptd
		BaseName: playertd
		RemapIndex: 176, 178, 180, 182, 184, 186, 189, 191, 177, 179, 181, 183, 185, 187, 188, 190
	OverlayPlayerColorPalette@D2K:
		BasePalette: d2k
		BaseName: playerd2k
		RemapIndex: 255, 254, 253, 252, 251, 250, 249, 248, 247, 246, 245, 244, 243, 242, 241, 240
	OverlayColorPickerPalette@colorpicker:
		Name: colorpicker
		BasePalette: player
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
		AllowModifiers: false
	OverlayColorPickerPalette@colorpickerscrin:
		Name: colorpickerscrin
		BasePalette: playerscrin
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
		AllowModifiers: false
	OverlayColorPickerPalette@colorpickertd:
		Name: colorpickertd
		BasePalette: playertd
		RemapIndex: 176, 178, 180, 182, 184, 186, 189, 191, 177, 179, 181, 183, 185, 187, 188, 190
		AllowModifiers: false
	PaletteFromFile@tseffect:
		Name: tseffect
		Filename: animts.pal
	PaletteFromFile@ra2effect:
		Name: ra2effect
		Filename: animra2.pal
		ShadowIndex: 4
	PaletteFromPaletteWithAlpha@RA2effect-nolite-alpha90:
		Name: ra2effect-ignore-lighting-alpha90
		Alpha: 0.9
		BasePalette: ra2effect
	PaletteFromPaletteWithAlpha@RA2effect-nolite-alpha75:
		Name: ra2effect-ignore-lighting-alpha75
		Alpha: 0.75
		BasePalette: ra2effect
	PaletteFromPaletteWithAlpha@RA2effect-nolite-alpha50:
		Name: ra2effect-ignore-lighting-alpha50
		Alpha: 0.5
		BasePalette: ra2effect
	PaletteFromPaletteWithAlpha@TSeffect-nolite-alpha75:
		Name: tseffect-ignore-lighting-alpha75
		Alpha: 0.75
		BasePalette: tseffect
	PaletteFromPaletteWithAlpha@TSunit-nolite-alpha75:
		Name: tsunit-ignore-lighting-alpha75
		Alpha: 0.75
		BasePalette: tsunit
	PaletteFromPaletteWithAlpha@RA2unit-nolite-alpha75:
		Name: ra2unit-ignore-lighting-alpha75
		Alpha: 0.75
		BasePalette: ra2unit
	PaletteFromPaletteWithAlpha@RA2unit-nolite-alpha90:
		Name: ra2unit-ignore-lighting-alpha90
		Alpha: 0.90
		BasePalette: ra2unit
	PaletteFromPaletteWithAlpha@TSeffect-nolite-alpha90:
		Name: tseffect-ignore-lighting-alpha90
		Alpha: 0.9
		BasePalette: tseffect
	PaletteFromPaletteWithAlpha@effect-nolite-alpha85:
		Name: effect-ignore-lighting-alpha85
		Alpha: 0.85
		BasePalette: effect
	PaletteFromPaletteWithAlpha@effect-nolite-alpha50:
		Name: effect-ignore-lighting-alpha50
		Alpha: 0.5
		BasePalette: effect
	PaletteFromPaletteWithAlpha@effect-nolite-alpha40:
		Name: effect-ignore-lighting-alpha40
		Alpha: 0.4
		BasePalette: effect
	PaletteFromPaletteWithAlpha@effect-nolite-alpha35:
		Name: effect-ignore-lighting-alpha35
		Alpha: 0.35
		BasePalette: effect
	PaletteFromPaletteWithAlpha@effect-nolite:
		Name: effect-ignore-lighting
		Alpha: 1
		BasePalette: effect
	PaletteFromPaletteWithAlpha@TDeffect-nolite-alpha85:
		Name: tdeffect-ignore-lighting-alpha85
		Alpha: 0.85
		BasePalette: tdeffect
	PaletteFromPaletteWithAlpha@D2Keffect-nolite-alpha75:
		Name: d2keffect-ignore-lighting-alpha75
		BasePalette: d2keffect
		Alpha: 0.75
	PaletteFromPaletteWithAlpha@D2Keffect-nolite-alpha50:
		Name: d2keffect-ignore-lighting-alpha50
		BasePalette: d2keffect
		Alpha: 0.5
	PaletteFromRGBA@shadow:
		Name: shadow
		R: 0
		G: 0
		B: 0
		A: 140
	PaletteFromRGBA@submerged:
		Name: submerged
		R: 0
		G: 0
		B: 0
		A: 140
	PaletteFromRGBA@scrinplasma:
		Name: scrinplasma
		R: 121
		G: 101
		B: 239
		A: 40
	PaletteFromRGBA@cold:
		Name: cold
		R: 81
		G: 174
		B: 217
		A: 28
	PaletteFromRGBA@moveflash:
		Name: moveflash
		R: 255
		G: 255
		B: 255
		A: 64
	PaletteFromRGBA@frenzyoverlay:
		Name: frenzyoverlay
		R: 255
		G: 68
		B: 0
		A: 40
	PaletteFromRGBA@ionsurgeoverlay:
		Name: ionsurgeoverlay
		R: 189
		G: 112
		B: 255
		A: 70
	ShroudPalette@shroud:
		Name: shroud
	ShroudPalette@fog:
		Name: fog
		Fog: true
	OverlayPlayerColorPalette@RA:
		BasePalette: player
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
	OverlayPlayerColorPalette@navy:
		BasePalette: navy
		BaseName: playernavy
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
	FixedColorPalette@BlueTiberium:
		Base: tiberiumpalette
		Name: bluetiberium
		Color: 89BAFF
		RemapIndex: 176, 178, 180, 182, 184, 186, 189, 191, 177, 179, 181, 183, 185, 187, 188, 190
	FixedColorPalette@BlackTiberium:
		Base: tiberiumpalette
		Name: blacktiberium
		Color: 2c0059
		RemapIndex: 176, 178, 180, 182, 184, 186, 189, 191, 177, 179, 181, 183, 185, 187, 188, 190
	PaletteFromPlayerPaletteWithAlpha@cloakra:
		BaseName: cloakra
		BasePalette: player
		Alpha: 0.5
	PaletteFromPlayerPaletteWithAlpha@cloaktd:
		BaseName: cloaktd
		BasePalette: playertd
		Alpha: 0.5
	PaletteFromRGBA@cloak:
		Name: cloak
		R: 0
		G: 0
		B: 0
		A: 140
	PaletteFromRGBA@cloakgreen:
		Name: cloakgreen
		R: 0
		G: 228
		B: 0
		A: 65
	PaletteFromRGBA@Darkened:
		Name: darkened
		R: 0
		G: 0
		B: 0
		A: 96
	CloakPaletteEffectCA@CLOAK:
		Palette: cloak
	CloakPaletteEffectCA@CLOAKGREEN:
		Palette: cloakgreen
	MenuPostProcessEffect:
		MenuEffect: Desaturated
	RotationPaletteEffect@defaultwater:
		Palettes: terrain
		ExcludeTilesets: DESERT
		ExcludePalettes: tiberiumpalette, bluetiberium
	RotationPaletteEffect@actorswater:
		Palettes: player, effect, playernavy
		ExcludePalettes: playerd2k, d2k, tiberiumpalette, bluetiberium
	RotationPaletteEffect@desertwater:
		Palettes: terrain, temptd, playertd, tiberiumpalette, bluetiberium
		Tilesets: DESERT
		RotationBase: 32
		ExcludePalettes: cursor, chrome, chrometd, chromes, tiberiumpalette, colorpicker, colorpickertd, colorpickerscrin, playerd2k, d2k
	RotationPaletteEffect@desertwater-actor:
		Palettes: desert, temptd, playertd, tdeffect, bluetiberium
		RotationBase: 32
		ExcludePalettes: cursor, chrome, chrometd, tiberiumpalette, bluetiberium, chromes, colorpicker, colorpickertd, colorpickerscrin, playerd2k, d2k
	LightPaletteRotator:
		ExcludePalettes: playerd2k, d2k, terrain, tiberiumpalette, bluetiberium, effect, tdeffect, scrineffect, desert, tseffect, ra2effect, tsunit, ra2unit, ra2unit-ignore-lighting-alpha75, tsunit-ignore-lighting-alpha75, tseffect-ignore-lighting-alpha75, tseffect-ignore-lighting-alpha90, effect-ignore-lighting-alpha85, ioncloud
	ChronoshiftPostProcessEffect:
	FlashPostProcessEffect@NUKE:
		Type: Nuke
	FlashPostProcessEffect@OverlordsWrath:
		Type: OverlordsWrath
		Color: 00ff00
	PaletteFromPaletteWithAlpha@placelinesegment:
		Name: placelinesegment
		BasePalette: terrain
		Alpha: 0.65
	PaletteFromPlayerPaletteWithAlpha@placebuilding:
		BaseName: placebuilding
		BasePalette: player
		Alpha: 0.65
	PaletteFromPlayerPaletteWithAlpha@placebuildingTD:
		BaseName: placebuildingtd
		BasePalette: playertd
		Alpha: 0.65
	PaletteFromRGBA@invuln:
		Name: invuln
		A: 0
	PulsingPaletteEffect@IC:
		PaletteName: invuln
		StartColor: 070000CC
		EndColor: 66000070
		PulseDuration: 35
		PulseDelay: 5
	PaletteFromRGBA@irrad:
		Name: irrad
		A: 0
	PulsingPaletteEffect@IRRADIATED:
		PaletteName: irrad
		StartColor: 55a40066
		EndColor: 7df100bb
		PulseDuration: 35
		PulseDelay: 5
	PaletteFromRGBA@forces:
		Name: forces
		A: 0
	PulsingPaletteEffect@FS:
		PaletteName: forces
		StartColor: 00008066
		EndColor: 0202bd99
		PulseDuration: 20
	PaletteFromRGBA@nrepair:
		Name: nrepair
		A: 0
	PulsingPaletteEffect@NREPAIR:
		PaletteName: nrepair
		StartColor: 00ffff99
		EndColor: 00ffff55
		PulseDuration: 10
	PaletteFromRGBA@berserk:
		Name: berserk
		A: 0
	PulsingPaletteEffect@BERSERK:
		PaletteName: berserk
		StartColor: 17000877
		EndColor: ff0072a0
		PulseDuration: 5
	PaletteFromRGBA@frenzy:
		Name: frenzy
		A: 0
	PulsingPaletteEffect@FRENZY:
		PaletteName: frenzy
		StartColor: 53150062
		EndColor: b1320072
		PulseDuration: 10
	PaletteFromRGBA@temporal:
		Name: temporal
		A: 0
	PulsingPaletteEffect@temporal:
		PaletteName: temporal
		StartColor: 9adaffaa
		EndColor: 9adaff88
		PulseDuration: 30
	PaletteFromRGBA@GYRO:
		Name: gyro
		A: 0
	PulsingPaletteEffect@GYRO:
		PaletteName: gyro
		StartColor: d7c00011
		EndColor: d7c00092
		PulseDuration: 35
	PaletteFromRGBA@SUPPRESSION:
		Name: suppression
		A: 0
	PulsingPaletteEffect@SUPPRESSION:
		PaletteName: suppression
		StartColor: ff28cf66
		EndColor: ef3fc864
		PulseDuration: 25
	PaletteFromRGBA@decoy:
		Name: decoy
		R: 255
		G: 255
		B: 255
		A: 40
	PaletteFromRGBA@Hypercharge:
		Name: hypercharge
		A: 0
	PulsingPaletteEffect@Hypercharge:
		PaletteName: hypercharge
		StartColor: 2600cc77
		EndColor: b700d477
		PulseDuration: 5
	PaletteFromRGBA@Anathema:
		Name: anathema
		A: 0
	PulsingPaletteEffect@Anathema:
		PaletteName: anathema
		StartColor: 07001777
		EndColor: 6600ffa0
		PulseDuration: 15
	PaletteFromRGBA@Overload:
		Name: overload
		A: 0
	PulsingPaletteEffect@Overload:
		PaletteName: overload
		StartColor: 00000088
		EndColor: 00005566
		PulseDuration: 3
	WeatherPaletteEffect@LIGHTNINGSTORM:
		Type: LightningStorm
		Color: 08112E03
		Length: 425
		Ratio: 0.3
		ExcludePalette: cursor, chrome, chrometd, chromes, colorpicker, colorpickertd, colorpickerscrin, fog, shroud, tseffect, ra2effect, ra2unit, tdeffect, effect, scrineffect,  effect-ignore-lighting, tdeffect-ignore-lighting-alpha85, d2keffect-ignore-lighting-alpha75, d2keffect-ignore-lighting-alpha50, ra2effect-ignore-lighting-alpha90, ra2effect-ignore-lighting-alpha75, ra2effect-ignore-lighting-alpha50, tseffect-ignore-lighting-alpha75, tsunit-ignore-lighting-alpha75, ra2unit-ignore-lighting-alpha75, ra2unit-ignore-lighting-alpha90, tseffect-ignore-lighting-alpha90
	WeatherPaletteEffect@Rift:
		Type: Rift
		Color: 14082E03
		Length: 420
		Ratio: 0.3
		ExcludePalette: cursor, chrome, chrometd, chromes, colorpicker, colorpickertd, colorpickerscrin, fog, shroud, tseffect, ra2effect, ra2unit, tdeffect, effect, scrineffect,  effect-ignore-lighting, tdeffect-ignore-lighting-alpha85, d2keffect-ignore-lighting-alpha75, d2keffect-ignore-lighting-alpha50, ra2effect-ignore-lighting-alpha90, ra2effect-ignore-lighting-alpha75, ra2effect-ignore-lighting-alpha50, tseffect-ignore-lighting-alpha75, tsunit-ignore-lighting-alpha75, ra2unit-ignore-lighting-alpha75, ra2unit-ignore-lighting-alpha90, tseffect-ignore-lighting-alpha90
	PaletteFromFile@caneon:
		Name: caneon
		Filename: caneon.pal
		ShadowIndex: 4
	PaletteFromPaletteWithAlpha@caneon-nolite-alpha75:
		Name: caneon-ignore-lighting-alpha75
		Alpha: 0.75
		BasePalette: caneon
	PaletteFromFile@scrin:
		Name: scrin
		Filename: scrin.pal
		ShadowIndex: 4
	PaletteFromFile@scrincursor:
		Name: scrincursor
		Filename: scrin.pal
		AllowModifiers: false
		CursorPalette: true
	PaletteFromFile@scrineffect:
		Name: scrineffect
		Filename: scrin.pal
		ShadowIndex: 4
	PaletteFromPaletteWithAlpha@scrin-nolite-alpha85:
		Name: scrin-ignore-lighting-alpha85
		Alpha: 0.85
		BasePalette: scrin
	PaletteFromPaletteWithAlpha@scrin-nolite-alpha30:
		Name: scrin-ignore-lighting-alpha30
		Alpha: 0.3
		BasePalette: scrin
	PaletteFromPaletteWithAlpha@scrin-nolite-alpha15:
		Name: scrin-ignore-lighting-alpha15
		Alpha: 0.15
		BasePalette: scrin
	OverlayPlayerColorPalette@scrin:
		BasePalette: scrin
		BaseName: playerscrin
		RemapIndex: 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95
	PaletteFromFile@chromescrin:
		Name: chromes
		Filename: scrin.pal
		ShadowIndex: 3
		AllowModifiers: false
	PaletteFromGimpOrJascFile@IonCloud:
		Name: ioncloud
		Filename: ioncloud.pal
	PaletteFromGimpOrJascFile@ScrinShield:
		Name: scrinshield
		Filename: scrinshield.pal
	PaletteFromPaletteWithAlpha@ScrinShield-nolite-alpha50:
		Name: scrinshield-ignore-lighting-alpha50
		Alpha: 0.2
		BasePalette: scrinshield
	PaletteFromGimpOrJascFile@MutaBlast:
		Name: mutablast
		Filename: mutablast.gpl
	PaletteFromPaletteWithAlpha@MutaBlast-nolite-alpha50:
		Name: mutablast-ignore-lighting-alpha50
		Alpha: 0.2
		BasePalette: mutablast
