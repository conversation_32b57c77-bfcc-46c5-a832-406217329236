WarriorGun:
	Inherits: M1Carbine
	Report: warrior-fire1.aud

WarriorGunBATF:
	Inherits: M1CarbineBATF
	Report: warrior-fire1.aud

DisintegratorBeam:
	Inherits: Laser
	ReloadDelay: 30
	Range: 5c0
	Report: disintegrator-fire1.aud, disintegrator-fire2.aud, disintegrator-fire3.aud
	Projectile: LaserZapCA
		Color: 00FFCCAA
		Width: 30
		Duration: 3
		SecondaryBeamWidth: 50
		SecondaryBeamColor: 00FFCC30
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Damage: 3000
		Versus:
			None: 10
			Wood: 74
			Heavy: 100
			Light: 34
			Concrete: 75

DisintegratorBeamBATF:
	Inherits: DisintegratorBeam
	Range: 5c768
	Warhead@1Dam: SpreadDamage
		Damage: 2250

DisintegratorBeamAA:
	Inherits: DisintegratorBeam
	Range: 7c0
	ValidTargets: Air, AirSmall
	Projectile: LaserZapCA
		Blockable: false
	Warhead@1Dam: SpreadDamage
		ValidTargets: Air
		ValidRelationships: Enemy, Neutral
		Range: 0, 0c64, 0c256, 3c0
		Falloff: 100, 100, 30, 15
		Damage: 2500
	Warhead@smallDamage: SpreadDamage
		ValidTargets: AirSmall
		ValidRelationships: Enemy, Neutral
		Spread: 128
		Falloff: 100, 50, 14, 0
		Damage: 3750

DisintegratorIFVBeam:
	Inherits: DisintegratorBeam
	ReloadDelay: 25
	Warhead@1Dam: SpreadDamage
		Damage: 3400

DisintegratorIFVBeamAA:
	Inherits: DisintegratorBeamAA
	ReloadDelay: 25
	Warhead@1Dam: SpreadDamage
		Damage: 4500
	Warhead@smallDamage: SpreadDamage
		Damage: 6800

EnslaveInfantry:
	Range: 7c0
	ReloadDelay: 35
	Projectile: InstantHit
	ValidTargets: MindControllable
	InvalidTargets: Vehicle, Ship, MindControlImmune
	Warhead@1Dam: TargetDamage
	Warhead@2Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: mindcontrol
		ValidTargets: Ground
		ValidRelationships: Neutral, Enemy
	Warhead@Flash: FlashTarget
		Spread: 341
		Color: ff00ff
		ValidTargets: Infantry
		ValidRelationships: Neutral, Enemy

EnslaveVehicle:
	Inherits: EnslaveInfantry
	Range: 4c512
	InvalidTargets: Infantry, MindControlImmune
	Warhead@Flash: FlashTarget
		ValidTargets: Vehicle

DetonateSlave:
	Range: 1c0
	ReloadDelay: 1
	Projectile: InstantHit
	Warhead@1Dam: HealthPercentageDamage
		Spread: 42
		Damage: 300
		ValidTargets: Infantry, Vehicle, Ship, Air, AirSmall
		DamageTypes: BulletDeath
	Warhead@3Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: kaboom12.aud

GunWalkerZap:
	Range: 5c0
	Report: gunwalker-fire1.aud, gunwalker-fire2.aud
	ReloadDelay: 30
	Burst: 5
	BurstDelays: 8
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 2450
		Versus:
			None: 100
			Wood: 10
			Concrete: 10
			Light: 35
			Heavy: 10
			Brick: 20
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath

GunwalkerZapVisual:
	Inherits: GunWalkerZap
	-Report:
	-Projectile:
	Projectile: Bullet
		Image: scrinzap
		Speed: 800
		Palette: scrin
	-Warhead@1Dam:
	Warhead@2Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: scrinzaphit
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
		Inaccuracy: 128
		ImpactSounds: irocatta.aud, irocattb.aud, irocattc.aud
		ImpactSoundChance: 100
	Warhead@3EffWater: CreateEffect
		Explosions: water_piffs
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

GunWalkerZapAA:
	Inherits: GunWalkerZap
	Range: 8c512
	-Projectile:
	Projectile: Bullet
		Speed: 3c0
		Blockable: false
	ValidTargets: Air, AirSmall
	Warhead@1Dam: SpreadDamage
		Damage: 1800
		ValidTargets: Air
		ValidRelationships: Enemy, Neutral
		Range: 0, 0c64, 0c256, 3c0
		Falloff: 100, 100, 30, 15
	Warhead@smallDamage: SpreadDamage
		Damage: 1800
		ValidTargets: AirSmall
		ValidRelationships: Enemy, Neutral
		Spread: 128
		Falloff: 100, 50, 14, 0

GunWalkerZapAAVisual:
	Inherits: GunWalkerZapAA
	Projectile: Bullet
		Image: scrinzapup
		Speed: 1c682
	-Warhead@1Dam:
	-Warhead@smallDamage:
	Warhead@2Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: scrinzaphit
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
		Inaccuracy: 128
		ImpactSounds: irocataa.aud, irocatab.aud, irocatac.aud, irocatad.aud
		ImpactSoundChance: 100

PlasmaDiscs:
	Range: 5c0
	MinRange: 1c512
	Report: seeker-fire1.aud, seeker-fire2.aud, seeker-fire3.aud
	ReloadDelay: 75
	Burst: 5
	BurstDelays: 4
	ValidTargets: Ground, Water, Underwater
	Projectile: Missile
		Image: plasdiscsm
		TrailImage: smokey
		TrailPalette: scrinplasma
		Speed: 450
		Inaccuracy: 0c256
		Blockable: false
		Jammable: true
		HorizontalRateOfTurn: 30
		RangeLimit: 6c512
		LockOnProbability: 50
		MinimumLaunchAngle: 80
		MaximumLaunchAngle: 112
		VerticalRateOfTurn: 60
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 2200
		ValidTargets: Ground, Water, Underwater
		Versus:
			None: 30
			Wood: 55
			Light: 65
			Heavy: 100
			Concrete: 35
			Brick: 75
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath, FlakVestMitigated
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion
		ImpactSounds: seeker-hit1.aud, seeker-hit2.aud

PlasmaDiscs.Hypercharged:
	Inherits: PlasmaDiscs
	Report: seeker-hyperfire1.aud, seeker-hyperfire2.aud
	Burst: 1
	ReloadDelay: 4
	Range: 6c0
	Projectile: Missile
		RangeLimit: 7c0
	Warhead@1Dam: SpreadDamage
		Damage: 1600
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath, FlakVestMitigated, TankBuster
		Versus:
			None: 15

IntruderDiscs:
	Inherits: PlasmaDiscs
	-MinRange:
	Range: 6c0
	ReloadDelay: 70
	Burst: 3
	Projectile: Missile
		Inaccuracy: 0c128
		LockOnProbability: 33
	Warhead@1Dam: SpreadDamage
		Damage: 2850
		Spread: 256
		Versus:
			None: 15
			Wood: 100
			Light: 65
			Heavy: 90
			Concrete: 100

MarauderDiscs:
	Inherits: IntruderDiscs
	Burst: 2
	BurstDelays: 5
	Report: mrdr-fire1.aud, mrdr-fire2.aud
	Projectile: Missile
		Image: mrdrdisc
		Speed: 550
		LockOnProbability: 100
		RangeLimit: 7c0
		Palette: scrin
		MinimumLaunchAngle: 40
		MaximumLaunchAngle: 80
		VerticalRateOfTurn: 90
	Warhead@1Dam: SpreadDamage
		Damage: 4100
		Spread: 384
		Versus:
			None: 10
			Light: 0
			Heavy: 0
	Warhead@2Dam: HealthPercentageSpreadDamage
		Damage: 100
		Spread: 384
		MaxReferenceHp: 60000
		MinReferenceHp: 30000
		Versus:
			None: 0
			Wood: 0
			Concrete: 0
			Light: 9
			Heavy: 8
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion

IntruderDiscsBATF:
	Inherits: IntruderDiscs
	Range: 5c768
	Warhead@1Dam: SpreadDamage
		Damage: 2150

DevastatorDiscs:
	Inherits: PlasmaDiscs
	Range: 16c0
	MinRange: 3c0
	ReloadDelay: 170
	Report: devastator-fire1.aud, devastator-fire2.aud, devastator-fire3.aud
	Burst: 7
	BurstDelays: 3
	TargetActorCenter: true
	-Projectile:
	Projectile: Bullet
		TrailImage: smokey
		TrailPalette: scrinplasma
		Blockable: false
		Image: plasdisclg
		Inaccuracy: 1c768
		Speed: 220
		LaunchAngle: 50
	Warhead@1Dam: SpreadDamage
		Spread: 416
		Damage: 4675
		Versus:
			None: 100
			Wood: 90
			Light: 65
			Heavy: 20
			Concrete: 100
			Brick: 100
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath, FlakVestMitigatedMinor, AirToGround
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion

DevastatorTorpedo:
	Inherits: DevastatorDiscs
	Burst: 3
	Report: plasmatorp.aud
	BurstDelays: 8
	ReloadDelay: 70
	Projectile: Bullet
		Image: plasmatorp3
		Palette: scrin
		LaunchAngle: 25
		Inaccuracy: 1c0
		Shadow: true
		ShadowColor: 00000033
	Warhead@1Dam: SpreadDamage
		Damage: 14000
		Spread: 512
	Warhead@4Eff: CreateEffect
		Explosions: large_artillery_explosion
		ImpactSounds: xplobig4.aud

^ScrinLaser:
	ValidTargets: Ground, Water
	Range: 7c0
	ReloadDelay: 85
	Projectile: LaserZapCA
		Duration: 3
		Width: 45
		ZOffset: 512
		SecondaryBeam: true
		Color: 66ff66FF
		SecondaryBeamColor: 00AA00CC
		SecondaryBeamWidth: 65
		SecondaryBeamZOffset: 511
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 7000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		Versus:
			None: 50
			Light: 65
			Wood: 30
			Heavy: 100
			Concrete: 90
			Brick: 50
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion, small_explosion_alt1, small_explosion_alt2, small_explosion_alt3

InterloperLaser:
	Inherits: ^ScrinLaser
	Range: 4c768
	ReloadDelay: 40
	Burst: 2
	BurstDelays: 5
	Report: interloper-fire1.aud, interloper-fire2.aud
	Warhead@1Dam: SpreadDamage
		Damage: 1900
		Versus:
			None: 30
			Light: 116
			Wood: 80
			Heavy: 100
			Concrete: 40

^ScrinPlasmaBeam:
	Inherits: ^ScrinLaser
	-Projectile:
	Projectile: PlasmaBeam
		Duration: 21
		Colors: 7d44ffE6, eb66ffE6, 5763ffE6
		InnerLightness: 200
		OuterLightness: 115
		Radius: 2
		SegmentLength: 0
		Blockable: true
		RecalculateColors: true
		TrackTarget: true
		MaxFacingDeviation: 64

DevourerLaser:
	Inherits: ^ScrinPlasmaBeam
	Report: devourer-fire1.aud, devourer-fire2.aud
	Range: 7c0
	ReloadDelay: 95
	Projectile: PlasmaBeam
		StartOffset: 0,360,0
		FollowingOffset: 0,-36,0
		ImpactTicks: 0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 1300
		Versus:
			None: 20
			Light: 70
			Wood: 45
	Warhead@antiShip: SpreadDamage
		ValidTargets: Water
		Damage: 750

DevourerLaserCharged:
	Inherits: DevourerLaser
	Report: devourer-cfire1.aud, devourer-cfire2.aud
	Projectile: PlasmaBeam
		Colors: 461fc605, 98177805
		InnerLightness: 240
		Radius: 4
		Distortion: 128
		DistortionAnimation: 128
		SegmentLength: 196
		CenterBeamColor: ffffffff
		CenterBeam: true
		CenterBeamWidth: 45
		StartOffset: 0,240,0
		FollowingOffset: 0,-24,0
		RecalculateDistortionInterval: 2
	Warhead@1Dam: SpreadDamage
		Damage: 1750
		Versus:
			None: 30
			Light: 85

RiftCannon:
	Inherits: ^ScrinLaser
	Report: darkener-fire1.aud, darkener-fire2.aud
	ReloadDelay: 120
	Range: 7c0
	Projectile: LaserZapCA
		Duration: 20
		Color: 000000FF
		Width: 70
		SecondaryBeamColor: 00000099
		SecondaryBeamWidth: 90
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Falloff: 100, 37, 23, 10, 0
		Damage: 2500
		Versus:
			None: 15
			Wood: 45
			Light: 100
			Brick: 75
		DamageTypes: BulletDeath
	Warhead@2Dam: HealthPercentageSpreadDamage
		Spread: 341
		Falloff: 100, 37, 23, 10, 0
		Damage: 100
		Versus:
			Wood: 4
			None: 6
			Concrete: 6
			Brick: 6
			Light: 12
			Heavy: 12
		DamageTypes: BulletDeath
	Warhead@antiShip: SpreadDamage
		ValidTargets: Water
		Damage: 4400
	Warhead@prone: SpreadDamage
		Spread: 512
		Falloff: 100, 100, 0
		Damage: 1
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@Spawn: SpawnActor
		Actors: riftminor
		Range: 1
		ForceGround: false
		ValidTargets: Ground, Water
		ImpactActors: false

MiniRift:
	ReloadDelay: 10
	Range: 1c0
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 692
		Damage: 840
		Falloff: 100, 70, 0
		Versus:
			None: 3
			Wood: 45
			Light: 90
			Heavy: 100
			Concrete: 95
			Brick: 20
		DamageTypes: BulletDeath
		ValidRelationships: Enemy, Neutral
	Warhead@2Dam: SpreadDamage
		Spread: 692
		Damage: 168
		Falloff: 100, 70, 0
		Versus:
			None: 3
			Wood: 45
			Light: 90
			Heavy: 100
			Concrete: 95
			Brick: 20
		DamageTypes: BulletDeath
		ValidRelationships: Ally
	Warhead@3Slow: GrantExternalConditionCA
		Range: 1c0
		Duration: 10
		Condition: slowed
		ValidTargets: Vehicle, Ship, Cyborg
	Warhead@4Slow: GrantExternalConditionCA
		Range: 2c0
		Duration: 10
		Condition: slowed
		ValidTargets: Vehicle, Ship
		ValidRelationships: Enemy, Neutral

CyberscrinLaser:
	Inherits: DevourerLaser
	Report: cscr-fire1.aud
	ReloadDelay: 50
	Range: 5c512
	Projectile: PlasmaBeam
		Colors: ff0000E6, ff6600E6
		Radius: 1
		CenterBeamColor: ff0000ff
		CenterBeam: true
		CenterBeamWidth: 45
		Duration: 17
		StartOffset: 0,288,0
		FollowingOffset: 0,-36,0
		ImpactTicks: 0, 2, 4, 6, 8, 10, 12, 14, 16
	Warhead@1Dam: SpreadDamage
		Spread: 256
		Damage: 1000
		Versus:
			None: 100
			Heavy: 20
			Light: 70
			Concrete: 30

CorrupterSpew:
	StartBurstReport: corrupter-fire1.aud
	ReloadDelay: 100
	Burst: 30
	BurstDelays: 3
	Range: 7c0
	Projectile: Bullet
		Speed: 110
		TrailImage: tibspew1
		Image: tibspew2
		LaunchAngle: 62
		Inaccuracy: 682
		Blockable: false
		Palette: scrin-ignore-lighting-alpha85
		TrailPalette: scrin-ignore-lighting-alpha85
	Warhead@1Dam: SpreadDamage
		DamageTypes: Prone50Percent, TriggerProne, PoisonDeath, FlakVestMitigatedMinor
		Spread: 288
		Falloff: 100, 45, 20, 7, 0
		Damage: 1000
		Versus:
			None: 300
			Wood: 125
			Light: 90
			Heavy: 30
			Concrete: 30
			Brick: 5
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Building, Wall
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: caneon
		Image: tibexplodesm

CorrupterExplode:
	Inherits: UnitExplodeIraqTank
	Warhead@3Eff_impact: CreateEffect
		Explosions: med_chem
		ExplosionPalette: tdeffect
		ImpactSounds: vtoxcona.aud, vtoxconb.aud, vtoxconc.aud
	-Warhead@6Eff_areanuke1:
	Warhead@Shrap: FireShrapnel
		Weapon: ChemDebris
		Amount: 5
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

LeecherExplode:
	Inherits: UnitExplodeSmall
	Warhead@3Eff_impact: CreateEffect
		Explosions: lchr_explode
		ExplosionPalette: scrineffect
		ImpactSounds: vtoxcona.aud, vtoxconb.aud, vtoxconc.aud

LeecherCoalesce:
	ValidTargets: Ground, Water
	Warhead@3Eff: CreateEffect
		Explosions: lchr_coalesce
		ExplosionPalette: scrineffect
		ImpactSounds: coalesce.aud

RuinerCannon:
	Inherits: 155mm
	Report: ruiner-fire1.aud
	ReloadDelay: 175
	Burst: 5
	BurstDelays: 15
	Range: 10c0
	Projectile: Bullet
		Speed: 185
		Image: tibglob
		Palette: scrin
		Inaccuracy: 682
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 5000
		Versus:
			None: 120
			Wood: 100
			Concrete: 80
			Light: 90
			Heavy: 45
			Brick: 20
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath, FlakVestMitigatedMinor
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
	Warhead@3Eff: CreateEffect
		ImpactSounds: ruiner-hit1.aud
		Explosions: idle
		ExplosionPalette: caneon
		Image: tibexplode

RuinerCannonCharged:
	Inherits: RuinerCannon
	Report: ruiner-cfire1.aud
	Burst: 3
	Projectile: Bullet
		Image: tibgloblg
		Speed: 224
	Warhead@1Dam: SpreadDamage
		Damage: 8200
		Spread: 512
	Warhead@Shrap: FireShrapnel
		Weapon: ChemDebrisSmall
		Amount: 2
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

TripodLaser:
	Inherits: ^ScrinPlasmaBeam
	Range: 5c768
	Report: tripod-fire1.aud, tripod-fire2.aud, tripod-fire3.aud
	ReloadDelay: 65
	Projectile: PlasmaBeam
		Duration: 7
		Colors: 7d44ffE6, eb66ffE6, 5763ffE6
		Blockable: false
		StartOffset: -150,300,0
		FollowingOffset: 50,-100,0
		ImpactTicks: 0, 1, 2, 3, 4, 5, 6
		MaxFacingDeviation: 128
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 925
		Versus:
			None: 85
			Light: 70
			Wood: 65
			Heavy: 105
			Concrete: 60

ReaperLaser:
	Inherits: TripodLaser
	ReloadDelay: 55
	Report: rtpd-fire1.aud, rtpd-fire2.aud, rtpd-fire3.aud
	Projectile: PlasmaBeam
		InnerLightness: 170
		OuterLightness: 130
		Colors: 338800EE, 008800EE
		CenterBeam: true
		CenterBeamWidth: 45
		CenterBeamColor: eeffeeff

ReaperLaserCharged:
	Inherits: ReaperLaser
	Report: rtpd-cfire1.aud, rtpd-cfire2.aud
	Projectile: PlasmaBeam
		Colors: 33880005, 00880005
		InnerLightness: 170
		OuterLightness: 140
		Radius: 4
		Distortion: 128
		DistortionAnimation: 128
		SegmentLength: 196
		RecalculateDistortionInterval: 2
	Warhead@1Dam: SpreadDamage
		Damage: 1080

TripodLaserReversed:
	Inherits: TripodLaser
	Projectile: PlasmaBeam
		StartOffset: -150,-300,0
		FollowingOffset: 50,100,0

ReaperLaserReversed:
	Inherits: ReaperLaser
	Projectile: PlasmaBeam
		StartOffset: -150,-300,0
		FollowingOffset: 50,100,0

ReaperLaserChargedReversed:
	Inherits: ReaperLaserCharged
	Projectile: PlasmaBeam
		StartOffset: -150,-300,0
		FollowingOffset: 50,100,0

ExterminatorLaser:
	Inherits: TripodLaser
	ReloadDelay: 55
	Report: etpd-fire1.aud, etpd-fire2.aud
	Projectile: PlasmaBeam
		InnerLightness: 170
		OuterLightness: 130
		Colors: 880000EE, AA0000EE
		CenterBeam: true
		CenterBeamWidth: 40
		CenterBeamColor: ffeeeeff
		Radius: 3
		Distortion: 64
		DistortionAnimation: 64
		SegmentLength: 196
		RecalculateDistortionInterval: 2
		MaxFacingDeviation: 256
	Warhead@1Dam: SpreadDamage
		Damage: 2250
		Spread: 512
		Versus:
			Wood: 45
			Concrete: 50
	Warhead@3Eff: CreateEffect
		Explosions: large_explosion, small_explosion_alt1, small_explosion_alt2, small_explosion_alt3

ExterminatorLaserReversed:
	Inherits: ExterminatorLaser
	Projectile: PlasmaBeam
		StartOffset: -150,-300,0
		FollowingOffset: 50,100,0

StormriderZap:
	Range: 5c768
	Report: stormrider-fire1.aud, stormrider-fire2.aud, stormrider-fire3.aud
	ReloadDelay: 70
	Burst: 5
	BurstDelays: 4
	Projectile: Bullet
		Image: scrinzapdown
		Inaccuracy: 42
		Speed: 1c128
		Palette: scrin
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 256
		Damage: 4800
		Versus:
			None: 105
			Wood: 50
			Concrete: 30
			Light: 55
			Heavy: 40
			Brick: 20
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, AirToGround
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Structure, Wall
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: scrinzaphit
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
		Inaccuracy: 128
		ImpactSounds: irocatta.aud, irocattb.aud, irocattc.aud
		ImpactSoundChance: 100
	Warhead@4EffWater: CreateEffect
		Explosions: water_piffs
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

StormRiderZapAA:
	Inherits: StormriderZap
	ReloadDelay: 70
	Projectile: Bullet
		Image: scrinzap
		Speed: 1c768
	ValidTargets: Air, AirSmall
	Warhead@1Dam: SpreadDamage
		Damage: 1800
		Range: 0, 0c64, 0c256, 1c768
		Falloff: 100, 100, 30, 15
		ValidTargets: Air
		ValidRelationships: Enemy, Neutral
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@smallDamage: SpreadDamage
		Damage: 1800
		Spread: 341
		Falloff: 100, 50, 14, 0
		ValidTargets: AirSmall
		ValidRelationships: Enemy, Neutral
	-Warhead@2Smu:
	Warhead@3Eff: CreateEffect
		ImpactSounds: irocataa.aud, irocatab.aud, irocatac.aud, irocatad.aud

TormentorZap:
	ReloadDelay: 70
	Burst: 6
	BurstDelays: 0, 4, 0, 4, 0
	ValidTargets: Ground, Water
	Range: 7c0
	Report: torm-fire1.aud, torm-fire2.aud
	Projectile: LaserZapCA
		Width: 56
		HitAnim: laserfire
		Color: 954fffAA
		ZOffset: 512
		SecondaryBeam: true
		SecondaryBeamWidth: 90
		SecondaryBeamZOffset: 511
		SecondaryBeamColor: 7d00fe30
		Duration: 3
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 2100
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, TankBuster, AirToGround
		Versus:
			None: 20
			Light: 65
			Wood: 30
			Heavy: 100
			Concrete: 95
			Brick: 50
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep

TormentorZapAA:
	Inherits: TormentorZap
	Range: 6c0
	ValidTargets: Air, AirSmall
	Warhead@1Dam: SpreadDamage
		Damage: 1100
		ValidTargets: Air, AirSmall
	-Warhead@2Smu:

EnervatorBolts:
	Inherits: ^AntiGroundMissile
	Report: enervator-fire1.aud
	ReloadDelay: 50
	Range: 6c0
	TargetActorCenter: true
	Projectile: MissileCA
		Speed: 341
		Image: enrvbolt
		Palette: scrin
		Inaccuracy: 128
		ContrailLength: 20
		ContrailStartColor: FF00FF77
		ContrailStartColorAlpha: 119
		ContrailStartWidth: 32
		Jammable: false
		LockOnProbability: 100
		RangeLimit: 8c0
		TrailImage: smokey
		TrailPalette: scrinplasma
		-PointDefenseType:
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 13000
		Versus:
			None: 30
			Wood: 30
			Light: 80
			Heavy: 100
			Concrete: 100
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster, AirToGround
	Warhead@2Sup: GrantExternalConditionCA
		Range: 0c768
		Duration: 75
		Condition: suppression
		ValidTargets: Vehicle, Ship, Structure
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: enrvbolthit
		ImpactSounds: enervator-hit1.aud

EnervatorBoltsAA:
	Inherits: EnervatorBolts
	Range: 5c0
	ValidTargets: Air, AirSmall
	Projectile: MissileCA
		CloseEnough: 341
		RangeLimit: 13c0
	Warhead@1Dam: SpreadDamage
		ValidTargets: Air
		Range: 0, 0c64, 0c256, 3c0
		Falloff: 100, 100, 30, 15
		ValidRelationships: Enemy, Neutral
		Damage: 6000
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath
	Warhead@smallDamage: SpreadDamage
		Damage: 6000
		Spread: 341
		Falloff: 100, 50, 14, 0
		ValidTargets: AirSmall
		ValidRelationships: Enemy, Neutral
	Warhead@2Sup: GrantExternalConditionCA
		ValidTargets: Air, AirSmall

InvaderLauncher:
	ReloadDelay: 5
	Range: 10c0
	ValidTargets: Air, AirSmall, Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage
		ValidTargets: Air, AirSmall, Ground, Water
		Damage: 5000
		Versus:
			None: 0
			Wood: 0
			Concrete: 0
			Light: 0
			Heavy: 0
			Brick: 0

InvaderZap:
	Range: 5c0
	Report: gunwalker-fire1.aud, gunwalker-fire2.aud
	ReloadDelay: 50
	Burst: 3
	BurstDelays: 5
	Projectile: Bullet
		Image: scrinzapdown
		Speed: 1c128
		Palette: scrin
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 448
		Damage: 1000
		InvalidTargets: Infantry
		Versus:
			None: 65
			Wood: 30
			Concrete: 15
			Light: 80
			Heavy: 100
			Brick: 20
		DamageTypes: FireDeath, AirToGround
	Warhead@2Dam: SpreadDamage
		Spread: 128
		ValidTargets: Infantry
		Damage: 1000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, AirToGround
	Warhead@2Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: scrinzaphitsm
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
		Inaccuracy: 341
	Warhead@3EffWater: CreateEffect
		Explosions: water_piffs
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

InvaderZapAA:
	Inherits: InvaderZap
	Projectile: Bullet
		Image: scrinzap
		Speed: 1c768
	ValidTargets: Air, AirSmall
	Warhead@1Dam: SpreadDamage
		Damage: 235
		Range: 0, 0c64, 0c256, 1c0
		Falloff: 100, 100, 30, 15
		ValidTargets: Air
		ValidRelationships: Enemy, Neutral
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	-Warhead@2Dam:
	Warhead@smallDamage: SpreadDamage
		Damage: 235
		Spread: 341
		Falloff: 100, 50, 14, 0
		ValidTargets: AirSmall
		ValidRelationships: Enemy, Neutral

PlasmaTurretGun:
	Range: 6c0
	StartBurstReport: plasma-fire1.aud
	ReloadDelay: 14
	Burst: 2
	BurstDelays: 4
	Projectile: InstantHit
		Blockable: true
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Falloff: 100, 47, 8, 0
		Damage: 2100
		Versus:
			None: 85
			Wood: 50
			Concrete: 30
			Light: 55
			Heavy: 35
			Brick: 20
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath
	Warhead@2Dam: HealthPercentageDamage
		Spread: 42
		Damage: 10
		ValidTargets: Infantry
		DamageTypes: Prone50Percent, TriggerProne, DefaultDeath
	Warhead@2Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: scrinzaphit
		ValidTargets: Ground, Ship, Air, AirSmall, Trees
	Warhead@3EffWater: CreateEffect
		Explosions: water_piffs
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

StormColumnZap:
	ReloadDelay: 35
	Burst: 7
	BurstDelays: 2
	Range: 7c511
	StartBurstReport: stormcolumn-fire1.aud, stormcolumn-fire2.aud
	Projectile: ElectricBolt
		Colors: 6042f5E6, B961FDE6, B5A3E8E6, 6D4AD2E6, B289DCE6, FFFFFFE6
		Duration: 6
		Width: 18
		Angle: 45
		Distortion: 96
		DistortionAnimation: 96
		ZOffset: 512
	Warhead@1Dam: SpreadDamage
		Damage: 1600
		Versus:
			None: 55
			Wood: 60
			Concrete: 90
			Light: 80
			Heavy: 95
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Structure, Wall
	Warhead@3Eff: CreateEffect
		Explosions: 1, 3
		Image: fire

IonCloudZap:
	Inherits: StormColumnZap
	ReloadDelay: 250
	Burst: 1
	ValidTargets: Ground, Water
	Projectile: ElectricBolt
		Colors: B961FDB6, CC00DDB6, 6D4AD2B6, ECBAF9B6, CC61FDB6, FFFFFFB6
		Distortion: 160
		DistortionAnimation: 160
		Duration: 10
	Warhead@1Dam: SpreadDamage
		Damage: 4000
		Spread: 512

IonCloudMinorZap:
	Inherits: IonCloudZap
	Range: 5c512
	Warhead@1Dam: SpreadDamage
		Damage: 2500

IonCloudChargeSource:
	ReloadDelay: 250
	Range: 0c512
	Projectile: InstantHit
	Warhead@Charge: FireShrapnel
		Weapon: IonCloudChargeZap
		Amount: 1
		AimChance: 100
		ThrowWithoutTarget: false
		AimTargetStances: Ally
		ValidTargets: Ground, Water, Air

IonCloudChargeZap:
	Inherits: StormColumnZap
	Burst: 1
	ReloadDelay: 250
	Range: 3c768
	ValidTargets: StormBoost
	-StartBurstReport:
	Report: stormcrawler-fire1.aud, stormcrawler-fire2.aud
	Projectile: ElectricBolt
		Colors: 6042f596, B961FD96, B5A3E896, 6D4AD296, FFFFFF96
		Distortion: 160
		DistortionAnimation: 160
	-Warhead@1Dam:
	-Warhead@2Smu:
	-Warhead@3Eff:
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: ffffff
		ValidTargets: Ground, Water, Air

StormcrawlerZap:
	Inherits: StormColumnZap
	ReloadDelay: 50
	Burst: 6
	BurstDelays: 3
	Range: 4c512
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 2800
		Versus:
			None: 15
			Wood: 70
			Light: 32
			Heavy: 28
			Concrete: 100

GravityStabilizerZap:
	Inherits: StormColumnZap
	-StartBurstReport:
	Range: 1c512
	ReloadDelay: 25
	Burst: 6
	BurstDelays: 6
	-Warhead@1Dam:
	-Warhead@2Smu:
	-Warhead@3Eff:
	ValidTargets: Resupplying
	FirstBurstTargetOffset: 0,0,192
	Projectile: ElectricBolt
		Colors: FFFFFF88, FFFFFF44
		PlayerColorZaps: 2
		PlayerColorZapAlpha: 100, 180
		Duration: 6
		Width: 32
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: ffffff
		ValidTargets: Resupplying

MothershipCharge:
	ReloadDelay: 10
	Warhead@Zaps: FireShrapnel
		ValidTargets: Ground, Water, Air
		Weapon: MothershipChargeZap
		Amount: 3
		AimChance: 100
		AllowDirectHit: true
		ThrowWithoutTarget: false
		AimTargetStances: Ally
		ImpactSounds: mshp-stmrcharge.aud

MothershipChargeZap:
	Inherits: GravityStabilizerZap
	ValidTargets: MothershipRearmable
	Range: 7c0
	Projectile: ElectricBolt
		TrackTarget: true
		Colors: FFFFFF66, FFFFFF33
		Duration: 3
		Distortion: 160
		DistortionAnimation: 160
	Warhead@Flash: FlashTarget
		ValidTargets: MothershipRearmable
	Warhead@rearm: GrantExternalConditionCA
		Range: 0c511
		Duration: 80
		Condition: mshp-rearm
		ValidTargets: MothershipRearmable

ShardLauncher:
	StartBurstReport: shard-fire1.aud
	ReloadDelay: 25
	Burst: 5
	BurstDelays: 4
	Range: 8c0
	ValidTargets: Air, AirSmall, ICBM
	Projectile: Missile
		Speed: 448
		CloseEnough: 448
		Palette: scrin
		ContrailStartWidth: 0c48
		ContrailLength: 12
		ContrailStartColor: 00FF00
		ContrailStartColorAlpha: 170
		Jammable: true
		Blockable: false
		Inaccuracy: 0
		Image: shard
		HorizontalRateOfTurn: 140
		RangeLimit: 10c512
		AllowSnapping: true
	Warhead@1Dam: SpreadDamage
		ValidTargets: Air
		ValidRelationships: Enemy, Neutral
		Range: 0, 0c64, 0c256, 3c768
		Falloff: 100, 100, 30, 15
		Damage: 2650
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@smallDamage: SpreadDamage
		ValidTargets: AirSmall, ICBM
		ValidRelationships: Enemy, Neutral
		Range: 0, 0c64, 0c256, 1c768
		Falloff: 100, 70, 30, 10
		Damage: 2650
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
	Warhead@3Eff: CreateEffect
		Explosions: idle, idle2, idle3, idle4
		ExplosionPalette: scrin
		Image: shardhit
		ValidTargets: Air, AirSmall, ICBM, Ground, Water, Trees
		Inaccuracy: 171
		ImpactSounds: gshardhit1.aud, gshardhit2.aud
		ImpactSoundChance: 100

RavagerShards:
	Inherits: ShardLauncher
	StartBurstReport: ravager-fire1.aud
	ValidTargets: Ground, Water, Trees
	ReloadDelay: 45
	BurstDelays: 5
	Range: 5c0
	Projectile: Missile
		Speed: 265
		CloseEnough: 265
		ContrailLength: 9
		ContrailStartColor: 00FF00
		ContrailStartColorAlpha: 68
		Inaccuracy: 128
		RangeLimit: 7c0
		Jammable: false
		AllowSnapping: false
	-Warhead@smallDamage:
	Warhead@1Dam: SpreadDamage
		ValidTargets: Ground, Water
		-ValidRelationships:
		-Range:
		-Falloff:
		Spread: 224
		Damage: 2000
		Versus:
			Wood: 15
			Light: 65
			Heavy: 10
			Concrete: 15
			Brick: 5
	Warhead@3Eff: CreateEffect
		ImpactSounds: gshardhitsm1.aud, gshardhitsm2.aud

EvisceratorShards:
	Inherits: RavagerShards
	Range: 6c0
	ReloadDelay: 30
	BurstDelays: 4
	Projectile: Missile
		Speed: 350
	Warhead@1Dam: SpreadDamage
		Damage: 2100
		Versus:
			Wood: 20

EvisceratorShardsCharged:
	Inherits: EvisceratorShards
	StartBurstReport: shard-fire1.aud
	Projectile: Missile
		ContrailStartColorAlpha: 128
	Warhead@1Dam: SpreadDamage
		Damage: 2600
		Versus:
			Wood: 30

StalkerShards:
	Inherits: RavagerShards
	StartBurstReport: stlk-fire1.aud, stlk-fire2.aud
	Range: 3c512
	Burst: 2
	BurstDelays: 4
	ReloadDelay: 40
	Projectile: Missile
		Speed: 350
		Image: scrinzap
		ContrailStartColor: 954fff
	Warhead@1Dam: SpreadDamage
		Damage: 7500
		Versus:
			Wood: 15
			Concrete: 10
			Light: 40
			Heavy: 8
		ValidRelationships: Enemy, Neutral
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: scrinzaphit
		ValidTargets: Ground, Ship, Trees
		Inaccuracy: 128
		-ImpactSounds:
	Warhead@3EffWater: CreateEffect
		Explosions: water_piffs
		ValidTargets: Water, Underwater
		InvalidTargets: Ship, Structure, Bridge

ShardWalkerShards:
	Inherits: RavagerShards
	ReloadDelay: 35
	StartBurstReport: shard-fire1.aud
	Projectile: Missile
		ContrailStartColor: 00FF0066
		ContrailStartColorAlpha: 102
		MinimumLaunchAngle: 80
		MaximumLaunchAngle: 112
		Inaccuracy: 0
		AllowSnapping: true
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 2650
		Versus:
			None: 115
			Wood: 10
			Light: 32
			Heavy: 10
			Concrete: 10

ShardWalkerShardsAA:
	Inherits: ShardLauncher
	ReloadDelay: 35
	BurstDelays: 5
	Range: 8c512
	Projectile: Missile
		ContrailStartColor: 00FF0066
		ContrailStartColorAlpha: 102
	Warhead@1Dam: SpreadDamage
		Damage: 1800
		Range: 0, 0c64, 0c256, 3c0
		Falloff: 100, 100, 30, 15
	Warhead@smallDamage: SpreadDamage
		Damage: 1800
		-Range:
		Spread: 128
		Falloff: 100, 50, 14, 0

LaceratorShards:
	Inherits: RavagerShards
	Range: 5c0
	StartBurstReport: lacerator-fire1.aud, lacerator-fire2.aud
	ReloadDelay: 100
	ValidTargets: Ground, Water, Underwater
	Projectile: Missile
		Image: bshard
		ContrailLength: 9
		ContrailStartWidth: 0c32
		ContrailStartColor: 4455FF
		ContrailStartColorAlpha: 170
		Jammable: true
	Warhead@1Dam: SpreadDamage
		Damage: 2900
		Spread: 128
		ValidTargets: Ground, Water, Underwater
		Versus:
			None: 15
			Wood: 55
			Light: 60
			Heavy: 100
			Concrete: 35
			Brick: 50
	Warhead@3Eff: CreateEffect
		Image: bshardhit

LaceratorShards.Hypercharged:
	Inherits: LaceratorShards
	Burst: 1
	ReloadDelay: 5
	-StartBurstReport:
	Range: 6c0
	Report: lacerator-hyperfire1.aud, lacerator-hyperfire2.aud
	Projectile: Missile
		ContrailStartWidth: 0c48
		ContrailStartColor: 7e00ff
	Warhead@1Dam: SpreadDamage
		Damage: 2000
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath, TankBuster
		Versus:
			None: 10

ImpalerSpike:
	Range: 7c0
	ReloadDelay: 125
	Burst: 2
	BurstDelays: 25
	Report: impl-fire1.aud
	Projectile: Missile
		Speed: 750
		Blockable: false
		Image: DRAGON
		Shadow: True
		HorizontalRateOfTurn: 20
		RangeLimit: 8c0
		ContrailLength: 11
		ContrailStartWidth: 0c48
		ContrailStartColor: ffffff
		ContrailStartColorAlpha: 64
		AllowSnapping: true
	Warhead@1Dam: SpreadDamage
		Damage: 12500
		Spread: 128
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
		Versus:
			Heavy: 15
			Light: 80
			Wood: 30
			Concrete: 25
			Brick: 15
	Warhead@3Eff: CreateEffect
		Explosions: piff
		ValidTargets: Ground, Water, Trees
		Inaccuracy: 64
		ImpactSounds: gshardhit1.aud, gshardhit2.aud
		ImpactSoundChance: 100
	Warhead@Slow: GrantExternalConditionCA
		Range: 0c256
		Duration: 25
		Condition: slowed
		ValidTargets: Vehicle, Infantry

WatcherDart:
	Range: 6c0
	ReloadDelay: 100
	Report: wchr-fire1.aud
	ValidTargets: WatcherParasiteAttachable
	Projectile: Missile
		Speed: 750
		Blockable: false
		Image: wchrdart
		Shadow: True
		HorizontalRateOfTurn: 20
		RangeLimit: 8c0
		ContrailLength: 11
		ContrailStartWidth: 0c48
		ContrailStartColor: 000000
		ContrailStartColorAlpha: 128
		AllowSnapping: true
	Warhead@Attach: AttachActor
		Range: 341
		Actor: watcher.parasite
		AttachSounds: wchr-hit1.aud
		MissSounds: dud1.aud
		ValidTargets: WatcherParasiteAttachable
		ValidRelationships: Enemy, Neutral
	Warhead@Flash: FlashTarget
		Spread: 341
		Color: ffffff
		ValidTargets: WatcherParasiteAttachable

RiftInit:
	ValidTargets: Ground, Water
	Projectile: InstantExplode
	ReloadDelay: 1
	Warhead@Spawn: SpawnActor
		Actors: rift
		Range: 1
		ForceGround: false
		ValidTargets: Ground, Water
		ImpactActors: false

Rift:
	ReloadDelay: 5
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 2300
		Falloff: 100, 95, 50, 40, 35, 30, 15, 10, 5
		ValidTargets: Ground, Trees, Underwater, Air, AirSmall
		Versus:
			None: 30
			Wood: 115
			Light: 70
			Heavy: 90
			Concrete: 120
			Brick: 75
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath

SuppressionField:
	Projectile: InstantExplode
	ReloadDelay: 1
	ValidTargets: Ground, Air, AirSmall, Ship
	# extra suppression range vs aircraft
	Warhead@Suppression: GrantExternalConditionCA
		Range: 4c512
		Duration: 600
		Condition: suppression
		ValidTargets: Air, AirSmall

BuzzerSpawner:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@1Dam: HealthPercentageDamage
		Spread: 341
		Damage: 75
		Versus:
			None: 100
			Wood: 1
			Light: 30
			Heavy: 1
			Concrete: 1
			Brick: 0
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
		ValidRelationships: Enemy, Neutral
	Warhead@Spawn: SpawnActor
		Actors: buzz
		Range: 1
		ForceGround: false
		ValidTargets: Ground, Water
		ImpactActors: false

BuzzerSpawnerAI:
	Inherits: BuzzerSpawner
	Warhead@Spawn: SpawnActor
		Actors: buzz.ai

BuzzerVortex:
	ReloadDelay: 5
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 682
		Falloff: 100, 70, 40, 0
		Damage: 150
		Versus:
			None: 100
			Wood: 10
			Light: 100
			Heavy: 30
			Concrete: 40
			Brick: 0
		DamageTypes: BulletDeath
		ValidRelationships: Enemy, Neutral
	Warhead@2Dam: HealthPercentageDamage
		Spread: 1c0
		Damage: 4
		Versus:
			None: 100
			Wood: 3
			Light: 30
			Heavy: 3
			Concrete: 3
			Brick: 0
		DamageTypes: BulletDeath
		ValidRelationships: Enemy, Neutral

BuzzerTargeting:
	ReloadDelay: 50
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Damage: 1
		ValidTargets: Ground, Trees
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath

StormSpikeSpawner:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@spawn: SpawnBuilding
		Buildings: scol.temp
		Range: 3
		ValidTargets: Ground, Water, Trees

VoidSpikeSpawner:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@spawn: SpawnBuilding
		Buildings: vspk
		Range: 3
		ValidTargets: Ground, Water, Trees

IonSurgeSpawner:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@Spawn: SpawnActor
		Actors: ionsurge
		Range: 1
		ForceGround: false
		ValidTargets: Ground, Water
		ImpactActors: false

IonSurge:
	ReloadDelay: 20
	Range: 1c0
	Projectile: InstantHit
	Warhead@surge: GrantExternalConditionCA
		Range: 5c0
		Duration: 300
		Condition: ionsurge
		ValidTargets: IonSurgable
		ValidRelationships: Ally

GatewaySpawner:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@Spawn: SpawnActor
		Actors: rebelgateway
		Range: 3
		ForceGround: false
		ValidTargets: Ground, Water
		ImpactActors: false
		AvoidActors: true

MothershipChargeBeam:
	ReloadDelay: 3
	Range: 0c512
	TargetActorCenter: true
	ValidTargets: Structure
	Projectile: PlasmaBeam
		ForceVertical: true
		Duration: 2
		Colors: ffffff44
		InnerLightness: 200
		OuterLightness: 75
		Radius: 2
		Distortion: 0
		DistortionAnimation: 0
		SegmentLength: 0
		ZOffset: 2048

MothershipBeam:
	ReloadDelay: 150
	Range: 0c512
	TargetActorCenter: true
	ValidTargets: Structure
	Projectile: PlasmaBeam
		ForceVertical: true
		Duration: 125
		Colors: 461fc609, 98177809
		InnerLightness: 240
		Radius: 7
		Distortion: 128
		DistortionAnimation: 96
		SegmentLength: 160
		CenterBeamColor: ffffffff
		CenterBeam: true
		CenterBeamWidth: 42
		SecondaryCenterBeam: true
		SecondaryCenterBeamColor: ffffff44
		ZOffset: 2048
		RecalculateColors: true
		RecalculateDistortionInterval: 3
		ImpactInterval: 5
	Warhead@1Dam: SpreadDamage
		Spread: 512
		Falloff: 100, 75, 56, 42, 31, 23, 17, 13, 10, 7
		Damage: 9000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@5Eff: CreateEffect
		Image: mshpshockwave
		ExplosionPalette: scrineffect
		Explosions: idle1, idle2, idle3, idle4
		Inaccuracy: 512
	Warhead@4Eff: CreateEffect
		Image: mshpshockwave
		Explosions: inner1, inner2, inner3, inner4
		ExplosionPalette: scrineffect
	Warhead@5Shake: ShakeScreen
		Duration: 5
		Intensity: 1
		Multiplier: 0.1,0.1

MothershipDiscs:
	Inherits: DevastatorDiscs
	Report: mshp-zap1.aud, mshp-zap2.aud
	Range: 7c0
	MinRange: 1c0
	ReloadDelay: 50
	Burst: 3
	BurstDelays: 7
	InvalidTargets: Structure
	Projectile: Bullet
		Image: plasmatorp1
		Palette: scrin
		LaunchAngle: 0
		Inaccuracy: 0c512
		Shadow: true
		ShadowColor: 00000033
		Speed: 280
	Warhead@1Dam: SpreadDamage
		Damage: 5000
		Versus:
			None: 100
			Wood: 45
			Concrete: 45
			Light: 80
			Heavy: 100

LeecherBeam:
	ValidTargets: Ground, Water
	StartBurstReport: leecher-fire1.aud
	Range: 5c512
	ReloadDelay: 60
	Burst: 9
	BurstDelays: 5
	Projectile: PlasmaBeam
		Duration: 7
		Colors: ee000008
		InnerLightness: 200
		OuterLightness: 100
		Radius: 2
		Distortion: 150
		DistortionAnimation: 150
		SegmentLength: 350
		Inaccuracy: 128
	Warhead@1Dam: SpreadDamage
		Spread: 288
		Falloff: 100, 45, 20, 7, 0
		Damage: 3250
		DamageTypes: Prone50Percent, TriggerProne, AtomizedDeath, FlakVestMitigatedMinor
		Versus:
			None: 100
			Light: 50
			Wood: 60
			Heavy: 15
			Concrete: 15
			Brick: 5
	Warhead@DRAIN: GrantExternalConditionCA
		Range: 1c512
		Duration: 100
		Condition: powerdrain
		ValidTargets: PowerDrainable
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: leechhitlg
		Inaccuracy: 256

LeecherOrbHeal:
	ReloadDelay: 25
	Range: 1c0
	ValidTargets: Ground, Water
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 1c128
		Damage: -1650
		Falloff: 100, 100, 0
		ValidTargets: Heal, Repair
		Versus:
			None: 50
			Light: 75
			Heavy: 100
		ValidRelationships: Ally

LeecherOrbHealInit:
	ReloadDelay: 1500
	Warhead@1Dam: SpreadDamage
		Damage: -5000

GreaterCoalescenceSpawner:
	ReloadDelay: 1
	Projectile: InstantExplode
	Warhead@Spawn: SpawnActor
		Actors: grcl
		Range: 1
		ForceGround: false
		ValidTargets: Ground, Water
		ImpactActors: false

GreaterCoalescence:
	ReloadDelay: 5
	Warhead@heal: SpreadDamage
		Range: 0, 4c512
		Falloff: 100, 100
		Damage: -550
		ValidTargets: Heal, Repair
		Versus:
			None: 40
			Light: 65
			Heavy: 100
		ValidRelationships: Ally
	Warhead@zaps: FireShrapnel
		Weapon: GreaterCoalescenceZap
		Amount: 3
		AimChance: 100
		AllowDirectHit: true
		ThrowWithoutTarget: false
		AimTargetStances: Enemy, Neutral

GreaterCoalescenceZap:
	Inherits: LeecherBeam
	Range: 4c512
	ValidTargets: Infantry, Vehicle, Structure, Ship
	Burst: 7
	Projectile: PlasmaBeam
		Duration: 6
		Inaccuracy: 0
		Colors: ee000006
	Warhead@1Dam: SpreadDamage
		Spread: 160
		Damage: 150
		Versus:
			None: 100
			Light: 100
			Wood: 50
			Heavy: 100
			Concrete: 50
		DamageTypes: TriggerProne, AtomizedDeath
	Warhead@3Eff: CreateEffect
		Image: leechhit
		Inaccuracy: 128
	Warhead@SLOW: GrantExternalConditionCA
		Range: 0c768
		Duration: 30
		Condition: slowed
		ValidTargets: Vehicle, Cyborg

TiberiumCoalescence:
	Inherits: GreaterCoalescence
	ReloadDelay: 8
	-Warhead@heal:
	Warhead@zaps: FireShrapnel
		Weapon: TiberiumCoalescenceZap
		ImpactSounds: grcl-loop1.aud, grcl-loop2.aud

TiberiumCoalescenceZap:
	Inherits: GreaterCoalescenceZap
	Range: 4c512
	Projectile: PlasmaBeam
		Colors: 00660006
		Duration: 9
	Warhead@1Dam: SpreadDamage
		Damage: 800
		DamageTypes: TriggerProne, RadiationDeath
	Warhead@3Eff: CreateEffect
		Image: ruin
		Explosions: muzzle
		ExplosionPalette: scrin
	-Warhead@DRAIN:
	-Warhead@SLOW:

VoidSpike:
	ReloadDelay: 15
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Spread: 8c0
		Damage: 200
		Falloff: 100, 50
		DamageTypes: Prone50Percent, TriggerProne, BulletDeath
		ValidRelationships: Enemy, Neutral

AtomizerBolts:
	Report: atomizer-fire1.aud
	ReloadDelay: 150
	Range: 9c0
	MinRange: 1c512
	Projectile: Missile
		Speed: 200
		Image: atmzbolt
		Palette: scrin
		Jammable: true
		RangeLimit: 12c0
		CruiseAltitude: 1c512
		TrailImage: atmzbolttrail
		TrailPalette: scrin
		TrailInterval: 1
		Blockable: false
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 4000
		ValidTargets: Ground, Water
		Versus:
			None: 15
			Wood: 30
			Light: 65
			Heavy: 100
			Concrete: 90
		DamageTypes: Prone50Percent, AtomizedDeath
	Warhead@Arc: FireShrapnel
		Weapon: AtomizerArc
		TargetClosest: true
		Amount: 4
		AimChance: 100
		ThrowWithoutTarget: false
		AimTargetStances: Enemy, Neutral
		AllowDirectHit: true
	Warhead@ArcInfantry: FireShrapnel
		Weapon: AtomizerArcInfantry
		TargetClosest: true
		Amount: 0
		AimChance: 100
		ThrowWithoutTarget: false
		AimTargetStances: Enemy, Neutral
		AllowDirectHit: true
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: scrin
		Image: atmzbolthit
		ImpactSounds: atomizer-hit1.aud

AtomizerArc:
	Inherits: AtomizerBolts
	Range: 1c768
	ValidTargets: Vehicle, Ship, Defense
	TargetActorCenter: true
	-MinRange:
	-Report:
	-Projectile:
	-Warhead@1Dam:
	-Warhead@Arc:
	-Warhead@ArcInfantry:
	Projectile: PlasmaBeam
		Duration: 7
		Colors: ee000008
		InnerLightness: 200
		OuterLightness: 100
		Radius: 2
		Distortion: 150
		DistortionAnimation: 150
		SegmentLength: 350
		TrackTarget: true
	Warhead@Flash: FlashTarget
		Spread: 0c42
		Color: ff0000
		ValidTargets: Vehicle, Ship, Defense, Infantry
	Warhead@atomize: GrantExternalConditionCA
		Range: 0c42
		Duration: 150
		Condition: atomized
		ValidTargets: Vehicle, Ship, Defense, Infantry
	Warhead@2Dam: TargetDamage
		DamageTypes: Prone50Percent, AtomizedDeath
		Damage: 2000
		Delay: 35
		Versus:
			None: 15
			Light: 65
			Concrete: 90
	Warhead@3Dam: TargetDamage
		DamageTypes: Prone50Percent, AtomizedDeath
		Damage: 2000
		Delay: 70
		Versus:
			None: 15
			Light: 65
			Concrete: 90
	Warhead@4Dam: TargetDamage
		DamageTypes: Prone50Percent, AtomizedDeath
		Damage: 2000
		Delay: 105
		Versus:
			None: 15
			Light: 65
			Concrete: 90
	Warhead@5Dam: TargetDamage
		DamageTypes: Prone50Percent, AtomizedDeath
		Damage: 2000
		Delay: 140
		Versus:
			None: 15
			Light: 65
			Concrete: 90
	Warhead@3Eff: CreateEffect
		Image: leechhitlg
		-ImpactSounds:

AtomizerArcInfantry:
	Inherits: AtomizerArc
	Range: 0c768
	ValidTargets: Infantry
	-Projectile:
	-Warhead@3Eff:
	Projectile: InstantHit

BursterTargeting:
	ValidTargets: Vehicle, Infantry, Structure
	Range: 1c512
	Projectile: InstantHit
	Warhead@1Dam: TargetDamage

BursterExplode:
	ValidTargets: Ground, Water
	Warhead@1Dam: SpreadDamage
		Damage: 12000
		Spread: 1c0
		Falloff: 1000, 368, 135, 50, 25, 12, 0
		InvalidTargets: Structure, Burster
		Versus:
			None: 35
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@FF: HealthPercentageDamage
		Spread: 2c0
		Damage: 25
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
		ValidTargets: Burster
	Warhead@SDam: SpreadDamage
		Damage: 4500
		Spread: 1c0
		Falloff: 1000, 1000, 0
		ValidTargets: Structure
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Structure, Wall, Trees
		Size: 3
		Delay: 5
	Warhead@3Eff: CreateEffect
		Explosions: lchr_explode
		ExplosionPalette: effect
		ImpactSounds: vdemdiea.aud
		Delay: 1
	Warhead@4Eff: CreateEffect
		Explosions: artillery_explosion

MindSparkEruption:
	ReloadDelay: 1000
	Projectile: InstantExplode
	Warhead@2Eff: CreateEffect
		Explosions: reverse
		ExplosionPalette: scrin
		Image: mindcontrol
		ValidTargets: Ground
		ImpactSounds: mastermind-shatter.aud
		ImpactActors: false
		AirThreshold: 512
	Warhead@Suppress: GrantExternalConditionCA
		Range: 2c0
		Duration: 200
		Condition: suppression
		ValidRelationships: Enemy, Neutral
		AirThreshold: 512

MindSparkZap:
	ReloadDelay: 60
	Range: 2c512
	Report: mindspark-zap1.aud, mindspark-zap2.aud
	Projectile: PlasmaBeam
		Duration: 7
		Colors: c45dff88
		InnerLightness: 200
		OuterLightness: 100
		Radius: 1
		Distortion: 250
		DistortionAnimation: 150
		SegmentLength: 350
		ZOffset: 512
	Warhead@1Dam: SpreadDamage
		Spread: 42
		Damage: 25000
		Versus:
			Light: 15
			Heavy: 5
			Concrete: 10
			Wood: 5
		DamageTypes: ElectricityDeath
	Warhead@3Eff: CreateEffect
		Explosions: small_explosion

ObliteratorCharge:
	Range: 15c0
	MinRange: 1c0
	ReloadDelay: 5
	Projectile: LinearPulse
		Speed: 2c0
		ImpactInterval: 448
	Warhead@2Eff: CreateEffect
		Explosions: idle, idle2, idle3, idle4, idle5, idle6, idle7, idle8
		ExplosionPalette: scrin
		Image: obltboltcharge
		ValidTargets: Ground, Air, Ship, Trees
		Inaccuracy: 341

ObliteratorBolt:
	Range: 15c0
	MinRange: 1c0
	ReloadDelay: 75
	Report: oblt-fire1.aud
	Projectile: LinearPulse
		Speed: 768
		Image: obltbolt
		Sequences: idle, idle2
		Palette: scrin
		ImpactInterval: 390
		FinalHitAnimSequence: idle
		FinalHitAnimPalette: scrin
		FinalHitAnim: enrvbolthit
	Warhead@1Dam: SpreadDamage
		Spread: 390
		Falloff: 100, 50, 0
		Damage: 7500
		Versus:
			Wood: 40
			Concrete: 40
			Heavy: 65
			Light: 65
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		ValidRelationships: Enemy, Neutral
	Warhead@friendlyFire: SpreadDamage
		Spread: 390
		Falloff: 100, 50, 0
		Damage: 1000
		Versus:
			Wood: 40
			Concrete: 40
			Heavy: 65
			Light: 65
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
		ValidRelationships: Ally
		InvalidTargets: Obliterator
	Warhead@2Eff: CreateFacingEffect
		Explosions: trail, trail2, trail3
		ExplosionPalette: scrin
		Image: obltbolt
		ValidTargets: Ground, Air, Ship, Trees
		Inaccuracy: 110
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch
	Warhead@Flash: FlashTarget
		Spread: 1c0
		Color: 9c3ee0
		ValidTargets: Infantry, Structure, Vehicle

NullifierBolt:
	Range: 8c0
	ReloadDelay: 80
	Report: null-fire1.aud
	Projectile: MissileCA
		Image: nullbolt
		Palette: caneon
		Speed: 600
		TrailImage: nulltrail
		TrailSequences: idle1, idle2
		TrailPalette: caneon
		TrailInterval: 1
		TrailSpacing: 600
		ContrailStartWidth: 32
		ContrailStartColor: ffff00
		ContrailStartColorAlpha: 96
		ContrailLength: 6
		RangeLimit: 9c0
		Jammable: false
		AllowSnapping: true
	Warhead@1Dam: SpreadDamage
		Spread: 0c128
		Damage: 25000
		Versus:
			None: 100
			Wood: 25
			Light: 50
			Heavy: 10
			Concrete: 25
			Brick: 10
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath
	Warhead@Blind: GrantExternalConditionCA
		Range: 0c341
		Duration: 125
		Condition: blinded
		ValidTargets: Infantry, Vehicle, Ship, Defense
	Warhead@3Eff: CreateEffect
		Explosions: idle
		ExplosionPalette: caneon
		Image: nullhit
		ImpactSounds: null-hit1.aud
	Warhead@Flash: FlashTarget
		Spread: 0c128
		Color: ffff00
		ValidTargets: Infantry, Vehicle, Ship, Defense

ScrinRepair:
	Inherits: Heal
	Range: 2c512
	ValidTargets: Repair
	Projectile: LaserZapCA
		Width: 48
		Duration: 4
		Color: FFFFFF80
	Warhead@1Dam: SpreadDamage
		Damage: -3000
		ValidTargets: Repair
		DamageTypes: DirectRepair
	-Warhead@2Dam:
	-Warhead@3Dam:
	-Warhead@4Dam:
	Warhead@Flash: FlashTarget
		Spread: 0c511
		Color: ff0000
		ValidTargets: Repair
