T01:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T01.Husk

T02:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T02.Husk

T03:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T03.Husk

T04:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: TEMPERAT, SNOW, INTERIOR
	SpawnActorOnDeath:
		Actor: T04.Husk

T05:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T05.Husk

T06:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T06.Husk

T07:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T07.Husk

T08:
	Inherits: ^Tree
	Building:
		Footprint: x_
		Dimensions: 2,1
	SpawnActorOnDeath:
		Actor: T08.Husk

T09:
	Inherits: ^Tree
	MapEditorData:
		ExcludeTilesets: TEMPERAT, SNOW, INTERIOR
	SpawnActorOnDeath:
		Actor: T09.Husk

T10:
	Inherits: ^Tree
	Building:
		Footprint: __ xx
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T10.Husk

T11:
	Inherits: ^Tree
	Building:
		Footprint: __ xx
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T11.Husk

T12:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T12.Husk

T13:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T13.Husk

T14:
	Inherits: ^Tree
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T14.Husk

T15:
	Inherits: ^Tree
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T15.Husk

T16:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T16.Husk

T17:
	Inherits: ^Tree
	Building:
		Footprint: __ x_
		Dimensions: 2,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: T17.Husk

T18:
	Inherits: ^Tree
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: INTERIOR, TEMPERATE, SNOW, WINTER, JUNGLE, BARREN
	SpawnActorOnDeath:
		Actor: T18.Husk

TC01:
	Inherits: ^Tree
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: INTERIOR
	SpawnActorOnDeath:
		Actor: TC01.Husk

TC02:
	Inherits: ^Tree
	Building:
		Footprint: _x_ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: TC02.Husk

TC03:
	Inherits: ^Tree
	Building:
		Footprint: xx_ xx_
		Dimensions: 3,2
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: TC03.Husk

TC04:
	Inherits: ^Tree
	Building:
		Footprint: ____ xxx_ x___
		Dimensions: 4,3
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: TC04.Husk

TC05:
	Inherits: ^Tree
	Building:
		Footprint: __x_ xxx_ _xx_
		Dimensions: 4,3
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR
	SpawnActorOnDeath:
		Actor: TC05.Husk

BOXES01:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES02:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES03:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES04:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES05:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES06:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES07:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES08:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

BOXES09:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Boxes
	MapEditorData:
		Categories: Decoration
	MirageTarget:

ICE01:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: Ice Floe
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, SNOW, BARREN
		Categories: Decoration

ICE02:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Building:
		Footprint: x x
		Dimensions: 1,2
	Tooltip:
		Name: Ice Floe
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, SNOW, BARREN
		Categories: Decoration

ICE03:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Building:
		Footprint: xx
		Dimensions: 2,1
	Tooltip:
		Name: Ice Floe
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, SNOW, BARREN
		Categories: Decoration

ICE04:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Ice Floe
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, SNOW, BARREN
		Categories: Decoration

ICE05:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Ice Floe
	MapEditorData:
		RequireTilesets: TEMPERAT, WINTER, SNOW, BARREN
		Categories: Decoration

ROCK1:
	Inherits: ^Rock
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2

ROCK2:
	Inherits: ^Rock
	Building:
		Footprint: xx_
		Dimensions: 3,1

ROCK3:
	Inherits: ^Rock
	Building:
		Footprint: ___ xx_
		Dimensions: 3,2

ROCK4:
	Inherits: ^Rock
	Building:
		Footprint: x_
		Dimensions: 2,1

ROCK5:
	Inherits: ^Rock
	Building:
		Footprint: x_
		Dimensions: 2,1

ROCK6:
	Inherits: ^Rock
	Building:
		Footprint: ___ xxx
		Dimensions: 3,2

ROCK7:
	Inherits: ^Rock
	Building:
		Footprint: xxxx_
		Dimensions: 5,1

UTILPOL1:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Utility Pole
	MapEditorData:
		Categories: Decoration

UTILPOL2:
	Inherits: ^Tree
	RenderSprites:
		Palette: player
	Tooltip:
		Name: Utility Pole
	MapEditorData:
		Categories: Decoration

TANKTRAP1:
	Inherits: ^Rock
	RenderSprites:
		Palette: player
	Building:
		Footprint: x
		Dimensions: 1,1
	Tooltip:
		Name: Tank Trap
	MapEditorData:
		RequireTilesets: TEMPERAT, SNOW, DESERT, INTERIOR, BARREN, WINTER, JUNGLE

TANKTRAP2:
	Inherits: ^Rock
	RenderSprites:
		Palette: player
	Building:
		Footprint: x
		Dimensions: 1,1
	Tooltip:
		Name: Tank Trap
	MapEditorData:
		RequireTilesets: TEMPERAT, SNOW, DESERT, INTERIOR, BARREN, WINTER, JUNGLE

#### ALIEN WORLD

^ScrinFlora:
	Inherits: ^Tree
	MapEditorData:
		ExcludeTilesets: DESERT, INTERIOR

SCRINFLORA1:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1

SCRINFLORA2:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1

SCRINFLORA3:
	Inherits: ^ScrinFlora
	Building:
		Footprint: xx
		Dimensions: 2,1

SCRINFLORA4:
	Inherits: ^ScrinFlora
	Building:
		Footprint: xx
		Dimensions: 2,1

SCRINFLORA5:
	Inherits: ^ScrinFlora
	Building:
		Footprint: xx
		Dimensions: 2,1

SCRINFLORA6:
	Inherits: ^ScrinFlora
	Building:
		Footprint: xxx
		Dimensions: 3,1

SCRINFLORA7:
	Inherits: ^ScrinFlora
	Building:
		Footprint: xx
		Dimensions: 2,1

SCRINFLORA8:
	Inherits: ^ScrinFlora
	Building:
		Footprint: xx
		Dimensions: 2,1

SCRINFLORA9:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1

SCRINFLORA10:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1

SCRINFLORA11:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1

SCRINFLORA12:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1

SCRINFLORA13:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1

SCRINFLORA14:
	Inherits: ^ScrinFlora
	Building:
		Footprint: x
		Dimensions: 1,1
