Metadata:
	Title: mod-title
	Version: prep-CA
	Website: https://www.moddb.com/mods/command-conquer-combined-arms
	WebIcon32: https://ca.oraladder.net/img/icon_32x32.png
	WindowTitle: mod-windowtitle

PackageFormats: Mix

FileSystem: ContentInstallerFileSystem
	SystemPackages:
		^EngineDir
		$ca: ca
		^EngineDir|mods/common: common
		~^SupportDir|Content/ca
		~^SupportDir|Content/ca/expand
		~^SupportDir|Content/ca/ra
		~^SupportDir|Content/ca/cnc
		~^SupportDir|Content/ca/ts
		~^SupportDir|Content/ca/firestorm
		~^SupportDir|Content/ca/ra2
		~^SupportDir|Content/ca/movies
		~^SupportDir|Content/ca/music
		ca|scripts
		ca|uibits
	ContentPackages:
		~main.mix
		~conquer.mix
		~lores.mix: lores
		~hires.mix
		~local.mix
		~sounds.mix
		~speech.mix
		~allies.mix
		~russian.mix
		~temperat.mix
		~snow.mix
		~interior.mix
		~./cnc/scores.mix
		~./ra/scores.mix
		~./ts/scores.mix
		~./ts/scores01.mix
		~./ra2/theme.mix
		~./ra2/thememd.mix
		~expand2.mix
		~hires1.mix
		~desert.mix
		ca|bits
		ca|bits/desert
		ca|bits/jungle
		ca|bits/winter
		ca|bits/barren
		ca|bits/temp
		ca|bits/int
		ca|bits/snow
		ca|bits/audio
		ca|bits/music
		ca|bits/scrin
		ca|bits/scrin/audio
		ca|bits/scrin/terrain
	RequiredContentFiles:
	ContentInstallerMod: ca-content

MapFolders:
	ca|maps: System
	~^SupportDir|maps/ca/prep-CA: User

LoadScreen: ImageLoadScreen
	Image: ca|uibits/ca-loading-artwork.png
	Image2x: ca|uibits/ca-loading-artwork-2x.png
	Image3x: ca|uibits/ca-loading-artwork-3x.png
	Width: 1024
	Height: 256
	Text: Loading...

Assemblies: OpenRA.Mods.Common.dll, OpenRA.Mods.Cnc.dll, OpenRA.Mods.CA.dll

FluentMessages:
	common|fluent/common.ftl
	common|fluent/chrome.ftl
	common|fluent/hotkeys.ftl
	common|fluent/rules.ftl
	ca|fluent/ca.ftl
	ca|fluent/chrome.ftl
	ca|fluent/factions.ftl
	ca|fluent/hotkeys.ftl
	ca|fluent/options.ftl
	ca|fluent/powers.ftl
	ca|fluent/rules.ftl

ServerTraits:
	LobbyCommands
	SkirmishLogic
	PlayerPinger
	MasterServerPinger
	LobbySettingsNotification

MapGrid:
	TileSize: 24,24
	Type: Rectangular

DefaultOrderGenerator: UnitOrderGenerator

SupportsMapsFrom: ca, ra

SoundFormats: Aud, Wav, Mp3, Ogg

SpriteFormats: ShpD2, R8, ShpTD, TmpRA, TmpTD, ShpTS

SpriteSequenceFormat: ClassicTilesetSpecificSpriteSequence
	TilesetCodes:
		TEMPERAT: .tem
		SNOW: .sno
		INTERIOR: .int
		DESERT: .des
		JUNGLE: .jun
		WINTER: .win
		BARREN: .bar
	TilesetExtensions:
		TEMPERAT: .tem
		SNOW: .sno
		INTERIOR: .int
		DESERT: .des
		JUNGLE: .jun
		WINTER: .win
		BARREN: .bar

VideoFormats: Vqa, Wsa

TerrainFormat: DefaultTerrain

AssetBrowser:
	SpriteExtensions: .shp, .tmp, .tem, .win, .sno, .des, .int, .jun, .r8
	AudioExtensions: .aud, .wav, .v00, .v01, .v02, .v03, .var
	VideoExtensions: .vqa, .wsa

GameSpeeds:
	DefaultSpeed: default
	Speeds:
		slowest:
			Name: options-game-speed.slowest
			Timestep: 80
			OrderLatency: 2
		slower:
			Name: options-game-speed.slower
			Timestep: 50
			OrderLatency: 3
		default:
			Name: options-game-speed.normal
			Timestep: 40
			OrderLatency: 3
		fast:
			Name: options-game-speed.fast
			Timestep: 35
			OrderLatency: 4
		faster:
			Name: options-game-speed.faster
			Timestep: 30
			OrderLatency: 4
		fastest:
			Name: options-game-speed.fastest
			Timestep: 20
			OrderLatency: 6

ModCredits:
	ModCreditsFile: ca|CREDITS
	ModTabTitle: Combined Arms

DiscordService:
	ApplicationId: 787647352399200277

Include: mod.content.yaml
Include: mod.chrome.yaml
