World:
	MissionData:
		Briefing: ================\n\n- Two teams must fight over five objectives.\n-------------------------\n- Players have a set number of credits, refunded when a unit is lost so it can be replaced.\n-------------------------\n- Faction choice has no effect.\n-------------------------\n- Any unit can capture an objective.\n-------------------------\n- Points are gained by holding objectives.\n-------------------------\n- First team to reach the configured score wins.\n\n================\n
	LuaScript:
		Scripts: tfca.lua
	StartingUnits@none:
		Class: none
		ClassName: None
		BaseActor: spawn
		Factions: allies, england, france, germany, usa, soviet, russia, ukraine, iraq, yuri, gdi, talon, zocom, eagle, arc, nod, blackh, marked, legion, shadow, scrin, reaper, traveler, harbinger, collector
	-StartingUnits@mcvonly:
	-StartingUnits@lightallies:
	-StartingUnits@lightsoviet:
	-StartingUnits@heavyallies:
	-StartingUnits@heavysoviet:
	-StartingUnits@mcvonly2:
	-StartingUnits@defaultgdia:
	-StartingUnits@defaultnoda:
	-StartingUnits@heavynoda:
	-StartingUnits@heavygdia:
	-StartingUnits@mcvonlyscrin:
	-StartingUnits@lightscrin:
	-StartingUnits@heavyscrin:
	SpawnStartingUnits:
		DropdownVisible: False
	MapBuildRadius:
		AllyBuildRadiusCheckboxVisible: False
		BuildRadiusCheckboxVisible: False
	MapOptions:
		TechLevelDropdownVisible: False
		ShortGameCheckboxEnabled: True
		ShortGameCheckboxLocked: True
		ShortGameCheckboxVisible: False
	CrateSpawner:
		CheckboxEnabled: False
		CheckboxLocked: True
		CheckboxVisible: False
	MapStartingLocations:
		SeparateTeamSpawnsCheckboxVisible: False
	TimeLimitManager:
		TimeLimitLocked: True
		TimeLimitDropdownVisible: False
	ScriptLobbyDropdown@UNITSPERPLAYER:
		ID: unitsperplayer
		Label: Units Per Player
		Description: Number of units each player can build
		Values:
			1: 1
			2: 2
			3: 3
			4: 4
			5: 5
			6: 6
			7: 7
			8: 8
			9: 9
			10: 10
			11: 11
			12: 12
			13: 13
			14: 14
			15: 15
		Default: 4
	ScriptLobbyDropdown@WINSCORE:
		ID: winscore
		Label: Score Required
		Description: Points required to win the game
		Values:
			1000: 1000
			1500: 1500
			2000: 2000
			2500: 2500
			3000: 3000
		Default: 1500

Player:
	ConquestVictoryConditions:
	PlayerResources:
		SelectableCash: 0
		DefaultCash: 0
		DefaultCashDropdownVisible: False
	LobbyPrerequisiteCheckbox@GLOBALBOUNTY:
		Enabled: False
		Locked: True
		Visible: False
	LobbyPrerequisiteCheckbox@FORCESHIELD:
		Enabled: False
		Visible: False
	DeveloperMode:
		CheckboxLocked: True
		CheckboxVisible: False
	LobbyPrerequisiteCheckbox@GLOBALFACTUNDEPLOY:
		Visible: False
	LobbyPrerequisiteCheckbox@NAVY:
		Visible: False
	LobbyPrerequisiteCheckbox@BALANCEDHARVESTING:
		Visible: False
	LobbyPrerequisiteCheckbox@FASTREGROWTH:
		Visible: False
	LobbyPrerequisiteCheckbox@REVEALONFIRE:
		Enabled: False
		Locked: True
		Visible: False
	LobbyPrerequisiteDropdown@QUEUETYPE:
		Default: global.singlequeue
		Visible: False
		Values:
			global.singlequeue: options-queuetype.singlequeue
	Shroud:
		ExploredMapCheckboxEnabled: True
		ExploredMapCheckboxVisible: True
		ExploredMapCheckboxLocked: False
	LobbyPrerequisiteCheckbox@SHOWNAMES:
		ID: shownames
		Label: Show Names
		Description: Show name tags above each unit
		Enabled: True
		DisplayOrder: 999
		Prerequisites: global.shownames
	ModularBot@StandardAI:
		Name: Bot
		Type: bot
	-ModularBot@BrutalAI:
	-ModularBot@VeryHardAI:
	-ModularBot@HardAI:
	-ModularBot@NormalAI:
	-ModularBot@EasyAI:
	-ModularBot@NavalAI:
	LobbyPrerequisiteCheckbox@BALANCEUNITS:
		ID: balanceunits
		Label: Balance Units
		Description: Give extra units to team with fewer players
		Enabled: True
		DisplayOrder: 999
		Prerequisites: global.balanceunits

SPAWN:
	Inherits: CAMERA
	Inherits@PROD: ^ProducesInfantry
	Tooltip:
		Name: Spawn Point
	Exit:
	ProvidesPrerequisite:
	WithSpriteBody:
	-RenderSpritesEditorOnly:
	RenderSprites:
		Image: shab
	WithIdleAnimation:
		Sequences: idle
		Interval: 25
	Production@SQINF:
		-PauseOnCondition:
	Production@MQINF:
		-PauseOnCondition:
	ProvidesRadar:
	ScriptTriggers:
	MustBeDestroyed:
		RequiredForShortGame: true

^Soldier:
	TakeCover:
		DamageModifiers:
			Prone50Percent: 75

^TFUnit:
	WithNameTagDecorationCA:
		Position: Top
		Font: Regular
		Margin: 0, -20
		ColorSource: Player
		ContrastColorLight: 000000
		ValidRelationships: Ally, Enemy, Neutral
		RequiresCondition: shownames
	GrantConditionOnPrerequisite@SHOWNAMES:
		Condition: shownames
		Prerequisites: global.shownames
	WithColoredSelectionBox@REL:
		ColorSource: Team
		ValidRelationships: Ally, Enemy, Neutral
	Buildable:
		Prerequisites: spawn
		BuildAtProductionType: Soldier
		BuildDuration: 5
		BuildDurationModifier: 100
	Valued:
		Cost: 1
	CaptureManager:
	Captures:
		CaptureTypes: building
		CaptureDelay: 225
		ConsumedByCapture: false
	ExternalCondition@SHIELDED:
		Condition: shielded
	WithColoredOverlay@SHIELDED:
		Color: 00ffbb99
		RequiresCondition: shielded
	DamageMultiplier@SHIELDED:
		RequiresCondition: shielded
		Modifier: 65
	ChangesHealth@SHIELDED:
		Step: 750
		Delay: 25
		StartIfBelow: 100
		RequiresCondition: shielded
	FirepowerMultiplier@ENGIBUFF:
		RequiresCondition: engibuff
		Modifier: 115
	ExternalCondition@ENGIBUFF:
		Condition: engibuff

XO:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		Description: Armoured suit with dual chainguns.
		BuildPaletteOrder: 6
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
	TooltipExtras:
		Strengths: • Durable\n• Self repairs
		Weaknesses: • Slow\n• Must wind up to full damage
	Mobile:
		Speed: 75
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 80000
	Armor:
		Type: None
	Armament@PRIMARY:
		Weapon: MGattG
		LocalOffset: 400,-220,180, 400,220,180
		MuzzleSequence: muzzle
	-Armament@SECONDARY:
	GrantConditionOnAttack@SPIN:
		ArmamentNames: primary
		Condition: spinning
		RequiredShotsPerInstance: 1,2,4
		MaximumInstances: 3
		RevokeDelay: 40
		RevokeOnNewTarget: False
		RevokeAll: True
	GrantConditionOnAttackCA@FIRE:
		ArmamentNames: primary
		Condition: firing
		RevokeDelay: 6
	FirepowerMultiplier@GAT1:
		Modifier: 115
		RequiresCondition: spinning == 1
	FirepowerMultiplier@GAT2:
		Modifier: 130
		RequiresCondition: spinning == 2
	FirepowerMultiplier@GAT3:
		Modifier: 145
		RequiresCondition: spinning >= 3
	AmbientSoundCA@ATTACKSOUND1:
		SoundFiles: vgatlo2a.aud, vgatlo2b.aud, vgatlo2c.aud
		RequiresCondition: firing && spinning == 1
	AmbientSoundCA@ATTACKSOUND2:
		InitialSound: vgatlo4a.aud
		SoundFiles: vgatlo5a.aud, vgatlo5b.aud
		RequiresCondition: firing && spinning == 2
	AmbientSoundCA@ATTACKSOUND3:
		InitialSound: vgatlo7a.aud
		SoundFiles: vgatlo8a.aud, vgatlo8b.aud
		RequiresCondition: firing && spinning >= 3
	Targetable@HEAL:
		TargetTypes: Heal
		RequiresCondition: damaged
	-Targetable@REPAIR:
	-GrantConditionOnHealingReceived@REPAIRCOOLDOWN:
	ChangesHealth@DEFAULT:
		PercentageStep: 1
		Delay: 25
		StartIfBelow: 100
		DamageCooldown: 150

E3:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		Description: Soldier armed with a rocket launcher.
		BuildPaletteOrder: 3
	TooltipExtras:
		Strengths: • Good all-round damage dealer\n• Guided projectiles\n• Decent HP
		Weaknesses: • Mediocre rate of fire\n• Slow projectiles
		Attributes: • Frenzy ability
	Mobile:
		Speed: 100
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 25000
	ReloadAmmoPool:
		Delay: 10
	Armament@PRIMARY:
		Weapon: Dragon
	-Armament@PRIMARYUPG:
	-Armament@SECONDARY:
	-Armament@SECONDARYUPG:
	-Armament@BATF:
	-Armament@BATFUPG:
	GrantTimedConditionOnDeploy:
		DeployedTicks: 125
		CooldownTicks: 1000
		DeployedCondition: frenzy
		ShowSelectionBar: true
		ChargingColor: 770000
		DischargingColor: ff0000
		StartsFullyCharged: true
		DeploySound: iteschaa.aud
		ShowSelectionBarWhenFull: false
	ReloadDelayMultiplier@FRENZY:
		Modifier: 40
	-ReloadDelayMultiplier@FRENZYDEBUFF:
	-SpeedMultiplier@FRENZYDEBUFF:
	GrantConditionOnBotOwner@ISBOT:
		Bots: bot
		Condition: is-bot
	AutoTargetPriority@BOTPRIO:
		RequiresCondition: is-bot
		ValidTargets: Defense
		Priority: 10

MEDI:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		BuildPaletteOrder: 5
	TooltipExtras:
		Attributes: • Can create protective healing zones
	Mobile:
		Speed: 100
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 20000
	SpawnActorAbility:
		Actors: shieldzone
		SkipMakeAnimations: false
		Range: 6c0
		CircleColor: 00ffbbaa
		SpawnSounds: srepaira.aud
		AmmoPool: shieldspawner
	AmmoPool@SHIELD:
		Name: shieldspawner
		Armaments: none
		Ammo: 1
	ReloadAmmoPoolCA@SHIELD:
		AmmoPool: shieldspawner
		Delay: 375
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: 00ffbb
		ReloadWhenAmmoReaches: 0

E4:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		Description: Soldier armed with a flamethrower.
		BuildPaletteOrder: 7
	TooltipExtras:
		Strengths: • High sustained damage
		Weaknesses: • Short range
		Attributes: • Pyroblast ability\n• Impervious to fire
	Mobile:
		Speed: 100
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 20000
	Armament:
		Weapon: HeavyFlameTankFlamer
		LocalOffset: 512,0,256
		PauseOnCondition: pyroblast-attack || pyroblast-fired
	Armament@SECONDARY:
		Name: secondary
		Weapon: Pyroblast
		LocalOffset: 512,0,256
		PauseOnCondition: !pyroblast-attack
	AmbientSoundCA:
		SoundFiles: flamer-loop1.aud
		InitialSound: flamer-start1.aud
		FinalSound: flamer-end1.aud
		RequiresCondition: attacking
		InitialSoundLength: 20
	GrantConditionOnAttackCA:
		Condition: attacking
		RevokeDelay: 5
	TargetedAttackAbility:
		ActiveCondition: pyroblast-attack
		ArmamentNames: secondary
		CircleColor: ff6600aa
		Type: Pyroblast
	GrantConditionOnAttack@PYROBLASTCOOLDOWN:
		Condition: pyroblast-fired
		RevokeDelay: 50
		ArmamentNames: secondary
	AmmoPool:
		Armaments: secondary
		Ammo: 1
	ReloadAmmoPoolCA:
		Delay: 375 # equal to reload time of weapon
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: ff6600
	Targetable@FIREPROOF:
		TargetTypes: Fireproof

SEAL:
	Inherits@TFUNIT: ^TFUnit
	Tooltip:
		Name: Navy SEAL (Scout)
	Buildable:
		Description: Fast soldier armed with a submachinegun.
		BuildPaletteOrder: 1
	TooltipExtras:
		Strengths: • Very fast\n• Large vision radius
		Weaknesses: • Mediocre damage\n• Fragile
		Attributes: • Detects cloaked units\n• Teleport ability
	Mobile:
		Speed: 150
		Locomotor: foot
		-Voice:
	RevealsShroud:
		Range: 12c0
	Health:
		HP: 15000
	Voiced:
		VoiceSet: GenericVoice
	Armament:
		Weapon: Uzi
		LocalOffset: 427,0,341
	-WithDecoration@COMMANDOSKULL:
	-Armament@C4Place:
	-Armament@C4Prepare:
	-AmmoPool@PreparedC4:
	-ReloadAmmoPoolCA@PreparedC4:
	-ReloadAmmoPoolCA@CancelC4:
	-GrantConditionOnAttack@PreparingC4:
	-Demolition:
	-TargetSpecificOrderVoice:
	Guard:
		-Voice:
	Passenger:
		-Voice:
	AttackMove:
		-Voice:
	AttackFrontal:
		Voice: Action
	-AnnounceOnKill:
	PortableChronoCA:
		ChargeDelay: 375
		MaxDistance: 12
		ChronoshiftSound: scrinport.aud
		TeleportCondition: blink
		ConditionDuration: 3
		SelectionBarColor: eeeeff
		ShowSelectionBarWhenFull: false
	WithColoredOverlay@BLINKFLASH:
		Color: ffffff80
		RequiresCondition: blink

E6:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		Description: Soldier that can deploy and repair turrets.
		BuildPaletteOrder: 9
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Detects mines\n• Nearby allies deal 15% more damage
	Mobile:
		Speed: 100
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 20000
	Selectable:
		Priority: 10
	SpawnActorAbility:
		Actors: gun
		SkipMakeAnimations: false
		Range: 5c0
		CircleColor: 999999
		SpawnSounds: placbldg.aud, build5.aud
		AmmoPool: turretspawner
		AvoidActors: true
	AmmoPool@TURRET:
		Name: turretspawner
		Armaments: none
		Ammo: 1
	ReloadAmmoPoolCA@TURRET:
		AmmoPool: turretspawner
		Delay: 750
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: FFAA00
		ReloadWhenAmmoReaches: 0
	WithAmmoPipsDecoration@TURRET:
		AmmoPools: turretspawner
		RequiresSelection: true
		Position: BottomLeft
		Margin: 4, 3
	Captures:
		CaptureDelay: 150
	Armament:
		Weapon: Repair
		Cursor: repair
		OutsideRangeCursor: repair
		TargetRelationships: Ally
		ForceTargetRelationships: None
	-Armament@bombdefuser:
	-Armament@minedefuser:
	-Armament@minedefusercharge:
	-AmmoPool@minedefuser:
	-ReloadAmmoPoolCA@minedefuser:
	-GrantConditionOnAttack@CHARGING:
	-AutoTargetPriority@defuse:
	-AutoTargetPriority@mines:
	AttackFrontal:
		Voice: Action
		PauseOnCondition: being-warped
		FacingTolerance: 0
	AttackSoundsCA@REPAIRSOUND:
		Sounds: fixit1.aud
	AutoTarget:
	AutoTargetPriority@DEFAULT:
		ValidTargets: Repair
		ValidRelationships: Ally
	-InstantlyRepairs:
	WithInfantryBody:
		DefaultAttackSequence: shoot
	MineImmune:
	DetectCloaked:
		Range: 5c0
		DetectionTypes: Mine
	WithRangeCircle:
		Color: ffff00aa
		Range: 3c512
		ValidRelationships: Ally
	ProximityExternalCondition@ENGIBUFF:
		Range: 3c512
		Condition: engibuff
		ValidRelationships: Ally

SAB:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		Description: Stealth infantry.
		BuildPaletteOrder: 8
		Queue: InfantrySQ, InfantryMQ
		BuildAtProductionType: Soldier
	TooltipExtras:
		Strengths: • High single-target burst damage
		Weaknesses: • Short range\n• Fragile
		Attributes: • Invisible when not attacking\n• Remote reveal ability
	Mobile:
		Speed: 100
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 15000
	Selectable:
		Priority: 10
	-GrantChargingCondition@CLOAK:
	-GrantConditionOnMovement:
	Cloak:
		RequiresCondition: !cloak-force-disabled && !being-warped
		CloakDelay: 66
	SpawnActorAbility:
		Actors: spyreveal
		SkipMakeAnimations: false
		SpawnSounds: hacksat.aud
		SelectTargetSpeechNotification: SelectTarget
		AmmoPool: revealspawner
	AmmoPool@REVEALSPAWNER:
		Name: revealspawner
		Armaments: none
		Ammo: 1
	ReloadAmmoPoolCA@REVEALSPAWNER:
		AmmoPool: revealspawner
		Delay: 375
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: ffffff
	WithAmmoPipsDecoration@REVEALSPAWNER:
		AmmoPools: revealspawner
		RequiresSelection: true
		Position: BottomLeft
		Margin: 4, 3
	AutoTarget:
		ScanRadius: 7

IVAN:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		Description: Soldier armed with bombs and mines.
		BuildPaletteOrder: 4
	TooltipExtras:
		Strengths: • High area damage
		Weaknesses: • Has difficulty hitting moving targets
		Attributes: • Lays mines
	Mobile:
		Speed: 125
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 20000
	Minelayer:
		Mine: MINV
		TileUnknownName: build-valid
	MineImmune:
	AmmoPool:
		Ammo: 1
		Armaments: none
	WithAmmoPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		FullSequence: pip-red
	ReloadAmmoPoolCA:
		Delay: 250
		Count: 1
		ShowSelectionBar: true
		SelectionBarColor: ff6600
	Rearmable:
		RearmActors: ivan.rearm
	GrantConditionOnBotOwner@ISBOT:
		Bots: bot
		Condition: is-bot
	Armament@PRIMARY:
		RequiresCondition: !is-bot
	Armament@PRIMARYBOT:
		PauseOnCondition: reloading
		Weapon: BotIvanbomb
		LocalOffset: 0,0,555
		FireDelay: 15
		RequiresCondition: is-bot
	Armament@PRIMARYBOTDEF:
		Weapon: BotIvanbombDef
		LocalOffset: 0,0,555
		FireDelay: 15
		ReloadingCondition: reloading
		RequiresCondition: is-bot
	AutoTargetPriority@DEFAULTDEF:
		RequiresCondition: is-bot
		ValidTargets: Defense
		Priority: 10

# ---- dummy actor because RearmableActors is required
IVAN.REARM:
	AlwaysVisible:

SNIP:
	Inherits@TFUNIT: ^TFUnit
	Buildable:
		Description: Soldier armed with a sniper rifle.
		BuildPaletteOrder: 2
	TooltipExtras:
		Strengths: • Long range\n• High single-target damage
		Weaknesses: • Fragile\n• Slow
		Attributes: • Must aim before shooting
	Mobile:
		Speed: 75
	RevealsShroud:
		Range: 8c0
	Health:
		HP: 10000
	AttackFrontalCharged:
		ChargeLevel: 50
		Armaments: primary
	-Cloak@NORMAL:
	-WithDecoration@hidden:
	-WithColoredSelectionBox@INVIS:
	Cloak@CRATE-CLOAK:
		RequiresCondition: (crate-cloak) && !(cloak-force-disabled || invisibility)
	Armament@PRIMARY:
		Weapon: sniper
		RequiresCondition: !is-bot
	Armament@PRIMARYBOT:
		Weapon: botsniper
		RequiresCondition: is-bot
	GrantConditionOnBotOwner@ISBOT:
		Bots: bot
		Condition: is-bot

ATEK:
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-Targetable:
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None
	Power:
		Amount: 0
	-DummyGpsPower@NOFOG:
	-DummyGpsPower@FOG:
	-ProduceActorPowerCA@SatelliteLaunched:
	-SupportPowerChargeBar:
	-ProduceActorPowerCA@InitialSatelliteScan:
	-ProduceActorPowerCA@SatelliteScan:
	-ProduceActorPowerCA@SatelliteScanNoFog:
	-GrantExternalConditionPowerCA@FSHIELD:
	-DetonateWeaponPower@TEMPINC:
	CaptureManager:
		-BeingCapturedCondition:

PROC:
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-Targetable:
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None
	Power:
		Amount: 0
	CaptureManager:
		-BeingCapturedCondition:

DOME:
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-Targetable:
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None
	Power:
		Amount: 0
	-AirstrikePowerCA@clustermines:
	-SpawnActorPowerCA@VeilOfWar:
	-ParatroopersPowerCA@paratroopers:
	-ParatroopersPowerCA@Russianparatroopers:
	-AirstrikePowerCA@spyplane:
	-Targetable@INFILTRATION:
	CaptureManager:
		-BeingCapturedCondition:
	-RangedGpsRadarProvider:
	-WithRangeCircle:

HQ:
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-Targetable:
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None
	Power:
		Amount: 0
	-Targetable@INFILTRATION:
	CaptureManager:
		-BeingCapturedCondition:
	-RangedGpsRadarProvider:
	-WithRangeCircle:
	-SpawnActorPowerCA@sathack:
	-SpawnActorPowerCA@sathacklegion:
	-DropPodsPowerCA@Zocom:
	-CashHackPower@Legion:
	-AirstrikePowerCA@BlackhandFirebomb:
	-AirReinforcementsPower@ShadowTeam:

APWR:
	-Sellable:
	-SpawnActorsOnSellCA:
	-SpawnActorOnDeath:
	-SpawnRandomActorOnDeath:
	-Targetable:
	Targetable@TEMPORAL:
		TargetTypes: None
	Targetable@HACKABLE:
		TargetTypes: None
	Power:
		Amount: 0
	-Targetable@PowerOutageInfiltrate:
	CaptureManager:
		-BeingCapturedCondition:

GUN:
	Health:
		HP: 50000
	Armament:
		Weapon: TurretUzi
	Power:
		Amount: 0
	-RepairableBuilding:
	-WithBuildingRepairDecoration:
	-Sellable:
	Targetable@REPAIR:
		RequiresCondition: damaged && !being-warped
		TargetTypes: Repair
	GrantConditionOnDamageState@DAMAGED:
		Condition: damaged
		ValidDamageStates: Light, Medium, Heavy, Critical
	WithColoredSelectionBox@REL:
		ColorSource: Team
		ValidRelationships: Ally, Enemy, Neutral
	-ChangesHealth@DefensePolicy2:

SPAWNGUN:
	Inherits: GUN
	RenderSprites:
		Image: gun
	MustBeDestroyed:
		RequiredForShortGame: true
	WithColoredSelectionBox@REL:
		ColorSource: Player
	-Targetable:
	-Targetable@C4Plantable:
	-Targetable@TNTPlantable:

SHIELDZONE:
	Inherits@1: CAMERA
	-RevealsShroud:
	WithRangeCircle:
		Visible: Always
		Color: 00ffbbaa
		Range: 3c0
		ValidRelationships: Ally
	ProximityExternalCondition@SHIELD:
		Range: 3c0
		Condition: shielded
		ValidRelationships: Ally
	KillsSelf:
		RemoveInstead: true
		Delay: 200

SPYREVEAL:
	Inherits: camera.sathack
	RevealsShroud:
		Range: 5c0
	RenderSprites:
		Image: satscansm
	KillsSelf:
		RemoveInstead: true
		Delay: 75

PYROZONE:
	Inherits@1: CAMERA
	-RevealsShroud:
	WithRangeCircle:
		Visible: Always
		Color: ff6600aa
		Range: 3c0
		ValidRelationships: Enemy, Ally
	KillsSelf:
		RemoveInstead: true
		Delay: 200
	PeriodicExplosion:
		Weapon: PyroZone
