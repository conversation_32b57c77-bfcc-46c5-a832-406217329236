^VehicleOverlays:
	emp-overlay:
		Filename: emp_fx01.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
	chrono-overlay:
		Filename: chronofade.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
	mind-overlay:
		Filename: mindanim.shp
		Length: *
		Tick: 200
		BlendMode: Alpha
		Offset: 0, 0
		ZOffset: 1024

mcv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mcv.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: mcvicon.shp

mcvhusk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mcvhusk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -1023

truk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: truk.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: trukicon.shp

harv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harv.shp
		Facings: 32
		UseClassicFacings: True
	harvest:
		Filename: harv.shp
		Start: 32
		Length: 8
		Facings: 8
	dock:
		Filename: harv.shp
		Start: 96
		Length: 8
	dock-loop:
		Filename: harv.shp
		Start: 104
		Length: 7
	icon:
		Filename: harvicon.shp

harvempty:
	Inherits: harv

harvhalf:
	Inherits: harv

hhusk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hhusk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -1023

hhusk2:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hhusk2.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -1023

charv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: charv.shp
		Facings: 32
		UseClassicFacings: True
	harvest:
		Filename: charv.shp
		Start: 32
		Length: 8
		Facings: 8
	dock:
		Filename: charv.shp
		Start: 96
		Length: 8
	dock-loop:
		Filename: charv.shp
		Start: 104
		Length: 7
	icon:
		Filename: charvicon.shp

charv.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: charv.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

1tnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 1tnk.shp
		Facings: 32
		UseClassicFacings: True
	idle-water:
		Filename: 1tnkwater.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 1tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 2
	icon:
		Filename: 1tnkicon.shp

1tnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 1tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 1tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

2tnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 2tnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 2tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 2tnkicon.shp

gtnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: gtnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 2tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: gtnkicon.shp

mtnk.gdi.chrono:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 2tnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: mtnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: mtnkicnh.shp

mtnk.nod.chrono:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 2tnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: mtnknod.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: mtnkicnh.shp

2tnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 2tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 2tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

gtnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: gtnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 2tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

3tnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 3tnk.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: 3tnki.shp
		Facings: 32
		UseClassicFacings: True
	idle3:
		Filename: 3tnky.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 3tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret2:
		Filename: 3tnky.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 3tnkicon.shp

3tnki:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 3tnki.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 3tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 3tnkiicon.shp

3tnky:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 3tnky.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: 3tnki.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 3tnky.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 3tnkyicon.shp

3tnkay:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 3tnky.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: 3tnkay.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 3tnky.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 3tnkayicon.shp

3tnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 3tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 3tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

3tnky.destroyed:
	Inherits: 3tnky
	idle:
		Filename: 3tnky.shp
		ZOffset: -512
	turret:
		Filename: 3tnky.shp
		Start: 32
		ZOffset: -512

4tnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 4tnk.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: 4tnki.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 4tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret2:
		Filename: 4tnki.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 4tnkicon.shp

4tnki:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 4tnki.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 4tnki.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: 4tnkiicon.shp

4tnkerad:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 4tnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 4tnkerad.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: radhitsm.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: *
	icon:
		Filename: 4tnkeradicon.shp

4tnkeradi:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 4tnki.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: 4tnkerad.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: radhitsm.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: *
	icon:
		Filename: 4tnkeradiicon.shp

4tnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 4tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 4tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

4tnkerad.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: 4tnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 4tnkerad.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

v2rl:
	Inherits: ^VehicleOverlays
	idle:
		Filename: v2rl.shp
		Facings: 32
		UseClassicFacings: True
	empty-idle:
		Filename: v2rl.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	aim:
		Filename: v2rl.shp
		Facings: 32
		UseClassicFacings: True
	empty-aim:
		Filename: v2rl.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: v2rlicon.shp

arty:
	Inherits: ^VehicleOverlays
	idle:
		Filename: arty.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	move:
		Filename: arty.shp
		Facings: 32
		Length: 1
		UseClassicFacings: True
	icon:
		Filename: artyicon.shp

artynod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: artynod.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: artyicnh.shp

howi:
	Inherits: ^VehicleOverlays
	idle:
		Filename: howi.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: howi.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: howiicnh.shp

jeep:
	Inherits: ^VehicleOverlays
	idle:
		Filename: jeep.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: jeep.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: jeepicon.shp

apc:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apc.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	turret:
		Filename: rapc.shp
		Facings: 32
		UseClassicFacings: True
	muzzle2:
		Filename: gunfire2.shp
		Length: 5
	open:
		Filename: apc.shp
		Start: 32
		Length: 3
	unload:
		Filename: apc.shp
		Start: 32
	icon:
		Filename: apcicon.shp

rapc:
	Inherits: apc
	icon:
		Filename: rapcicon.shp

apc.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apc.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	turret:
		Filename: jeep.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True

mnly:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mnly.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: mnlyicon.shp

mrj:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mrj.shp
		Facings: 32
		UseClassicFacings: True
	spinner:
		Filename: mrj.shp
		Start: 32
		Length: 32
	jamsignal:
		Filename: jamsignal.shp
		Length: *
		BlendMode: Alpha
		Alpha: 0.3
	icon:
		Filename: mrjicon.shp

mgg:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mgg.shp
		Facings: 32
		UseClassicFacings: True
	spinner:
		Filename: mgg.shp
		Start: 32
		Length: 8
	spinner-idle:
		Filename: mgg.shp
		Start: 32
		Length: 1
	muzzle:
		Filename: gapmuzzle.shp
		Frames: 7,8,9,10,11,12,13,14,15,16,17,18
		BlendMode: Subtractive
		Tick: 60
		Length: *
		ZOffset: 2048
	icon:
		Filename: mggicon.shp

mgg.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mgg.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	spinner:
		Filename: mgg.shp
		Start: 32
		Length: 8
		ZOffset: -512
	spinner-idle:
		Filename: mgg.shp
		Start: 32

msg:
	Inherits: ^VehicleOverlays
	idle:
		Filename: msg.shp
		Facings: 32
		UseClassicFacings: True
	make:
		Filename: msg.shp
		Start: 32
		Length: 10
		Tick: 80
	idle-deployed:
		Filename: msg.shp
		Frames: 42,43,44,45,46,47,48,48,47,46,45,44,43,42
		Length: 14
		Tick: 120
	icon:
		Filename: msgicon.shp

msg.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: msg.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ttra:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ttnk.shp
		Facings: 32
		UseClassicFacings: True
	spinner:
		Filename: ttnk.shp
		Start: 32
		Length: 32
	icon:
		Filename: ttraicon.shp

ttra.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ttnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ttnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ttnk2.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: ttnk2.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: litningmuzzle.shp
		Length: 8
	icon:
		Filename: ttnk2icon.shp

ttnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ttnk2.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: ttnk2.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

dtrk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: dtrk.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: dtrkicon.shp

ctnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ctnk.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: ctnkicon.shp

qtnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: qtnk.shp
		Facings: 32
		UseClassicFacings: True
	piston:
		Filename: qtnk.shp
		Start: 32
		Facings: 8
		Length: 8
	icon:
		Filename: qtnkicon.shp

apc2:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apc2.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	turret:
		Filename: hmmv.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret2:
		Filename: vulc.shp
		Facings: 32
		UseClassicFacings: True
	turret2-ground:
		Filename: vulc.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	open:
		Filename: apc2.shp
		Start: 32
		Length: 3
	unload:
		Filename: apc2.shp
		Start: 35
		Length: 3
	icon:
		Filename: apc2icnh.shp

apc2.nod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apc2.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	turret:
		Filename: hmmv.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret2:
		Filename: vulc.shp
		Facings: 32
		UseClassicFacings: True
	turret2-ground:
		Filename: vulc.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	open:
		Filename: apc2.shp
		Start: 32
		Length: 3
	unload:
		Filename: apc2.shp
		Start: 35
		Length: 3
	icon:
		Filename: apc2nodicnh.shp

apc2.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apc2.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: jeep.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True

vulc:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apc2.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16big.shp
		Length: 6
		Facings: 16
	muzzle-huge:
		Filename: minigun16huge.shp
		Length: 6
		Facings: 16
	turret:
		Filename: vulc.shp
		Facings: 32
		UseClassicFacings: True
	turret-ground:
		Filename: vulc.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	open:
		Filename: apc2.shp
		Start: 32
		Length: 3
	unload:
		Filename: apc2.shp
		Start: 35
		Length: 3
	icon:
		Filename: vulcicnh.shp

amcv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: amcv.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: amcvicnh.shp

amcv.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: amcv.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -1023

hmmv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hmmv.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: hmmv.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turrettow:
		Filename: hmmvtow.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	unload:
		Filename: hmmv.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: jeepicnh.shp

hmmv.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hmmv.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: hmmv.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

gdrn:
	Inherits: ^VehicleOverlays
	idle:
		Filename: gdrn.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: gdrn.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turrettow:
		Filename: gdrn.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: gdrnicon.shp

bggy:
	Inherits: ^VehicleOverlays
	idle:
		Filename: bggy.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: bggy.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	unload:
		Filename: bggy.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: bggyicnh.shp

bggy.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: bggy.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: bggy.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

mtnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mtnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: mtnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turretpd:
		Filename: mtnk.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	turretlas:
		Filename: mtnknod.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	muzzlelas:
		Filename: lasermuzzle.shp
		Length: *
	icon:
		Filename: mtnkicnh.shp

mtnk.nod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mtnknod.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: mtnknod.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turretpd:
		Filename: mtnk.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	turretlas:
		Filename: mtnknod.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	muzzlelas:
		Filename: lasermuzzle.shp
		Length: *
	icon:
		Filename: mtnknodicnh.shp

mtnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mtnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: mtnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turretpd:
		Filename: mtnk.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

drone:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mtnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: dronet.shp
		Facings: 32
		UseClassicFacings: True
	turretpd:
		Filename: dronet.shp
		Facings: 32
		Start: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	spinner:
		Filename: drad.shp
		Length: *
	icon:
		Filename: droneicnh.shp

drone.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mtnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: dronet.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

mdrn:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mdrn.shp
		Facings: 1
		Start: 64
	turret:
		Filename: mdrn.shp
		Facings: 32
		UseClassicFacings: True
	turret-attached:
		Filename: mdrn.shp
		Facings: 32
		Start: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	icon:
		Filename: mdrnicon.shp

htnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: htnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: htnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	idle-hover:
		Filename: htnkhover.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: htnkicnh.shp

htnk.ion:
	Inherits: ^VehicleOverlays
	idle:
		Filename: htnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: htnktion.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: htnkionicnh.shp

htnk.hover:
	Inherits: ^VehicleOverlays
	idle:
		Filename: htnkhover.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: htnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: htnkhovericnh.shp

htnk.drone:
	Inherits: ^VehicleOverlays
	idle:
		Filename: htnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: mdrone.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: mdroneicnh.shp

htnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: htnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	idle-hover:
		Filename: htnkhover.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: htnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret-ion:
		Filename: htnktion.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret-drone:
		Filename: mdrone.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

msam:
	Inherits: ^VehicleOverlays
	idle:
		Filename: msam.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: msam.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	empty-aim:
		Filename: msam.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	aim:
		Filename: msam.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: msamicnh.shp

msam.nod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: msam.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: msam.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	empty-aim:
		Filename: msam.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	aim:
		Filename: msam.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: msamnodicnh.shp

msam.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: msam.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: msam.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

mlrs:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mlrs.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: mlrs.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret1:
		Filename: mlrs.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	turret0:
		Filename: mlrs.shp
		Start: 96
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: mlrsicnh.shp

mlrs.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mlrs.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: mlrs.shp
		Start: 96
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

stnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: stnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: stnk.shp
		Start: 38
		Facings: 32
	icon:
		Filename: stnkicon.shp

stnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: stnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: stnk.shp
		Start: 38
		Facings: 32

stnktnod:
	Inherits: ^VehicleOverlays
	idle:
		Filename: stnktnod.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: stnktnod.shp
		Start: 32
		Facings: 32
	turret-upg:
		Filename: stnktupg.shp
		Facings: 32
	icon:
		Filename: stnkicnh.shp

stnknod.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: stnknod.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

hstk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hstk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: hstk.shp
		Start: 32
		Facings: 32
	turret-upg:
		Filename: hstktupg.shp
		Facings: 32
	icon:
		Filename: hstkicnh.shp

hstk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hstk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: hstk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

bike:
	Inherits: ^VehicleOverlays
	idle:
		Filename: bike.shp
		Facings: 32
		UseClassicFacings: True
	idle-upgrade:
		Filename: bikeupg.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: bikeicnh.shp

bike.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: bike.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ftnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ftnk.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Combine:
			0:
				Filename: flame-n.shp
				Length: *
				Offset: 3,6
			1:
				Filename: flame-nw.shp
				Length: *
				Offset: 8,7
			2:
				Filename: flame-w.shp
				Length: *
				Offset: 8,2
			3:
				Filename: flame-sw.shp
				Length: *
				Offset: 7,-2
			4:
				Filename: flame-s.shp
				Length: *
				Offset: 3,-2
			5:
				Filename: flame-se.shp
				Length: *
				Offset: -5,-2
			6:
				Filename: flame-e.shp
				Length: *
				Offset: -7,2
			7:
				Filename: flame-ne.shp
				Length: *
				Offset: -7,8
		Facings: 8
		Length: 13
	icon:
		Filename: ftnkicnh.shp

ftnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ftnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

hftk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hftk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: hftk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Combine:
			0:
				Filename: flame-n.shp
				Length: *
				Offset: 3,6
			1:
				Filename: flame-nw.shp
				Length: *
				Offset: 8,7
			2:
				Filename: flame-w.shp
				Length: *
				Offset: 8,2
			3:
				Filename: flame-sw.shp
				Length: *
				Offset: 7,-2
			4:
				Filename: flame-s.shp
				Length: *
				Offset: 3,-2
			5:
				Filename: flame-se.shp
				Length: *
				Offset: -5,-2
			6:
				Filename: flame-e.shp
				Length: *
				Offset: -7,2
			7:
				Filename: flame-ne.shp
				Length: *
				Offset: -7,8
		Facings: 8
		Length: 13
	muzzle2:
		Filename: flameblack.shp
		Length: 13
		Facings: 8
	icon:
		Filename: hftkicnh.shp

hftk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hftk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: hftk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ltnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ltnk.shp
		Facings: 32
		UseClassicFacings: True
	idle-water:
		Filename: ltnkwater.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: ltnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turretlas:
		Filename: ltnk.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 2
	muzzlelas:
		Filename: lasermuzzle.shp
		Length: *
	icon:
		Filename: ltnkicnh.shp

ltnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ltnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: ltnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

harv2:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harv2.shp
		Facings: 32
		UseClassicFacings: True
	harvest:
		Filename: harv2.shp
		Start: 32
		Length: 4
		Facings: 8
		Tick: 60
	dock:
		Filename: harv2dump.shp
		Length: 7
	dock-loop:
		Filename: harv2dump.shp
		Start: 7
	icon:
		Filename: harv2icnh.shp

harv2.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harv2.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

harv2.upg:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harv2upg.shp
		Facings: 32
		UseClassicFacings: True
	harvest:
		Filename: harv2upg.shp
		Start: 32
		Length: 4
		Facings: 8
		Tick: 60
	dock:
		Filename: harv2dumpupg.shp
		Length: 7
	dock-loop:
		Filename: harv2dumpupg.shp
		Start: 7
	icon:
		Filename: harv2upgicnh.shp

harv2.upg.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: harv2upg.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

btr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: btr.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: btrt.shp
		Facings: 32
		UseClassicFacings: True
	turret-upg:
		Filename: btrtr.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		ZOffset: 2048
		IgnoreWorldTint: true
		Length: 2
	icon:
		Filename: btricon.shp

btry:
	Inherits: ^VehicleOverlays
	idle:
		Filename: btr.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: gata.shp
		Facings: 32
		UseClassicFacings: True
	turret-ground:
		Filename: gatl.shp
		Facings: 32
		UseClassicFacings: True
	turret-ground-upg:
		Filename: gatlr.shp
		Facings: 32
		UseClassicFacings: True
	turret-air-upg:
		Filename: gatar.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16big.shp
		ZOffset: 2048
		IgnoreWorldTint: true
		Length: 6
		Facings: 16
	muzzle-huge:
		Filename: minigun16huge.shp
		ZOffset: 2048
		IgnoreWorldTint: true
		Length: 6
		Facings: 16
	icon:
		Filename: btryicon.shp

btr.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: btr.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: samtur.shp
		Facings: 32
		UseClassicFacings: True

ifv:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ifv.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	muzzle2:
		Filename: gunfire2.shp
		Length: 2
	spinner:
		Filename: mrj.shp
		Start: 32
		Length: 32
	medic:
		Filename: mouse.shp
		Start: 194
		Length: 1
	nuke:
		Filename: nsymbol.shp
	tesla:
		Filename: bttnk.shp
		Start: 32
		Length: 32
	psy:
		Filename: ifvtur.shp
		Start: 576
		Length: 1
	hack:
		Filename: ifvtur.shp
		Start: 577
		Length: 1
	turret:
		Filename: ifvtur.shp
		Facings: 32
		UseClassicFacings: True
	turret-rocket:
		Filename: ifvtur.shp
		Start: 320
		Facings: 32
		UseClassicFacings: True
	turret-mg:
		Filename: ifvtur.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	turret-frag:
		Filename: ifvtur.shp
		Start: 96
		Facings: 32
		UseClassicFacings: True
	turret-flame:
		Filename: ifvtur.shp
		Start: 128
		Facings: 32
		UseClassicFacings: True
	turret-chem:
		Filename: ifvtur.shp
		Start: 160
		Facings: 32
		UseClassicFacings: True
	turret-laser:
		Filename: ifvtur.shp
		Start: 192
		Facings: 32
		UseClassicFacings: True
	turret-rad:
		Filename: ifvtur.shp
		Start: 224
		Facings: 32
		UseClassicFacings: True
	turret-cmdo:
		Filename: ifvtur.shp
		Start: 256
		Facings: 32
		UseClassicFacings: True
	turret-mech:
		Filename: ifvtur.shp
		Start: 288
		Facings: 32
		UseClassicFacings: True
	turret-snip:
		Filename: ifvtur.shp
		Start: 352
		Facings: 32
		UseClassicFacings: True
	turret-enli:
		Filename: ifvtur.shp
		Start: 384
		Facings: 32
		UseClassicFacings: True
	turret-rmbc:
		Filename: ifvtur.shp
		Start: 416
		Facings: 32
		UseClassicFacings: True
	turret-bh:
		Filename: ifvtur.shp
		Start: 448
		Facings: 32
		UseClassicFacings: True
	turret16:
		Filename: ifvtur.shp
		Start: 480
		Facings: 32
		UseClassicFacings: True
		Offset: 0, 1
	turret-mort:
		Filename: ifvtur.shp
		Start: 512
		Facings: 32
		UseClassicFacings: True
	turret-ggi:
		Filename: ifvtur.shp
		Start: 544
		Facings: 32
		UseClassicFacings: True
	turret-cryo:
		Filename: ifvtur.shp
		Start: 578
		Facings: 32
		UseClassicFacings: True
	turret-enfo:
		Filename: ifvtur.shp
		Start: 610
		Facings: 32
		UseClassicFacings: True
	turret-engi:
		Filename: ifvtur.shp
		Start: 642
		Facings: 32
		UseClassicFacings: True
	turret-ztrp:
		Filename: ifvtur.shp
		Start: 674
		Facings: 32
		UseClassicFacings: True
	turret-zdef:
		Filename: ifvtur.shp
		Start: 706
		Facings: 32
		UseClassicFacings: True
	turret-zrai:
		Filename: ifvtur.shp
		Start: 738
		Facings: 32
		UseClassicFacings: True
	turret-tigr:
		Filename: ifvtur.shp
		Start: 770
		Facings: 32
		UseClassicFacings: True
	turret-disin:
		Filename: ifvtur.shp
		Start: 802
		Facings: 32
		UseClassicFacings: True
	turret-shard:
		Filename: ifvtur.shp
		Start: 834
		Facings: 32
		UseClassicFacings: True
	turret-pdisc:
		Filename: ifvtur.shp
		Start: 866
		Facings: 32
		UseClassicFacings: True
	turret-impl:
		Filename: ifvtur.shp
		Start: 898
		Facings: 32
		UseClassicFacings: True
	turret-stlk:
		Filename: ifvtur.shp
		Start: 930
		Facings: 32
		UseClassicFacings: True
	turret-conf:
		Filename: ifvtur.shp
		Start: 962
		Facings: 32
		UseClassicFacings: True
	turret-reap:
		Filename: ifvtur.shp
		Start: 994
		Facings: 32
		UseClassicFacings: True
	turret-hopl:
		Filename: ptnkt.shp
		Facings: 32
		UseClassicFacings: True
		Offset: 0, -1
	turret15:
		Filename: seek.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		Offset: 0, 1
	open:
		Filename: ifv.shp
		Start: 32
		Length: 3
	unload:
		Filename: ifv.shp
		Start: 32
	icon:
		Filename: ifvicon.shp

ifv.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ifv.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: 1tnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True

xo:
	Inherits: ^VehicleOverlays
	Defaults:
		Offset: 0, 0
	idle:
		Filename: xo.shp
		Facings: 8
	move:
		Filename: xo.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 200
	shoot:
		Filename: xo.shp
		Start: 40
		Length: 2
		Facings: 8
		Tick: 40
	descend:
		Filename: xo.shp
		Start: 56
		Facings: 8
		Length: 3
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	muzzle2:
		Filename: scrinmuzz4.shp
		Length: *
		Tick: 40
		ZOffset: 2049
		BlendMode: Additive
		IgnoreWorldTint: true
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		ZOffset: 512
		Tick: 80
	icon:
		Filename: xoicon.shp

wolv:
	Inherits: ^VehicleOverlays
	Defaults:
		Offset: 0, 0
	idle:
		Filename: wolv.shp
		Facings: 8
	move:
		Filename: wolv.shp
		Start: 8
		Length: 4
		Facings: 8
		Tick: 200
	shoot:
		Filename: wolv.shp
		Start: 40
		Length: 2
		Facings: 8
		Tick: 40
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		ZOffset: 512
		Tick: 80
	icon:
		Filename: wolvicnh.shp

titn:
	Inherits: ^VehicleOverlays
	stand:
		Filename: titanlegs.shp
		Facings: 16
	run:
		Filename: titanlegs.shp
		Start: 16
		Facings: 16
		Length: 14
		Tick: 90
	turret:
		Filename: titanturr.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: titanicnh.shp

titn.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: titanlegs.shp
		Facings: 16
		ZOffset: -512
	turret:
		Filename: titanturr.shp
		Facings: 32
		UseClassicFacings: True

titn.rail:
	Inherits: titn
	turret:
		Filename: titantrail.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: titanrailicnh.shp

titn.rail.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: titanlegs.shp
		Facings: 16
		ZOffset: -512
	turret:
		Filename: titantrail.shp
		Facings: 32
		UseClassicFacings: True

jugg:
	Inherits: ^VehicleOverlays
	stand:
		Filename: titanlegs.shp
		Facings: 16
	run:
		Filename: titanlegs.shp
		Start: 16
		Facings: 16
		Length: 14
		Tick: 90
	turret:
		Filename: juggt.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: juggicnh.shp

jugg.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: titanlegs.shp
		Facings: 16
		ZOffset: -512
	turret:
		Filename: juggt.shp
		Facings: 32
		UseClassicFacings: True

ttrk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ttrk.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: ttrkicon.shp

disr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: disr.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: sonict.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: disricon.shp

disr.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: disr.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: sonict.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

hmlrs:
	Inherits: ^VehicleOverlays
	idle:
		Filename: hvrhull.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: hvrtur.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: hmlrsicnh.shp

tnkd:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tnkd.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: tnkd.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 2
	icon:
		Filename: tnkdicon.shp

tnkd.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: tnkd.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: tnkd.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True

v3rl:
	Inherits: ^VehicleOverlays
	idle:
		Filename: v3rl.shp
		Facings: 32
		UseClassicFacings: True
	empty-idle:
		Filename: v3rl.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: v3rlicon.shp

rtnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: rtnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: rtnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: rtnkicon.shp

rtnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: rtnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: rtnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

msar:
	Inherits: ^VehicleOverlays
	idle:
		Filename: msar.shp
		Facings: 32
		UseClassicFacings: True
	spinner:
		Filename: msarspinner.shp
		Length: 32
	make:
		Filename: msarmake.shp
		Length: *
		Tick: 50
	idle-static:
		Filename: msardeployed.shp
		Length: 14
		Tick: 80
	icon:
		Filename: msaricnh.shp

ptnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ptnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: ptnkt.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: prismmuzzle.shp
		Length: 4
	icon:
		Filename: ptnkicon.shp

ptnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ptnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: ptnkt.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

mwtnk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mwtnk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: mwtnkt.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: emp_fx02.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
	icon:
		Filename: mwtnkicnh.shp

mwtnk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mwtnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: mwtnkt.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

batf:
	Inherits: ^VehicleOverlays
	idle:
		Filename: batf.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun16.shp
		Length: 6
		Facings: 16
	medic:
		Filename: mouse.shp
		Start: 194
		Length: 1
	icon:
		Filename: batficon.shp

batf.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: batf.shp
		Facings: 32
		UseClassicFacings: True

spec:
	Inherits: ^VehicleOverlays
	idle:
		Filename: spec.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: specicnh.shp

sapc:
	Inherits: ^VehicleOverlays
	idle:
		Filename: sapc.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: sapcicon.shp

memp:
	Inherits: ^VehicleOverlays
	idle:
		Filename: memp.shp
		Facings: 32
		UseClassicFacings: True
	charging:
		Filename: memp.shp
		Facings: 32
	idle-overlay:
		Filename: pulsball.shp
		Length: 23
		BlendMode: Additive
		Tick: 40
		ZOffset: 511
	icon:
		Filename: mempicnh.shp

cdrone:
	Inherits: ^VehicleOverlays
	idle:
		Filename: cdrone.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: cdroneicnh.shp

katy:
	Inherits: ^VehicleOverlays
	idle:
		Filename: katy.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	move:
		Filename: katy.shp
		Facings: 32
		Length: 1
		UseClassicFacings: True
	icon:
		Filename: katyicnh.shp

grad:
	Inherits: ^VehicleOverlays
	idle:
		Filename: grad.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	turret:
		Filename: gradtur.shp
		Facings: 32
		UseClassicFacings: True
	turret-upg:
		Filename: gradturupg.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: gradicon.shp

chpr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: chpr.shp
		Facings: 32
		UseClassicFacings: True
	turretactive:
		Filename: chpr.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: chpr.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: chpricnh.shp

chpr.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: chpr.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: chpr.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

pcan:
	Inherits: ^VehicleOverlays
	idle:
		Filename: pcan.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: pcan.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: prismmuzzle.shp
		Length: 4
	icon:
		Filename: pcanicon.shp

pcan.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: pcan.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

cryo:
	Inherits: ^VehicleOverlays
	idle:
		Filename: cryol.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: cryolicnh.shp

isu:
	Inherits: ^VehicleOverlays
	idle:
		Filename: isu.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: isuicon.shp

isu.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: isu.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

nukc:
	Inherits: ^VehicleOverlays
	idle:
		Filename: nukc.shp
		Facings: 32
		UseClassicFacings: True
	make:
		Filename: nukcd.shp
		Facings: 4
		Length: 4
		Tick: 300
	undeploy:
		Filename: nukcd.shp
		Frames: 3,2,1,0,7,6,5,4,11,10,9,8,15,14,13,12
		Facings: 4
		Length: 4
		Tick: 300
	deployed:
		Filename: nukcd.shp
		Facings: 4
		Frames: 3, 7, 11, 15
	overlay:
		Filename: nukco.shp
		Facings: 32
		ZOffset: 128
		UseClassicFacings: True
	turret:
		Filename: nukct.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: smokeygun.shp
		Length: 12
		Tick: 30
	icon:
		Filename: nukcicon.shp

nukc.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: nukc.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

apoc:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apoc.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: apoci.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: apoctur.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: apocicon.shp

apoc.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apoc.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: apoctur.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

apoci:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apoci.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: apoctur.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: apociicon.shp

apoc.erad:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apoc.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: apoci.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: apocturi.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: radhitsm.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: *
	icon:
		Filename: apoceradicon.shp

apoc.erad.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apoc.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: apocturi.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

apoc.eradi:
	Inherits: ^VehicleOverlays
	idle:
		Filename: apoci.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: apocturi.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: radhitsm.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: *
	icon:
		Filename: apoceradiicon.shp

ovld:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ovld.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: apoci.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: ovldtur.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: ovldicon.shp

ovld.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ovld.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: ovldtur.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ovldi:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ovldi.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: ovldtur.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: ovldiicon.shp

ovld.erad:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ovld.shp
		Facings: 32
		UseClassicFacings: True
	idle2:
		Filename: ovldi.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: ovldturi.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: radhitsm.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: *
	icon:
		Filename: ovlderadicon.shp

ovld.erad.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ovld.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: ovldturi.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ovld.eradi:
	Inherits: ^VehicleOverlays
	idle:
		Filename: ovldi.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: ovldturi.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: radhitsm.shp
		IgnoreWorldTint: true
		BlendMode: Additive
		Length: *
	icon:
		Filename: ovlderadiicon.shp

trpc:
	Inherits: ^VehicleOverlays
	idle:
		Filename: trpc.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: trpcicon.shp

reck:
	Inherits: ifv
	idle:
		Filename: reck.shp
	open:
		Filename: reck.shp
		Start: 3
		Length: 1
	unload:
		Filename: reck.shp
		Start: 3
		Length: 1
	icon:
		Filename: reckicon.shp

reck.destroyed:
	Inherits: ifv.destroyed
	idle:
		Filename: reck.shp

cycp:
	Inherits: ^VehicleOverlays
	idle:
		Filename: cycp.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: cycpicon.shp

cycp.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: cycp.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

basi:
	Inherits: ^VehicleOverlays
	idle:
		Filename: basi.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: basiicon.shp

basi.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: basi.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

pbul:
	Inherits: ^VehicleOverlays
	idle:
		Filename: pbul.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: pbul.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: pbulicnh.shp

mant:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mant.shp
		Facings: 32
		Stride: 2
		UseClassicFacings: True
	move:
		Filename: mant.shp
		Facings: 32
		Length: 2
		Tick: 80
		UseClassicFacings: True
	muzzle:
		Filename: lasermuzzle.shp
		Length: *
	icon:
		Filename: manticnh.shp

mant.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: mant.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

vipr:
	Inherits: ^VehicleOverlays
	idle:
		Filename: vipr.shp
		Facings: 1
		Start: 32
	turret:
		Filename: vipr.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: lasermuzzle.shp
		Frames: 0,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1,2,1
		Length: *
	icon:
		Filename: vipricnh.shp

rhin:
	Inherits: ^VehicleOverlays
	idle:
		Filename: rhin.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: rhin.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret-upg:
		Filename: rhinu.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: rhinicon.shp

rhini:
	Inherits: ^VehicleOverlays
	idle:
		Filename: rhini.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: rhin.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret-upg:
		Filename: rhinu.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: 5
	icon:
		Filename: rhiniicon.shp

rhiny:
	Inherits: rhin
	idle:
		Filename: rhiny.shp
	turret:
		Filename: rhiny.shp
	turret-upg:
		Filename: rhinu.shp
		Start: 32
	icon:
		Filename: rhinyicon.shp

rhinay:
	Inherits: rhini
	idle:
		Filename: rhinay.shp
	turret:
		Filename: rhiny.shp
	turret-upg:
		Filename: rhinu.shp
		Start: 32
	icon:
		Filename: rhinayicon.shp

rhin.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: rhin.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: rhin.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret-upg:
		Filename: rhinu.shp
		Facings: 32
		UseClassicFacings: True

thwk:
	Inherits: ^VehicleOverlays
	idle:
		Filename: thwk.shp
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: thwk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret-empty:
		Filename: thwk.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: thwkicnh.shp

thwk.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: thwk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: thwk.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

zeus:
	Inherits: ^VehicleOverlays
	idle:
		Filename: zeus.shp
		Facings: 32
		UseClassicFacings: True
	shoot:
		Filename: zeus.shp
		Facings: 32
		Start: 32
		UseClassicFacings: True
	active-overlay:
		Filename: emp_fx01.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
	icon:
		Filename: zeusicon.shp

mole:
	Inherits: ^VehicleOverlays
	Defaults:
		Filename: mole.shp
		Tick: 50
	idle:
		Frames: 20,19,18
		Length: 3
		Tick: 40
	paused:
		Frames: 18
	unburrow:
		Length: 19
	burrow:
		Start: 20
		Length: 22
	empty:
		Filename: empty.shp

mole.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Frames: 18
		Filename: mole.shp
		ZOffset: -512

avtr:
	Inherits: ^VehicleOverlays
	Defaults:
		Offset: 0, -5
	idle:
		Filename: avtrbody.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 512
	bodyidle:
		Filename: avtrbody.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: 512
	bodymove:
		Filename: avtrbody.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: 512
	bodyshoot:
		Filename: avtrbody.shp
		Start: 64
		Facings: 32
		UseClassicFacings: True
		ZOffset: 512
		Tick: 750
	bodymake:
		Filename: avtrbody.shp
		Start: 96
		Facings: 16
		Length: 10
		Tick: 140
		ZOffset: 512
	legsidle:
		Filename: avtrlegs.shp
		Facings: 32
	legsmove:
		Filename: avtrlegs.shp
		Start: 32
		Facings: 32
		Length: 10
		Tick: 140
	muzzle:
		Filename: lasermuzzle.shp
		Frames: 0,1,2,1,2,1,2,1,2
		Length: *
		Offset: 0, 0
	muzzle-flame:
		Combine:
			0:
				Filename: flame-n.shp
				Length: *
				Offset: 3,6
			1:
				Filename: flame-nw.shp
				Length: *
				Offset: 8,7
			2:
				Filename: flame-w.shp
				Length: *
				Offset: 8,2
			3:
				Filename: flame-sw.shp
				Length: *
				Offset: 7,-2
			4:
				Filename: flame-s.shp
				Length: *
				Offset: 3,-2
			5:
				Filename: flame-se.shp
				Length: *
				Offset: -5,-2
			6:
				Filename: flame-e.shp
				Length: *
				Offset: -7,2
			7:
				Filename: flame-ne.shp
				Length: *
				Offset: -7,8
		Facings: 8
		Length: 13
		Offset: 0, 0
	muzzle-black:
		Filename: flameblack.shp
		Length: 13
		Facings: 8
		Offset: 0, 0
	icon:
		Filename: avtricnh.shp
		Offset: 0, 0

avtr.destroyed:
	Inherits: ^VehicleOverlays
	idle:
		Filename: avtrlegs.shp
		Facings: 32
		ZOffset: -512
		Offset: 0, -5
