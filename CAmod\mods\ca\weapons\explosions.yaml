^Explosion:
	ValidTargets: Ground, Water, Air
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Damage: 5000
		Versus:
			None: 90
			Wood: 75
			Light: 60
			Heavy: 25
			Concrete: 50
			Brick: 100
		DamageTypes: Prone50Percent, Tri<PERSON><PERSON>rone, Explosion<PERSON><PERSON><PERSON>
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Structure, Wall, Trees
	Warhead@2Eff: CreateEffect
		Explosions: self_destruct
		ImpactSounds: kaboom22.aud
		ValidTargets: Ground, Air, Ship, Trees
		ImpactActors: false
	Warhead@3EffWater: CreateEffect
		Explosions: large_splash
		ImpactSounds: splash9.aud
		ValidTargets: Water, Underwater
		InvalidTargets: Ground, Ship, Structure, Bridge

CrateNapalm:
	Inherits: ^Explosion
	ValidTargets: Ground, Trees
	Warhead@1Dam: SpreadDamage
		Spread: 170
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: Ground, Trees
		Versus:
			Wood: 100
			Brick: 50
		AffectsParent: true
		DamageTypes: Prone50Per<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>D<PERSON>h, Incendiary
	Warhead@2Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: firebl3.aud
		ValidTargets: Ground, Water, Air, Trees
	-Warhead@3EffWater:
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx xxx xxx

CrateExplosion:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		AffectsParent: true
	Warhead@2Eff: CreateEffect
		ValidTargets: Ground, Water, Air
	-Warhead@3EffWater:

UnitExplode:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Falloff: 1000, 368, 135, 50, 18, 7, 0

UnitExplodePlane:
	Inherits: UnitExplode
	Warhead@1Dam: SpreadDamage
		Damage: 4000
		Versus:
			None: 30
			Light: 30
	Warhead@2Eff: CreateEffect
		Explosions: large_napalm

UnitExplodePlaneLight:
	Inherits: UnitExplodePlane
	Warhead@1Dam: SpreadDamage
		Damage: 3000
	Warhead@2Eff: CreateEffect
		Explosions: napalm

UnitExplodePlaneEmpty:
	Inherits: UnitExplode
	Warhead@1Dam: SpreadDamage
		Damage: 2000

UnitExplodeHeli:
	Inherits: UnitExplode
	Warhead@1Dam: SpreadDamage
		Damage: 3750
		Versus:
			None: 30
			Light: 30
	Warhead@2Eff: CreateEffect
		Explosions: large_napalm

UnitExplodeHeliEmpty:
	Inherits: UnitExplodeHeli
	Warhead@1Dam: SpreadDamage
		Damage: 2000
	Warhead@2Eff: CreateEffect
		Explosions: napalm

UnitExplodeDrone:
	Inherits: UnitExplodeHeli
	Warhead@1Dam: SpreadDamage
		Damage: 2000
		Versus:
			None: 30
			Light: 30

UnitExplodeDroneEmpty:
	Inherits: UnitExplodeHeliEmpty
	Warhead@1Dam: SpreadDamage
		Damage: 1000

UnitExplodeFlameSmall:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Damage: 4000
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@2Eff: CreateEffect
		Explosions: offseted_napalm
		ImpactSounds: firebl3.aud
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch

UnitExplodeChemSmall:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Damage: 4000
		DamageTypes: Prone50Percent, TriggerProne, PoisonDeath
		InvalidTargets: ChemWarrior
	Warhead@2Eff: CreateEffect
		Explosions: small_chem
		ExplosionPalette: temptd
		ImpactSounds: firebl3.aud
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame

UnitExplodeWarpOut:
	Inherits: ^Explosion
	-Warhead@1Dam:
	Warhead@2Eff: CreateEffect
		Image: chrono
		Explosions: warpout
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ImpactSounds: chrono2.aud

VisualExplodeHusk:
	Inherits: ^Explosion
	-Warhead@1Dam:
	Warhead@2Eff: CreateEffect
		Explosions: large_explosion
	Warhead@Shrap: FireShrapnel
		Weapon: SmallDebris
		Amount: 5
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

VisualExplodeAirborne:
	Inherits: VisualExplodeHusk
	Warhead@2Eff: CreateEffect
		Explosions: med_explosion_air

VisualUnitExplodeWater:
	Inherits: VisualExplodeHusk
	-Warhead@Smu:
	Warhead@4EffWater: CreateEffect
		Explosions: small_splash
		ImpactSounds: splash9.aud

VisualExplodeInvader:
	Inherits: VisualExplodeHusk
	-Warhead@Smu:
	Warhead@2Eff: CreateEffect
		Explosions: self_destruct
		ImpactSounds: invader-die1.aud, invader-die2.aud

UnitExplodeShip:
	Inherits: ^Explosion
	-Warhead@Smu:
	Warhead@2Eff: CreateEffect
		Explosions: building, building2
		ImpactSounds: kaboom25.aud
		ValidTargets: Ground, Water

UnitExplodeSubmarine:
	Inherits: ^Explosion
	-Warhead@Smu:
	Warhead@2Eff: CreateEffect
		Explosions: large_splash
		ImpactSounds: splash9.aud
		ValidTargets: Ground, Water

UnitExplodeSmall:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Damage: 4000
	Warhead@2Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: kaboom15.aud

UnitExplodeGrenade:
	Inherits: UnitExplodeSmall
	Warhead@2Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: kaboom25.aud

UnitExplodeGrenadeTD:
	Inherits: UnitExplodeGrenade
	Warhead@2Eff: CreateEffect
		Explosions: self_destruct
		ImpactSounds: xplosml2.aud

VisualExplodeSmall:
	Inherits: UnitExplodeSmall
	-Warhead@1Dam:

ArtilleryExplode:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Damage: 15000

V3ExplodeAirborne:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Range: 0, 1c768, 2c768
		Falloff: 100, 100, 0
		Damage: 5000
		Versus:
			None: 10
			Wood: 55
			Light: 30
			Heavy: 20
			Concrete: 100
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: artillery_explosion
		ImpactSounds: kaboom15.aud

THExplodeAirborne:
	Inherits: V3ExplodeAirborne

BuildingExplode:
	Warhead@2Eff: CreateEffect
		Explosions: building, building_napalm, large_explosion, self_destruct, large_napalm
		Inaccuracy: 171
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Wall, Trees
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 3,3
		Footprint: xxx xxx xxx

SmallBuildingExplode:
	Inherits: BuildingExplode
	Warhead@2Eff: CreateEffect
		Explosions: building, building_napalm, large_explosion, self_destruct
	Warhead@Flames: FireCluster
		RandomClusterCount: 1
		Dimensions: 2,2
		Footprint: xx xx

FakeBuildingExplode:
	Warhead@1Dam: SpreadDamage
		DamageTypes: DefaultDeath
		Spread: 1c0
		Falloff: 100, 50, 35, 22, 14, 0
		Damage: 75000
		Versus:
			None: 30
			Wood: 20
			Concrete: 20
			Light: 90

FakeBuildingSelfDestruct:
	Report: icolseta.aud
	ReloadDelay: 200
	Warhead@1Dam: HealthPercentageDamage
		Delay: 20
		Spread: 1c0
		Damage: 100
		ValidTargets: Structure
		ValidRelationships: Ally
		AffectsParent: true

CivPanicExplosion:
	Warhead@1Dam: SpreadDamage # Used to panic civilians which are emitted from a killed CivBuilding
		Falloff: 100, 100
		Range: 0, 128
		Damage: 1
		Delay: 1

BarrelExplode:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: Ground, Trees
		Versus:
			None: 120
			Wood: 100
			Light: 50
			Brick: 50
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, Incendiary
	Warhead@2Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: firebl3.aud
		Delay: 5
	-Warhead@3EffWater:
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch
		Size: 2
		Delay: 5
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 1
		Dimensions: 2,2
		Footprint: xx xx

KirovExplode:
	Inherits: ^Explosion
	Warhead@1Dam: SpreadDamage
		Damage: 3000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 2
		ValidTargets: Ground, Trees
		Versus:
			None: 120
			Wood: 100
			Heavy: 35
			Concrete: 35
			Brick: 50
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Eff: CreateEffect
		Explosions: large_napalm
		ImpactSounds: vkircraa.aud
		Delay: 2
	Warhead@3EffWater: CreateEffect
		Explosions: large_napalm
		ImpactSounds: vkircraa.aud
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch
		Size: 2
		Delay: 2
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 3
		Dimensions: 2,2
		Footprint: xx xx

MothershipExplode:
	Inherits: KirovExplode
	Warhead@3Eff: CreateEffect
		Explosions: large_artillery_explosion
		ImpactSounds: artyhit2.aud, artyhit3.aud
	Warhead@Cluster1: FireCluster
		Weapon: MothershipExplodeCluster
		Dimensions: 5,5
		Footprint: __X__ _XX__ X__X_ __X_X _____
		Delay: 2
	Warhead@Cluster2: FireCluster
		Weapon: MothershipExplodeCluster
		Dimensions: 5,5
		Footprint: _____ X__XX _X__X XX_X_ __X__
		Delay: 4
	Warhead@5Shake: ShakeScreen
		Duration: 5
		Intensity: 1
		Multiplier: 0.5,0.5

MothershipExplodeCluster:
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Damage: 3000
	Warhead@2Eff: CreateEffect
		Explosions: napalm

ATMine:
	Warhead@1Dam: SpreadDamage
		Spread: 448
		Damage: 37000
		AffectsParent: true
		ValidTargets: Ground, Water, Underwater
		InvalidTargets: Mine
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster
	Warhead@2Eff: CreateEffect
		Explosions: large_explosion
		ImpactSounds: mineblo1.aud
	Warhead@Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Structure, Wall, Trees

APMine:
	Inherits: ATMine
	Warhead@1Dam: SpreadDamage
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@2Eff: CreateEffect
		Explosions: napalm
		ImpactSounds: mine1.aud
	Warhead@Smu: LeaveSmudge
		SmudgeType: Scorch
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 1
		Dimensions: 2,2
		Footprint: xx xx

OreExplosion:
	Warhead@1Dam: SpreadDamage
		Spread: 9
		Damage: 1000
		Versus:
			None: 90
			Wood: 70
			Light: 60
			Heavy: 20
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@3Res: CreateResource
		AddsResourceType: Ore
		Size: 1,1
	Warhead@2Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: kaboom25.aud

TibExplosion:
	Warhead@1Dam: SpreadDamage
		Spread: 9
		Damage: 1000
		Versus:
			None: 90
			Wood: 70
			Light: 60
			Heavy: 20
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@3Res: CreateResource
		AddsResourceType: Tiberium
		Size: 1,1
	Warhead@2Eff: CreateEffect
		Explosions: med_explosion
		ImpactSounds: kaboom25.aud

CrateNuke:
	ValidTargets: Ground, Trees, Water, Air
	Warhead@1Dam_impact: SpreadDamage
		Spread: 1c0
		Damage: 5000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: Ground, Trees, Water, Air
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Res_impact: DestroyResource
	Warhead@3Eff_impact: CreateEffect
		Explosions: nuke
		ImpactSounds: kaboom1.aud
		ImpactActors: false
	Warhead@4Dam_areanuke1: SpreadDamage
		Spread: 1c0
		Damage: 3000
		Falloff: 1000, 600, 400, 250, 150, 100, 0
		Delay: 5
		ValidTargets: Ground, Trees, Water, Air
		Versus:
			Tree: 200
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@5Res_areanuke1: DestroyResource
		Size: 4
		Delay: 5
	Warhead@6Eff_areanuke1: CreateEffect
		ImpactSounds: kaboom22.aud
		Delay: 5
		ImpactActors: false
	Warhead@6Smu_areanuke1: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Trees
		Size: 4
		Delay: 5
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 5
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 350
		Falloff: 100, 75, 52, 37, 24, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong
	Warhead@FlashEffect: FlashEffect
		Duration: 20
		FlashType: Nuke

UnitExplodeIraqTank:
	Inherits: CrateNuke
	Warhead@3Eff_impact: CreateEffect
		Explosions: nuke3
	Warhead@1Dam_impact: SpreadDamage
		Damage: 1000
		Versus:
			None: 45
			Light: 85
		Falloff: 368, 135, 50, 18, 0
	-Warhead@2Res_impact:
	Warhead@4Dam_areanuke1: SpreadDamage
		Damage: 500
		Versus:
			None: 45
			Light: 85
		Falloff: 600, 400, 250, 150,  0
	-Warhead@5Res_areanuke1:
	Warhead@6Smu_areanuke1: LeaveSmudge
		Size: 2
	Warhead@18Radio: CreateTintedCells
		Level: 300
		Falloff: 100, 55, 32, 5
		LayerName: radioactivity.weak
	-Warhead@Flames:
	-Warhead@FlashEffect:

MiniNuke:
	ValidTargets: Ground, Trees, Water, Underwater, Air
	Warhead@1Dam_impact: SpreadDamage
		Spread: 1c0
		Damage: 7500
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: Ground, Trees, Water, Air
		Versus:
			Tree: 200
			Wood: 35
			Concrete: 35
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Res_impact: DestroyResource
		Size: 1
	Warhead@3Eff_impact: CreateEffect
		Explosions: nuke
		ImpactSounds: kaboom1.aud
		ImpactActors: false
	Warhead@4Dam_areanuke1: SpreadDamage
		Spread: 2c0
		Damage: 3000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: Ground, Trees, Water, Underwater, Air
		Versus:
			Tree: 200
			Wood: 75
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@5Res_areanuke1: DestroyResource
		Size: 2
		Delay: 5
	Warhead@6Eff_areanuke1: CreateEffect
		ImpactSounds: kaboom22.aud
		Delay: 5
		ImpactActors: false
	Warhead@7Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 3000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 10
		ValidTargets: Ground, Water, Underwater, Air
		Versus:
			Wood: 75
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@8Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 6000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 10
		ValidTargets: Trees
		DamageTypes: Incendiary
	Warhead@9Res_areanuke2: DestroyResource
		Size: 3
		Delay: 10
	Warhead@10Dam_areanuke3: SpreadDamage
		Spread: 4c0
		Damage: 3000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 15
		ValidTargets: Ground, Water, Underwater
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@12Res_areanuke3: DestroyResource
		Size: 4
		Delay: 15
	Warhead@13Smu_areanuke3: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Trees
		Size: 4
		Delay: 15
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 6
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 350
		Falloff: 100, 75, 52, 37, 24, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong
	Warhead@FlashEffect: FlashEffect
		Duration: 20
		FlashType: Nuke

MicroNuke:
	Inherits: MiniNuke
	Warhead@1Dam_impact: SpreadDamage
		Spread: 1c0
		Damage: 5000
		Falloff: 1000, 368, 50, 7, 0
		ValidTargets: Ground, Trees, Water, Air
		Versus:
			Tree: 200
			Wood: 35
			Concrete: 35
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@2Res_impact: DestroyResource
		Size: 1
	Warhead@3Eff_impact: CreateEffect
		Explosions: nuke
		ImpactSounds: kaboom1.aud
		ImpactActors: false
	Warhead@4Dam_areanuke1: SpreadDamage
		Spread: 2c0
		Damage: 1000
		Falloff: 1000, 368, 50, 7, 0
		Delay: 5
		ValidTargets: Ground, Trees, Water, Underwater, Air
		Versus:
			Wood: 75
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@5Res_areanuke1: DestroyResource
		Size: 2
		Delay: 5
	Warhead@6Eff_areanuke1: CreateEffect
		ImpactSounds: kaboom22.aud
		Delay: 5
		ImpactActors: false
	Warhead@7Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 1000
		Falloff: 1000, 368, 50, 7, 0
		Delay: 10
		ValidTargets: Ground, Water, Underwater, Air
		Versus:
			Wood: 75
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@9Res_areanuke2: DestroyResource
		Size: 3
		Delay: 10
	Warhead@10Dam_areanuke3: SpreadDamage
		Spread: 4c0
		Damage: 1500
		Falloff: 1000, 368, 50, 7, 0
		Delay: 15
		ValidTargets: Ground, Water, Underwater
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath, Incendiary
	Warhead@11Dam_areanuke3: SpreadDamage
		Spread: 4c0
		Damage: 4500
		Falloff: 1000, 368, 50, 7, 0
		Delay: 15
		ValidTargets: Trees
		DamageTypes: Incendiary
	Warhead@12Res_areanuke3: DestroyResource
		Size: 3
		Delay: 15
	Warhead@13Smu_areanuke3: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Trees
		Size: 3
		Delay: 15
	Warhead@Flames: FireCluster
		Weapon: BurnFx
		RandomClusterCount: 6
		Dimensions: 3,3
		Footprint: xxx xxx xxx
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 350
		Falloff: 100, 70, 37, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong
	-Warhead@FlashEffect:

UnitExplodeVice:
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Damage: 5000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: Ground, Trees
		InvalidTargets: Creep
		Versus:
			None: 120
			Wood: 200
			Light: 50
			Heavy: 25
			Concrete: 25
			Brick: 5
		DamageTypes: Prone50Percent, TriggerProne, RadiationDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		Size: 2
		Delay: 5
	Warhead@3Eff: CreateEffect
		Explosions: med_chem
		ExplosionPalette: temptd
		ImpactSounds: vtoxcona.aud, vtoxconb.aud, vtoxconc.aud
		Delay: 5
	Warhead@4Radio: CreateTintedCells
		Spread: 1c0
		Level: 250
		Falloff: 100, 55, 32, 5
		MaxLevel: 750
		LayerName: radioactivity.weak

UnitExplodeToxinTruck:
	ValidTargets: Ground, Water, Underwater
	Warhead@1Dam_impact: SpreadDamage
		Spread: 1c0
		Damage: 5000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		ValidTargets: Ground, Water
		InvalidTargets: Creep
		Versus:
			Brick: 5
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
	Warhead@3Eff_impact: CreateEffect
		Explosions: large_chem
		ExplosionPalette: tdeffect-ignore-lighting-alpha85
		ImpactSounds: vdemdiea.aud
	Warhead@4Dam_areanuke1: SpreadDamage
		Spread: 2c0
		Damage: 2000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 5
		ValidTargets: Ground, Water
		InvalidTargets: Creep
		Versus:
			Brick: 5
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
	Warhead@7Dam_areanuke2: SpreadDamage
		Spread: 3c0
		Damage: 2000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Delay: 10
		ValidTargets: Ground, Water
		InvalidTargets: Creep
		Versus:
			Brick: 5
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, ToxinDeath
	Warhead@11Smu_areanuke3: LeaveSmudge
		SmudgeType: Scorch-NoFlame
		InvalidTargets: Structure, Wall, Trees
		Size: 3
		Delay: 5
	Warhead@18Radio: CreateTintedCells
		Spread: 1c0
		Level: 350
		Falloff: 100, 75, 52, 37, 24, 15, 2
		MaxLevel: 750
		LayerName: radioactivity.strong
	Warhead@15Spawn: SpawnActor
		Actors: toxiccloud2, toxiccloud
		Range: 5
		ForceGround: false
		Image: Cloud1d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
	Warhead@16Spawn: SpawnActor
		Actors: toxiccloud2
		Range: 5
		ForceGround: false
		Image: Cloud2d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
	Warhead@17Spawn: SpawnActor
		Actors: toxiccloud
		Range: 5
		ForceGround: false
		Image: Cloud2d
		Palette: tseffect-ignore-lighting-alpha75
		ValidTargets: Ground, Water
	Warhead@Shrap: FireShrapnel
		Weapon: ChemDebris
		Amount: 7
		AimChance: 0
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

MEMP:
	Report: mobemp1.aud
	ValidTargets: Ground, Water
	Warhead@1Dam_impact: SpreadDamage
		Range: 0, 1c1, 2c1, 3c1, 3c512, 4c1, 4c512
		Damage: 4500
		Falloff: 1000, 368, 135, 90, 68, 37, 10
		DamageTypes: Prone50Percent, TriggerProne, ElectricityDeath
		Versus:
			None: 20
			Wood: 20
			Light: 20
			Heavy: 20
			Concrete: 25
			Brick: 5
	Warhead@2Eff: CreateEffect
		ExplosionPalette: tsunit-ignore-lighting-alpha75
		Explosions: pulse_explosion3
	Warhead@emp: GrantExternalConditionCA
		Range: 5c0
		Duration: 300
		Condition: empdisable
		ValidTargets: Ground, Vehicle
		InvalidTargets: Defense, EmpImmune
	Warhead@empdef: GrantExternalConditionCA
		Range: 5c0
		Duration: 600
		Condition: empdisable
		ValidTargets: Defense
		InvalidTargets: EmpImmune
	Warhead@empAir: GrantExternalConditionCA
		Range: 2c0
		Duration: 300
		Condition: empdisable
		ValidTargets: Air
		InvalidTargets: EmpImmune
		HitShapeCheck: false
	Warhead@2Smu_impact: LeaveSmudge
		SmudgeType: Scorch
	Warhead@3Smu_area: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 1
		Delay: 3
	Warhead@4Smu_area2: LeaveSmudge
		SmudgeType: Scorch
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees
		Size: 2,1
		Delay: 6

ChaosGas:
	ReloadDelay: 200
	ValidTargets: Ground, Water, Underwater
	Warhead@3Eff_impact: CreateEffect
		Explosions: gasring2
		ExplosionPalette: caneon-ignore-lighting-alpha75
		ImpactSounds: vchaatta.aud
	Warhead@chaos: GrantExternalConditionCA
		Range: 3c0
		Duration: 200
		Condition: berserk
		ValidTargets: Ground, Vehicle
		InvalidTargets: ChaosImmune
	Warhead@chaosClouds: SpawnActor
		Actors: chaoscloud, chaoscloud2
		Range: 3
		ValidTargets: Ground, Water

ChaosDroneExplode:
	Inherits: ChaosGas
	Report: vchadiea.aud

ChaosDroneExplodeSmall:
	Inherits: ChaosGas
	Warhead@3Eff_impact: CreateEffect
		Explosions: chaosexplosion
		ExplosionPalette: caneon
		ImpactSounds: firebl3.aud
		ImpactActors: false
	Warhead@chaos: GrantExternalConditionCA
		Range: 1c512

CryoExplosion:
	Warhead@2Eff: CreateEffect
		Explosions: cryoblast
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ImpactSounds: cryoblast.aud
	Warhead@chill: GrantExternalConditionCA
		Condition: chilled
		Duration: 150
		Range: 1c768
	Warhead@cryoresidue: CreateTintedCells
		LayerName: cryoresidue
		Spread: 1c0
		Level: 100
		Falloff: 100, 75, 52, 15, 2
		MaxLevel: 600

TemporalExplode:
	Warhead@1Eff: CreateEffect
		Explosions: chronowarp_effect
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ImpactSounds: chronowarp.aud
		ValidTargets: Ground, Water, Air

TemporalExplodeLarge:
	Warhead@1Eff: CreateEffect
		Explosions: chronowarpbig_effect
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ImpactSounds: chronowarp.aud
		ValidTargets: Ground, Water, Air
	Warhead@3Flash: ChronoFlashEffect

TPWRZap:
	Inherits: ^TeslaWeapon
	ReloadDelay: 80
	Range: 5c0
	ValidTargets: Ground, Water, Infantry, Vehicle
	-Report:
	Warhead@1Dam: SpreadDamage
		Damage: 1000
		AffectsParent: True
		Versus:
			Wood: 120
			Concrete: 110
	Warhead@Burst: FireShrapnel
		Weapon: TPWRArc1
		Amount: 1
		AimChance: 90
		ValidTargets: Ground, Water, Infantry, Vehicle
		ThrowWithoutTarget: true

TPWRArc1:
	Inherits: TTankZap
	Range: 3c0
	Warhead@1Dam: SpreadDamage
		ValidTargets: Ground, Water, Infantry, Vehicle
	Warhead@Burst: FireShrapnel
		Weapon: TPWRArc2
		AimChance: 80

TPWRArc2:
	Inherits: TPWRArc1
	-Warhead@Burst:

BSKY:
	Warhead@1Dam: SpreadDamage
		Damage: 70000
		Spread: 128
		Versus:
			Wood: 20
			Concrete: 35
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster
	Warhead@3Eff: CreateEffect
		Explosions: artillery_explosion
		ImpactSounds: bsky-hit1.aud, bsky-hit2.aud

Mindblast:
	ReloadDelay: 200
	ValidTargets: Ground, Water, Underwater
	Report: iyurat2a.aud
	Warhead@Eff: CreateEffect
		Explosions: mindblast
		ExplosionPalette: ra2effect-ignore-lighting-alpha75
		ValidTargets: Ground, Trees, Water, Underwater, Air, AirSmall
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 20000
		Falloff: 1000, 500, 300, 150, 60, 17, 5, 5
		ValidTargets: Infantry, Vehicle, Structure, Ship
		ValidRelationships: Enemy, Neutral
		Versus:
			None: 10
			Wood: 100
			Concrete: 75
			Brick: 75
			Heavy: 65
			Light: 50
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath

MindblastSlave:
	Inherits: Mindblast
	Warhead@Eff: CreateEffect
		Explosions: mindblastsm
	Warhead@1Dam: SpreadDamage
		Spread: 0c512
		Damage: 40000
		Falloff: 100, 50, 25, 7, 0
		Versus:
			None: 25
	Warhead@2Dam: SpreadDamage
		Damage: 50000
		Range: 0, 1c0, 1c512, 2c512
		Falloff: 100, 100, 80, 10
		ValidTargets: Hero
		ValidRelationships: Enemy, Neutral

JackknifeExplosion:
	Range: 8c0
	ValidTargets: Ground, Water, Air, AirSmall
	Projectile: InstantHit
	Warhead@1Dam: SpreadDamage
		Damage: 30000
		Spread: 341
		Versus:
			None: 50
			Wood: 15
			Light: 75
			Heavy: 100
			Concrete: 80
			Brick: 15
		DamageTypes: Prone50Percent, TriggerProne, ExplosionDeath, TankBuster
		ValidTargets: Ground, Water, Air, AirSmall
		InvalidTargets: Jackknife
	Warhead@2Eff: CreateEffect
		Explosions: artillery_explosion
		ImpactSounds: kaboom15.aud
		ValidTargets: Ground, Water, Air, AirSmall
