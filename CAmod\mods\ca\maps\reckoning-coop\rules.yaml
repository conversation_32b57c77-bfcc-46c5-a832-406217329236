
World:
	LuaScript:
		Scripts: campaign.lua, reckoning-coop.lua
	MissionData:
		Briefing: ================\n\nSpoiler warning: This is a co-op version of the campaign mission with the same name.\n\nNod and GDI put their differences aside in order to help the Scrin Rebels defeat the forces of the Overlord.
	MapBuildRadius:
		AllyBuildRadiusCheckboxLocked: False
		AllyBuildRadiusCheckboxVisible: True

AFLD.GDI:
	-InterceptorPower@AirDef:

HPAD.TD:
	-InterceptorPower@AirDef:

STWR:
	Buildable:
		Prerequisites: ~structures.gdi, vehicles.any

TITN:
	Buildable:
		Prerequisites: ~vehicles.gdi, gtek, ~!railgun.upgrade

TITN.RAIL:
	Buildable:
		Prerequisites: ~vehicles.gdi, gtek, ~railgun.upgrade

JUGG:
	Buildable:
		Prerequisites: ~vehicles.gdi, gtek

HSAM:
	Buildable:
		Prerequisites: ~vehicles.gdi, anyradar

MDRN:
	Buildable:
		Prerequisites: ~vehicles.gdi, anyradar

GDRN:
	Buildable:
		Prerequisites: ~vehicles.gdi, ~!tow.upgrade

GDRN.TOW:
	Buildable:
		Prerequisites: ~vehicles.gdi, ~tow.upgrade

WOLV:
	Buildable:
		Prerequisites: ~vehicles.gdi, gtek

XO:
	Buildable:
		Prerequisites: ~vehicles.gdi, gtek

PBUL:
	Buildable:
		Prerequisites: ~vehicles.gdi, gtek

AURO:
	Buildable:
		Prerequisites: ~aircraft.gdi, gtek

gyro.upgrade:
	Buildable:
		Prerequisites: ~player.gdi, gtek

abur.upgrade:
	Buildable:
		Prerequisites: ~player.gdi, gtek

bdrone.upgrade:
	Buildable:
		Prerequisites: ~player.gdi, gtek

railgun.upgrade:
	Buildable:
		Prerequisites: ~player.gdi, upgc

ionmam.upgrade:
	Buildable:
		Prerequisites: ~player.gdi, upgc, !mdrone.upgrade, !hovermam.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

hovermam.upgrade:
	Buildable:
		Prerequisites: ~player.gdi, upgc, !ionmam.upgrade, !mdrone.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

mdrone.upgrade:
	Buildable:
		Prerequisites: ~player.gdi, upgc, !ionmam.upgrade, !hovermam.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

GTEK:
	GrantExternalConditionPowerCA@NREPAIR:
		Prerequisites: ~disabled

HQ:
	SpawnActorPower@GDIEagleDropzone:
		Prerequisites: ~disabled
		BlockedCursor: move-blocked
	ParatroopersPowerCA@xodrop:
		Prerequisites: ~disabled
	DropPodsPowerCA@Zocom:
		Prerequisites: ~radar.gdi

UAV:
	-Targetable@AIRBORNE:
	RevealsShroud:
		Range: 10c0
		MinRange: 8c0
	RevealsShroud@GAPGEN:
		Range: 8c0

SFAC:
	Health:
		HP: 275000
