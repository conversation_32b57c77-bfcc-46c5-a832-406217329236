quickinstall: Quick Install Package
	Type: ZipFile
	SHA1: 44241f68e69db9511db82cf83c174737ccda300b
	MirrorList: http://www.openra.net/packages/ra-quickinstall-mirrors.txt
	Extract:
		^SupportDir|Content/ca/allies.mix: allies.mix
		^SupportDir|Content/ca/conquer.mix: conquer.mix
		^SupportDir|Content/ca/hires.mix: hires.mix
		^SupportDir|Content/ca/interior.mix: interior.mix
		^SupportDir|Content/ca/local.mix: local.mix
		^SupportDir|Content/ca/lores.mix: lores.mix
		^SupportDir|Content/ca/russian.mix: russian.mix
		^SupportDir|Content/ca/snow.mix: snow.mix
		^SupportDir|Content/ca/sounds.mix: sounds.mix
		^SupportDir|Content/ca/speech.mix: speech.mix
		^SupportDir|Content/ca/temperat.mix: temperat.mix
		^SupportDir|Content/ca/expand/chrotnk1.aud: expand/chrotnk1.aud
		^SupportDir|Content/ca/expand/expand2.mix: expand/expand2.mix
		^SupportDir|Content/ca/expand/fixit1.aud: expand/fixit1.aud
		^SupportDir|Content/ca/expand/hires1.mix: expand/hires1.mix
		^SupportDir|Content/ca/expand/jburn1.aud: expand/jburn1.aud
		^SupportDir|Content/ca/expand/jchrge1.aud: expand/jchrge1.aud
		^SupportDir|Content/ca/expand/jcrisp1.aud: expand/jcrisp1.aud
		^SupportDir|Content/ca/expand/jdance1.aud: expand/jdance1.aud
		^SupportDir|Content/ca/expand/jjuice1.aud: expand/jjuice1.aud
		^SupportDir|Content/ca/expand/jjump1.aud: expand/jjump1.aud
		^SupportDir|Content/ca/expand/jlight1.aud: expand/jlight1.aud
		^SupportDir|Content/ca/expand/jpower1.aud: expand/jpower1.aud
		^SupportDir|Content/ca/expand/jshock1.aud: expand/jshock1.aud
		^SupportDir|Content/ca/expand/jyes1.aud: expand/jyes1.aud
		^SupportDir|Content/ca/expand/lores1.mix: expand/lores1.mix
		^SupportDir|Content/ca/expand/madchrg2.aud: expand/madchrg2.aud
		^SupportDir|Content/ca/expand/madexplo.aud: expand/madexplo.aud
		^SupportDir|Content/ca/expand/mboss1.aud: expand/mboss1.aud
		^SupportDir|Content/ca/expand/mhear1.aud: expand/mhear1.aud
		^SupportDir|Content/ca/expand/mhotdig1.aud: expand/mhotdig1.aud
		^SupportDir|Content/ca/expand/mhowdy1.aud: expand/mhowdy1.aud
		^SupportDir|Content/ca/expand/mhuh1.aud: expand/mhuh1.aud
		^SupportDir|Content/ca/expand/mlaff1.aud: expand/mlaff1.aud
		^SupportDir|Content/ca/expand/mrise1.aud: expand/mrise1.aud
		^SupportDir|Content/ca/expand/mwrench1.aud: expand/mwrench1.aud
		^SupportDir|Content/ca/expand/myeehaw1.aud: expand/myeehaw1.aud
		^SupportDir|Content/ca/expand/myes1.aud: expand/myes1.aud
		^SupportDir|Content/ca/cnc/desert.mix: cnc/desert.mix

basefiles: Base Freeware Content
	Type: ZipFile
	SHA1: aa022b208a3b45b4a45c00fdae22ccf3c6de3e5c
	MirrorList: http://www.openra.net/packages/ra-base-mirrors.txt
	Extract:
		^SupportDir|Content/ca/allies.mix: allies.mix
		^SupportDir|Content/ca/conquer.mix: conquer.mix
		^SupportDir|Content/ca/hires.mix: hires.mix
		^SupportDir|Content/ca/interior.mix: interior.mix
		^SupportDir|Content/ca/local.mix: local.mix
		^SupportDir|Content/ca/lores.mix: lores.mix
		^SupportDir|Content/ca/russian.mix: russian.mix
		^SupportDir|Content/ca/snow.mix: snow.mix
		^SupportDir|Content/ca/sounds.mix: sounds.mix
		^SupportDir|Content/ca/speech.mix: speech.mix
		^SupportDir|Content/ca/temperat.mix: temperat.mix

aftermath: Aftermath Expansion Files
	Type: ZipFile
	SHA1: d511d4363b485e11c63eecf96d4365d42ec4ef5e
	MirrorList: http://www.openra.net/packages/ra-aftermath-mirrors.txt
	Extract:
		^SupportDir|Content/ca/expand/chrotnk1.aud: expand/chrotnk1.aud
		^SupportDir|Content/ca/expand/expand2.mix: expand/expand2.mix
		^SupportDir|Content/ca/expand/fixit1.aud: expand/fixit1.aud
		^SupportDir|Content/ca/expand/hires1.mix: expand/hires1.mix
		^SupportDir|Content/ca/expand/jburn1.aud: expand/jburn1.aud
		^SupportDir|Content/ca/expand/jchrge1.aud: expand/jchrge1.aud
		^SupportDir|Content/ca/expand/jcrisp1.aud: expand/jcrisp1.aud
		^SupportDir|Content/ca/expand/jdance1.aud: expand/jdance1.aud
		^SupportDir|Content/ca/expand/jjuice1.aud: expand/jjuice1.aud
		^SupportDir|Content/ca/expand/jjump1.aud: expand/jjump1.aud
		^SupportDir|Content/ca/expand/jlight1.aud: expand/jlight1.aud
		^SupportDir|Content/ca/expand/jpower1.aud: expand/jpower1.aud
		^SupportDir|Content/ca/expand/jshock1.aud: expand/jshock1.aud
		^SupportDir|Content/ca/expand/jyes1.aud: expand/jyes1.aud
		^SupportDir|Content/ca/expand/lores1.mix: expand/lores1.mix
		^SupportDir|Content/ca/expand/madchrg2.aud: expand/madchrg2.aud
		^SupportDir|Content/ca/expand/madexplo.aud: expand/madexplo.aud
		^SupportDir|Content/ca/expand/mboss1.aud: expand/mboss1.aud
		^SupportDir|Content/ca/expand/mhear1.aud: expand/mhear1.aud
		^SupportDir|Content/ca/expand/mhotdig1.aud: expand/mhotdig1.aud
		^SupportDir|Content/ca/expand/mhowdy1.aud: expand/mhowdy1.aud
		^SupportDir|Content/ca/expand/mhuh1.aud: expand/mhuh1.aud
		^SupportDir|Content/ca/expand/mlaff1.aud: expand/mlaff1.aud
		^SupportDir|Content/ca/expand/mrise1.aud: expand/mrise1.aud
		^SupportDir|Content/ca/expand/mwrench1.aud: expand/mwrench1.aud
		^SupportDir|Content/ca/expand/myeehaw1.aud: expand/myeehaw1.aud
		^SupportDir|Content/ca/expand/myes1.aud: expand/myes1.aud

cncdesert: C&C Desert Tileset
	Type: ZipFile
	SHA1: 039849f16e39e4722e8c838a393c8a0d6529fd59
	MirrorList: http://www.openra.net/packages/ra-cncdesert-mirrors.txt
	Extract:
		^SupportDir|Content/ca/cnc/desert.mix: cnc/desert.mix

music-cnc: C&C Music
	Type: ZipFile
	URL: https://openra.baxxster.no/openra/cnc-music.zip
	Extract:
		^SupportDir|Content/ca/cnc/scores.mix: scores.mix

music-ra: Red Alert Music
	Type: ZipFile
	URL: https://openra.baxxster.no/openra/ra-music.zip
	Extract:
		^SupportDir|Content/ca/ra/scores.mix: scores.mix

music-ts: Tib Sun Music
	Type: ZipFile
	URL: https://openra.baxxster.no/openra/ts-music.zip
	Extract:
		^SupportDir|Content/ca/ts/scores.mix: scores.mix

music-fs: Expansion Freeware Music
	Type: ZipFile
	SHA1: 74b1ec47ea8c9815fb85c998963229aa0a8f8619
	URL: https://openra.baxxster.no/openra/ts-fsmusic.zip
	Extract:
		^SupportDir|Content/ca/firestorm/dmachine.aud: firestorm/dmachine.aud
		^SupportDir|Content/ca/firestorm/elusive.aud: firestorm/elusive.aud
		^SupportDir|Content/ca/firestorm/fsmap.aud: firestorm/fsmap.aud
		^SupportDir|Content/ca/firestorm/fsmenu.aud: firestorm/fsmenu.aud
		^SupportDir|Content/ca/firestorm/hacker.aud: firestorm/hacker.aud
		^SupportDir|Content/ca/firestorm/infiltra.aud: firestorm/infiltra.aud
		^SupportDir|Content/ca/firestorm/kmachine.aud: firestorm/kmachine.aud
		^SupportDir|Content/ca/firestorm/linkup.aud: firestorm/linkup.aud
		^SupportDir|Content/ca/firestorm/rainnite.aud: firestorm/rainnite.aud
		^SupportDir|Content/ca/firestorm/slavesys.aud: firestorm/slavesys.aud
