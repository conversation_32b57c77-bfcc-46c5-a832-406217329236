#region Copyright & License Information
/*
 * Copyright (c) The OpenRA Developers and Contributors
 * This file is part of OpenRA, which is free software. It is made
 * available to you under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version. For more
 * information, see COPYING.
 */
#endregion

using System;
using OpenRA.Widgets;

namespace OpenRA.Mods.CA.Widgets.Logic
{
	public class SupportPowerBinLogicCA : ChromeLogic
	{
		[ObjectCreator.UseCtor]
		public SupportPowerBinLogicCA(Widget widget)
		{
			var palette = widget.Get<SupportPowersScrollableWidget>("SUPPORT_PALETTE");

			var background = widget.GetOrNull("PALETTE_BACKGROUND");
			var foreground = widget.GetOrNull("PALETTE_FOREGROUND");
			if (background != null || foreground != null)
			{
				Widget backgroundTemplate = null;
				Widget foregroundTemplate = null;

				if (background != null)
					backgroundTemplate = background.Get("ICON_TEMPLATE");

				if (foreground != null)
					foregroundTemplate = foreground.Get("ICON_TEMPLATE");

				void UpdateBackground(int _, int icons)
				{
					var rowHeight = palette.IconSize.Y + palette.IconMargin;
					var rowWidth = palette.IconSize.X + palette.IconMargin;

					if (background != null)
					{
						background.RemoveChildren();

						for (var i = 0; i < icons; i++)
						{
							var row = backgroundTemplate.Clone();
							if (palette.Horizontal)
								row.Bounds.X += i * rowWidth;
							else
								row.Bounds.Y += i * rowHeight;
							background.AddChild(row);
						}
					}

					if (foreground != null)
					{
						foreground.RemoveChildren();

						for (var i = 0; i < icons; i++)
						{
							var row = foregroundTemplate.Clone();
							if (palette.Horizontal)
								row.Bounds.X += i * rowWidth;
							else
								row.Bounds.Y += i * rowHeight;
							foreground.AddChild(row);
						}
					}
				}

				palette.OnIconCountChanged += UpdateBackground;

				// Set the initial palette state
				UpdateBackground(0, 0);
			}
		}
	}
}
