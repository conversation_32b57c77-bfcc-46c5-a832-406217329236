^Palettes:
	PaletteFromFile@terrain-temperat:
		Filename: alien.pal
	TintPostProcessEffect:
		Red: 1.3
		Green: 1
		Blue: 1.5
		Ambient: 0.5

^BaseWorld:
	TerrainLighting:

World:
	LuaScript:
		Scripts: campaign.lua, spearhead.lua
	MissionData:
		Briefing: It seems we were not the Scrin fleet's target. Scouts sent in the direction they were heading have located a Nod outpost in a mountainous and relatively sparsely inhabited region. We have thus far been unable to locate the bulk of the Nod forces, but <PERSON> can't be too far away, and the Scrin were clearly trying to get to him. The terrain means it will be some time before we are able to move heavy forces to intercept.\n\nWe need more intel on <PERSON>'s whereabouts and intentions to properly plan our movements, and we believe this Nod outpost will be vulnerable to a small rapidly deployed strike team. Resources in the area are limited and there are few suitable locations for a staging area, but the lack of significant Scrin presence and the difficult terrain should mean you'll only face limited resistance.\n\nYour mission is to capture the Nod Communications Center. Anything else is at your discretion.
	MapOptions:
		ShortGameCheckboxEnabled: False
	ScriptLobbyDropdown@DIFFICULTY:
		ID: difficulty
		Label: dropdown-difficulty.label
		Description: dropdown-difficulty.description
		Values:
			easy: options-difficulty.easy
			normal: options-difficulty.normal
			hard: options-difficulty.hard
		Default: normal
	MusicPlaylist:
		StartingMusic: heroism

Player:
	PlayerResources:
		DefaultCash: 6000

# Disable tech

AFLD.GDI:
	-InterceptorPower@AirDef:

HQ:
	-DropPodsPowerCA@Zocom:
	-AirstrikePowerCA@uav:
	-AirstrikePowerCA@uavST:

EYE:
	Inherits@CAMPAIGNDISABLED: ^Disabled

UPGC.DROP:
	Inherits@CAMPAIGNDISABLED: ^Disabled

# Enable subfaction specific tech

XO:
	Buildable:
		Prerequisites: gtek, ~vehicles.gdi

WOLV:
	Buildable:
		Prerequisites: gtek, ~vehicles.gdi

PBUL:
	Buildable:
		Prerequisites: gtek, ~vehicles.gdi

STWR:
	Buildable:
		Prerequisites: vehicles.any, ~structures.gdi

ionmam.upgrade:
	Buildable:
		Prerequisites: upgc, !mdrone.upgrade, !hovermam.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

hovermam.upgrade:
	Buildable:
		Prerequisites: upgc, !ionmam.upgrade, !mdrone.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

mdrone.upgrade:
	Buildable:
		Prerequisites: upgc, !ionmam.upgrade, !hovermam.upgrade
	TooltipExtras:
		Attributes: \n(!) Only ONE Mammoth Tank upgrade can be chosen

# Misc

XO:
	TargetedLeapAbility:
		ChargeDelay: 175

ZRAI:
	RevealsShroud:
		Range: 7c0
	TargetedLeapAbility:
		ChargeDelay: 175

OCAR.AMCV:
	Inherits: OCAR
	RejectsOrders:
	-Buildable:
	-Selectable:
	Interactable:
	Aircraft:
		InitialFacing: 512
	Carryall:
		InitialActor: amcv
	Health:
		HP: 100000

# Hunt() requires only 1 AttackBase
DEVA:
	-AttackFrontalCharged:
	-Armament@PRIMARYUPG:
	-AmbientSound@CHARGE:
	-WithIdleOverlay@CHARGE1:
	-WithIdleOverlay@CHARGE2:
	-WithIdleOverlay@CHARGE3:
