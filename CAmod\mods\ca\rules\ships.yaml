SS:
	Inherits: ^Submarine
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Submarine
		BuildPaletteOrder: 30
		Prerequisites: ~spen, ~techlevel.low
		Description: Submerged anti-ship unit armed with torpedoes.
	TooltipExtras:
		Strengths: • Strong vs Naval Units
		Weaknesses: • Cannot attack Ground Units, Aircraft
		Attributes: • Can detect other submarines
	Valued:
		Cost: 950
	Tooltip:
		Name: Submarine
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 25000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 16
		Speed: 56
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Armament:
		Weapon: TorpTube
		LocalOffset: 0,-171,0, 0,171,0
		FireDelay: 2
		PauseOnCondition: under-bridge
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		RequiresCondition: !crossing-ford
		FacingTolerance: 0
	AutoTarget:
		InitialStance: HoldFire
	AutoTargetPriority@DEFAULT:
		ValidTargets: Water, Underwater
	AutoTargetPriority@ATTACKANYTHING:
		ValidTargets: Water, Underwater
	Selectable:
		DecorationBounds: 1621, 1621
	GrantConditionOnAttack@STOPSHOOTINGROCKS:
		Condition: im-dumb
		RequiredShotsPerInstance: 2
		MaximumInstances: 3
		RevokeDelay: 110
		RevokeOnNewTarget: true
		RevokeAll: true
		RequiresCondition: botowner
	RangeMultiplier@STOPSHOOTINGROCKS1:
		Modifier: 50
		RequiresCondition: im-dumb == 1
	RangeMultiplier@STOPSHOOTINGROCKS2:
		Modifier: 35
		RequiresCondition: im-dumb == 2
	RangeMultiplier@STOPSHOOTINGROCKS3:
		Modifier: 25
		RequiresCondition: im-dumb == 3
	Encyclopedia:
		Category: Soviets/Naval

MSUB:
	Inherits: ^Submarine
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Submarine
		BuildPaletteOrder: 60
		Prerequisites: ~spen, stek, ~techlevel.high
		Description: Submerged anti-ground siege unit.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Ground Units
		Weaknesses: • Weak vs Naval Units\n• Cannot attack Aircraft
		Attributes: • Can detect other submarines
	Valued:
		Cost: 1650
	Tooltip:
		Name: Missile Submarine
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 30000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 16
		Speed: 49
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Armament@PRIMARY:
		Weapon: SubMissile
		LocalOffset: 0,-171,0, 0,171,0
		FireDelay: 2
		PauseOnCondition: under-bridge
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 70
	AutoTarget:
		InitialStance: HoldFire
	Selectable:
		DecorationBounds: 1877, 1877
	Cloak:
		CloakDelay: 100
	Encyclopedia:
		Category: Soviets/Naval

DD:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetAllNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Boat
		BuildPaletteOrder: 40
		Prerequisites: ~syrd, anyradar, ~techlevel.medium
		Description: Fast multi-role ship.
	TooltipExtras:
		Strengths: • Strong vs Naval Units, Vehicles, Aircraft
		Weaknesses: • Weak vs Infantry
		Attributes: • Can detect submarines
	Valued:
		Cost: 1000
	Tooltip:
		Name: Destroyer
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 28
		Speed: 56
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted@PRIMARY:
		Turret: primary
		TurnSpeed: 28
		Offset: 469,0,128
	Turreted@SECONDARY:
		Turret: secondary
		TurnSpeed: 15
		Offset: -469,0,128
	Armament@PRIMARY:
		Weapon: Stinger
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 64, -64
		PauseOnCondition: under-bridge
	Armament@SECONDARY:
		Name: secondary
		Turret: secondary
		Weapon: DoubleDepthCharge
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 80, -80
		PauseOnCondition: under-bridge
	Armament@TERTIARY:
		Name: tertiary
		Weapon: StingerAA
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 64, -64
		PauseOnCondition: under-bridge
	AttackTurreted:
		Turrets: primary, secondary, tertiary
		Armaments: primary, secondary, tertiary
		PauseOnCondition: empdisable || being-warped || blinded
	WithSpriteTurret:
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
		Color: 00ff0020
		BorderColor: 00000020
	Selectable:
		DecorationBounds: 1621, 1621
	Encyclopedia:
		Category: Allies/Naval

CA:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Boat
		BuildPaletteOrder: 50
		Prerequisites: ~syrd, atek, ~techlevel.high
		Description: Very slow long-range bombardment ship.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Ground Units
		Weaknesses: • Weak vs Naval Units\n• Cannot attack Aircraft
	Valued:
		Cost: 2000
	Tooltip:
		Name: Cruiser
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 80000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 16
		Speed: 29
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted@PRIMARY:
		Turret: primary
		Offset: -896,0,128
		TurnSpeed: 12
	Turreted@SECONDARY:
		Turret: secondary
		Offset: 768,0,128
		TurnSpeed: 12
	Armament@PRIMARY:
		Turret: primary
		Weapon: 8Inch
		LocalOffset: 480,-100,40, 480,100,40
		Recoil: 171
		RecoilRecovery: 34
		MuzzleSequence: muzzle
		PauseOnCondition: under-bridge
	Armament@SECONDARY:
		Name: secondary
		Turret: secondary
		Weapon: 8Inch.NoReport
		LocalOffset: 480,-100,40, 480,100,40
		Recoil: 171
		RecoilRecovery: 34
		MuzzleSequence: muzzle
		FireDelay: 4
		PauseOnCondition: under-bridge
	AttackTurreted:
		Turrets: primary, secondary
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret@PRIMARY:
		Turret: primary
	WithSpriteTurret@SECONDARY:
		Turret: secondary
	Selectable:
		DecorationBounds: 1877, 1877
	Encyclopedia:
		Category: Allies/Naval

LST:
	Inherits: ^Ship
	Inherits@TRANSPORT: ^Transport
	Inherits@SELECTION: ^SelectableSupportUnit
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildPaletteOrder: 10
		Prerequisites: ~techlevel.low
		Description: General-purpose naval transport.
	TooltipExtras:
		Weaknesses: • Unarmed
		Attributes: • Can carry vehicles and infantry
	Valued:
		Cost: 700
	Tooltip:
		Name: Transport
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 35000
	Armor:
		Type: Heavy
	Mobile:
		Locomotor: lcraft
		Speed: 113
		PauseOnCondition: notmobile || empdisable || being-warped
	Hovers:
		BobDistance: -35
		RequiresCondition: !empdisable
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithLandingCraftAnimation:
		OpenTerrainTypes: Clear, Rough, Road, Ore, Gems, Beach
	Cargo:
		Types: Infantry, Hacker, Vehicle
		MaxWeight: 5
		PassengerFacing: 0
		LoadingCondition: notmobile
		LoadedCondition: cargo
		PassengerConditions:
			mcv: contains-mcv
			amcv: contains-mcv
			smcv: contains-mcv
	WithCargoSounds:
		EnterSounds: genter1a.aud
		ExitSounds: gexit1a.aud
	ProvidesPrerequisite@MCV:
		RequiresCondition: contains-mcv
		Prerequisite: anyconyard
	Selectable:
		DecorationBounds: 1536, 1536
	-Targetable@MINIDRONE:
	-ChangesHealth@MINIDRONE:
	-AttachableTo@MINIDRONE:
	Targetable@MINDCONTROL:
		RequiresCondition: !cargo && !mindcontrolled
	Encyclopedia:
		Category: Allies/Naval; Soviets/Naval; GDI/Naval; Nod/Naval

PT:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Boat
		BuildPaletteOrder: 20
		Prerequisites: ~syrd, ~techlevel.low
		Description: Light scout & support ship.
	TooltipExtras:
		Strengths: • Strong vs Naval Units
		Weaknesses: • Weak vs Ground Units\n• Cannot attack Aircraft
		Attributes: • Can detect submarines
	Valued:
		Cost: 500
	Tooltip:
		Name: Gunboat
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 20000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 28
		Speed: 92
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 28
		Offset: 512,0,0
	Armament@PRIMARY:
		Weapon: 2Inch
		LocalOffset: 208,0,48
		MuzzleSequence: muzzle
		PauseOnCondition: under-bridge
	Armament@SECONDARY:
		Name: secondary
		Weapon: DepthCharge
		MuzzleSequence: muzzle
		PauseOnCondition: under-bridge
	AttackTurreted:
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret:
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
		Color: 00ff0020
		BorderColor: 00000020
	Selectable:
		DecorationBounds: 1536, 1536
	Encyclopedia:
		Category: Allies/Naval

PT2:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetAllNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Boat
		BuildPaletteOrder: 20
		Prerequisites: ~syrd.gdi, ~techlevel.low
		IconPalette: chrometd
		Description: Light scout & support ship armed with guided missiles.
	TooltipExtras:
		Strengths: • Strong vs Submarines, Aircraft
		Weaknesses: • Weak vs Ground Units
		Attributes: • Can detect submarines
	Valued:
		Cost: 750
	Tooltip:
		Name: Patrol Boat
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 30000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 28
		Speed: 72
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Turreted:
		TurnSpeed: 28
		Offset: 512,0,0
	Armament@PRIMARY:
		Weapon: boatmissile
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 64, -64
		PauseOnCondition: under-bridge
	Armament@SECONDARY:
		Name: secondary
		Weapon: DepthCharge
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 80, -80
		PauseOnCondition: under-bridge
	Armament@TERTIARY:
		Name: tertiary
		Weapon: BoatMissileAA
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 64, -64
		PauseOnCondition: under-bridge
	AttackTurreted:
		Armaments: primary, secondary, tertiary
		PauseOnCondition: empdisable || being-warped || blinded
	WithMuzzleOverlay:
	WithSpriteTurret:
	WithIdleOverlay@SPINNER:
		Sequence: spinner
		Offset: -256,0,256
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: !under-bridge
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
		Color: 00ff0020
		BorderColor: 00000020
	Selectable:
		DecorationBounds: 1536, 1536
	Encyclopedia:
		Category: GDI/Naval

DD2:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Boat
		BuildPaletteOrder: 40
		IconPalette: chrometd
		Prerequisites: ~syrd.gdi, anyradar, ~techlevel.medium
		Description: Advanced warship armed with a powerful railgun.
	TooltipExtras:
		Strengths: • Strong vs Naval Units, Vehicles
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Can detect submarines
	Valued:
		Cost: 1000
	Tooltip:
		Name: Frigate
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 38000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 28
		Speed: 60
	RevealsShroud:
		MinRange: 5c0
		Range: 6c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	WithIdleOverlay@SPINNER:
		Sequence: spinner
		Offset: -90,0,406
		PauseOnCondition: empdisable || being-warped || blinded
		RequiresCondition: !under-bridge
	Turreted@PRIMARY:
		Turret: primary
		TurnSpeed: 20
		Offset: 551,0,158
	Turreted@SECONDARY:
		Turret: secondary
		Offset: 551,0,158
		TurnSpeed: 1024
	Turreted@TERTIARY:
		Turret: tertiary
		TurnSpeed: 15
		Offset: -469,0,128
	Armament@PRIMARY:
		Turret: primary
		Weapon: Railgun
		LocalOffset: 200,0,30
		Recoil: 0
		RecoilRecovery: 0
		MuzzleSequence: muzzle
		PauseOnCondition: under-bridge
	Armament@SECONDARY:
		Name: secondary
		Turret: secondary
		Weapon: PointLaser
		LocalOffset: 0,10,90
		PauseOnCondition: under-bridge
		RequiresCondition: pointdef-upgrade
		ForceTargetRelationships: enemy
	Armament@TERTIARY:
		Name: tertiary
		Turret: tertiary
		Weapon: DoubleDepthCharge
		LocalOffset: 0,-100,0, 0,100,0
		LocalYaw: 80, -80
		PauseOnCondition: under-bridge
	AttackTurreted:
		Turrets: primary, secondary, tertiary
		Armaments: primary, secondary, tertiary
		PauseOnCondition: empdisable || being-warped
	AutoTarget:
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
		Color: 00ff0020
		BorderColor: 00000020
	WithSpriteTurret@PRIMARY:
		Turret: primary
	WithMuzzleOverlay:
	Selectable:
		DecorationBounds: 1621, 1621
	PointDefense:
		Armament: secondary
		PointDefenseTypes: Missile
		RequiresCondition: pointdef-upgrade
		ValidRelationships: Enemy
	GrantConditionOnPrerequisite@POINTDEF:
		Condition: pointdef-upgrade
		Prerequisites: pointdef.upgrade
	Encyclopedia:
		Category: GDI/Naval

SS2:
	Inherits: ^Submarine
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Submarine
		BuildPaletteOrder: 30
		Prerequisites: ~spen.nod, ~techlevel.low
		Description: Submerged anti-ship unit armed with torpedoes.
	TooltipExtras:
		Strengths: • Strong vs Naval Units
		Weaknesses: • Cannot attack Ground Units, Aircraft
		Attributes: • Can detect other submarines
	Valued:
		Cost: 950
	Tooltip:
		Name: Attack Submarine
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 24000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 20
		Speed: 60
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Armament@PRIMARY:
		Weapon: TorpTube
		LocalOffset: 0,-171,0, 0,171,0
		FireDelay: 2
		PauseOnCondition: under-bridge
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		RequiresCondition: !crossing-ford
		FacingTolerance: 0
	AutoTarget:
		InitialStance: HoldFire
	AutoTargetPriority@DEFAULT:
		ValidTargets: Water, Underwater
	AutoTargetPriority@ATTACKANYTHING:
		ValidTargets: Water, Underwater
	Selectable:
		DecorationBounds: 1621, 1621
	GrantConditionOnAttack@STOPSHOOTINGROCKS:
		Condition: im-dumb
		RequiredShotsPerInstance: 2
		MaximumInstances: 3
		RevokeDelay: 110
		RevokeOnNewTarget: true
		RevokeAll: true
		RequiresCondition: botowner
	RangeMultiplier@STOPSHOOTINGROCKS1:
		Modifier: 50
		RequiresCondition: im-dumb == 1
	RangeMultiplier@STOPSHOOTINGROCKS2:
		Modifier: 35
		RequiresCondition: im-dumb == 2
	RangeMultiplier@STOPSHOOTINGROCKS3:
		Modifier: 25
		RequiresCondition: im-dumb == 3
	Encyclopedia:
		Category: Nod/Naval

ISUB:
	Inherits: ^Submarine
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildAtProductionType: Submarine
		BuildPaletteOrder: 30
		Prerequisites: ~spen.nod, tmpl, ~techlevel.high
		Description: Submerged unit armed with extreme long-range missiles.
	TooltipExtras:
		Strengths: • Strong vs Buildings, Ground Units
		Weaknesses: • Weak vs Naval Units\n• Cannot attack Aircraft
		Attributes: • Missiles can be shot down by static anti-air defenses\n• Can detect other submarines
	Valued:
		Cost: 2000
	Tooltip:
		Name: ICBM Submarine
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 35000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 16
		Speed: 31
		PauseOnCondition: empdisable || being-warped || launching
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Armament@primary:
		Weapon: ICBMLauncher
	MissileSpawnerMaster:
		Actors: ICBM
		RespawnTicks: 274
		LaunchingCondition: launching
		RequiresCondition: !empdisable && !being-warped
		SpawnOffset: 800,0,400
	WithSpawnerMasterPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	AttackFrontalCharged:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		PauseOnCondition: under-bridge || empdisable || being-warped || blinded
		FacingTolerance: 16
		ChargeLevel: 50
		DischargeRate: 5
		ShowSelectionBar: true
		SelectionBarColor: ffaa00
		ChargingCondition: cloak-force-disabled
	AutoTarget:
		InitialStance: HoldFire
	Selectable:
		DecorationBounds: 1877, 1877
	Cloak:
		PauseOnCondition: launching || cloak-force-disabled || invisibility || being-warped || crossing-ford
		CloakDelay: 100
	RenderRangeCircle@Attack:
		RangeCircleType: ISUBRange
		Color: ffdd0060
	Encyclopedia:
		Category: Nod/Naval

ICBM:
	Inherits: ^ShootableMissile
	RenderSprites:
		Image: ICBM
		Palette: temptd
	Valued:
		Cost: 50
	Tooltip:
		Name: ICBM
	Health:
		HP: 7500
	BallisticMissile:
		LaunchAngle: 160
		Speed: 140
		MinAirborneAltitude: 256
		AirborneCondition: airborne
	LeavesTrailsCA:
		Image: smokey2
		Palette: tseffect-ignore-lighting-alpha75
		MovingInterval: 2
		Type: CenterPosition
		Offsets: -200, 0, 0
	MissileSpawnerSlave:
	SpawnedExplodes:
		Weapon: HonestJohnSub
		Type: Footprint
		RequiresCondition: !airborne
	FireWarheadsOnDeath:
		Weapon: V3ExplodeAirborne
		RequiresCondition: airborne
	FireProjectilesOnDeath@Debris:
		Weapons: SmallDebris
		Pieces: 3, 5
		Range: 1c511, 3c0
		RequiresCondition: airborne

CV:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildPaletteOrder: 60
		Prerequisites: ~syrd.gdi, gtek, ~techlevel.high
		Description: Carrier that launches a squadron of Hornet drone aircraft.
	TooltipExtras:
		Strengths: • Strong vs Ground Units, Buildings
		Weaknesses: • Cannot attack Aircraft
		Attributes: • Hornets only targetable by static anti-air defenses
	Valued:
		Cost: 2000
	Tooltip:
		Name: Drone Carrier
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 70000
	Armor:
		Type: Heavy
	RevealsShroud:
		MinRange: 5c0
		Range: 8c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Mobile:
		TurnSpeed: 16
		Speed: 31
		Voice: Move
		ImmovableCondition: drone-landing
	WithIdleOverlay@SPINNER:
		Sequence: spinner
		Offset: 30,70,600
		PauseOnCondition: empdisable || being-warped
		RequiresCondition: !under-bridge
	AttackFrontal:
		Voice: Attack
		TargetFrozenActors: True
		FacingTolerance: 512
		PauseOnCondition: empdisable || being-warped || blinded
	CarrierMaster:
		Actors: horn, horn, horn
		RearmTicks: 75
		RespawnTicks: 500
		InstantRepair: true
		SlaveDisposalOnKill: KillSlaves
		SpawnAllAtOnce: true
		RequiresCondition: !empdisable && !being-warped
		BeingEnteredCondition: drone-landing
		RearmAsGroup: true
		MaxSlaveDistance: 20c0
	WithSpawnerMasterPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
	Exit:
	Armament:
		Weapon: HornetLauncher
		PauseOnCondition: under-bridge
	Selectable:
		DecorationBounds: 1877, 1877
	Voiced:
		VoiceSet: DroneCarrVoice
	RenderRangeCircle@Attack:
		RangeCircleType: CarrierRange
		Color: 0000FF60
	ProductionCostMultiplier@arcBonus:
		Multiplier: 90
		Prerequisites: player.arc
	Encyclopedia:
		Category: GDI/Naval

SB:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetAllNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildPaletteOrder: 20
		IconPalette: chrometd
		Prerequisites: ~spen.nod, ~techlevel.low
		Description: Fast scout boat, armed with rockets.
	TooltipExtras:
		Strengths: • Strong vs Naval Units, Ground Vehicles, Aircraft
		Weaknesses: • Weak vs Infantry
	Valued:
		Cost: 500
	Tooltip:
		Name: Recon Boat
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 11000
	Armor:
		Type: Light
	Mobile:
		TurnSpeed: 40
		Speed: 113
	RevealsShroud:
		MinRange: 4c0
		Range: 5c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 4c0
	Armament@PRIMARY:
		Weapon: SBRockets
		LocalOffset: -128, -170, 170, -128, 170, 170
		LocalYaw: 100, -100
		PauseOnCondition: under-bridge
	Armament@SECONDARY:
		Name: secondary
		Weapon: BikeRocketsAA
		LocalOffset: -128, -170, 170, -128, 170, 170
		LocalYaw: 100, -100
		PauseOnCondition: under-bridge
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	WithMuzzleOverlay:
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 2c0
	RenderDetectionCircle:
		Color: 00ff0020
		BorderColor: 00000020
	Selectable:
		DecorationBounds: 1536, 1536
	Encyclopedia:
		Category: Nod/Naval

SEAS:
	Inherits: ^Ship
	Inherits@AUTOTARGET: ^AutoTargetAllNavalAssaultMove
	Inherits@BERSERK: ^Berserk
	Buildable:
		Queue: ShipSQ, ShipMQ
		BuildPaletteOrder: 20
		Prerequisites: ~spen, ~techlevel.low
		Description: Light scout & support ship.
	TooltipExtras:
		Strengths: • Strong vs Aircraft, Infantry
		Weaknesses: • Weak vs Naval Units, Vehicles
		Attributes: • Can detect submarines
	Valued:
		Cost: 600
	Tooltip:
		Name: Sea Scorpion
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 28000
	Armor:
		Type: Heavy
	Mobile:
		TurnSpeed: 36
		Speed: 92
	RevealsShroud:
		MinRange: 5c0
		Range: 7c0
		RevealGeneratedShroud: False
	RevealsShroud@GAPGEN:
		Range: 5c0
	Armament@1:
		Weapon: FLAK-SEAS-AG
		LocalOffset: 50,0,376
		MuzzleSequence: muzzle
		PauseOnCondition: under-bridge
	Armament@2AA:
		Name: secondary
		Weapon: FLAK-SEAS-AA
		LocalOffset: 50,0,376
		MuzzleSequence: muzzle
		PauseOnCondition: under-bridge
	AttackFrontal:
		PauseOnCondition: empdisable || being-warped || blinded
		FacingTolerance: 0
	WithMuzzleOverlay:
	DetectCloaked:
		DetectionTypes: Underwater
		Range: 4c0
	RenderDetectionCircle:
		Color: 00ff0020
		BorderColor: 00000020
	Selectable:
		DecorationBounds: 1536, 1536
	Encyclopedia:
		Category: Soviets/Naval
