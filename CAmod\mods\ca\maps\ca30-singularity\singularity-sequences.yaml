wormholexxl:
	idle:
		Filename: wormholexxl.shp
		Length: 13
		ZOffset: 1023
		BlendMode: Additive
	die:
		Filename: wormholexxl.shp
		Length: 13
		ZOffset: 1023
		BlendMode: Additive
	make:
		Filename: wormholexxl.shp
		Length: 13
		ZOffset: 1023
		BlendMode: Additive

wormhole:
	idle:
		Filename: wormhole.shp
		ZOffset: 9000
	die:
		Filename: wormhole.shp
		ZOffset: 9000
	make:
		Filename: wormhole.shp
		ZOffset: 9000

enrvbolthit:
	idle2:
		Filename: enrvbolthit.shp
		BlendMode: Additive
		Length: *
		ZOffset: 2047
		FlipX: true

bluebuff:
	idle:
		Filename: bluebuff.shp
		Length: *
		ZOffset: 1023
		Tick: 80
		Alpha: 0.8

kane:
	Inherits: ^CommonDeaths
	stand:
		Filename: kane.shp
		Facings: 8
	run:
		Filename: kane.shp
		Start: 8
		Length: 6
		Facings: 8
		Tick: 100
	parachute:
		Filename: kane.shp
		Start: 3
	shoot:
		Filename: yuria.shp
		Length: 2
		Tick: 140
		Facings: 8
	die1:
		Filename: kane.shp
		Start: 139
		Length: 8
	die2:
		Filename: kane.shp
		Start: 147
		Length: 8
	die3:
		Filename: kane.shp
		Start: 155
		Length: 8
	die4:
		Filename: kane.shp
		Start: 163
		Length: 12
	die5:
		Filename: kane.shp
		Start: 175
		Length: 18
	die7:
		Filename: kane.shp
		Start: 139
		Length: 8
	chrono-overlay:
		Filename: chronofade_small.shp
		Length: *
		BlendMode: Additive
		Offset: 0, 0
		ZOffset: 512
		Tick: 80
	mind-overlay:
		Filename: mindanim.shp
		Length: *
		Tick: 200
		BlendMode: Alpha
		Offset: 0, 0
